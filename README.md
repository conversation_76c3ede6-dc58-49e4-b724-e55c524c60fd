# Spring Boot Demo Application

A well-structured Spring Boot application demonstrating best practices in project organization and REST API development.

## Project Structure

```
src/main/java/com/example/demo/
├── config/          # Configuration classes
├── controller/      # REST controllers
├── services/         # Business logic interfaces and implementations
├── exceptionHandler/       # Custom exceptions and error handling
├── repositories/           # databse JPA repositories 
└── DemoApplication.java
```

## Features

- Layered architecture (Controller, Service, etc.)
- Global exception handling
- CORS configuration
- Structured logging
- RESTful API endpoints

## API Endpoints

- GET /customers - Returns All Customers From database
- GET //customer/{customerId} - Returns specific custoemr

## Getting Started

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Running the Application

1. Clone the repository
2. Navigate to the project directory
3. Run: `mvn spring-boot:run`

The application will start on http://localhost:8080/api

## Development

### Building the Project

```bash
mvn clean install
```
### Debug the Project
Set to the Intellij environment variables
PROFILE=local;LOGGING=INFO;oauth2.enabled=false
### Running Tests

```bash
mvn test
```
