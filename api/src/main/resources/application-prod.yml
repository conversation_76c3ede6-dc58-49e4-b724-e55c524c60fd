# Server Configuration
server:
  port: 8080

# CBOJ Custom Properties
# OAuth2 Security Configuration
cboj:
  security:
    oauth2:
      enabled: ${oauth2.enabled}


swagger:
  server-url: https://istio.apps.prod-ms.capitalbank.jo/accounts
spring:

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://ssoprodcboj.capitalbank.jo:8443/auth/realms/cboj-mai

http-lib:
  tls:
    keystore:
      path: ""
      password: ""
