# Server Configuration
server:
  port: 8080


spring:
  profiles:
    active: ${PROFILE}

  application:
    name: accounts-core-adapter
  # Database Configuration
  jpa:
    database-platform: org.hibernate.dialect.SQLServerDialect
    show-sql: true
    #hibernate:
    #ddl-auto: validate
    properties:
      hibernate:
        format_sql: true

  # Flyway Configuration
  flyway:
    enabled: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    validate-on-migrate: true
  # Jackson Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_null
    time-zone: UTC
  #  main: [<PERSON>] we need to make sure if we want to enable it. maybe will be needed with libraries approach
  #    allow-bean-definition-overriding: true
  web:
    resources:
      add-mappings: false

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true


springdoc:
  api-docs:
    path: /accounts/api-docs
  swagger-ui:
    config-url: /accounts/api-docs/swagger-config
    url: /accounts/api-docs

# Logging Configuration
logging:
  level:
    root: ${LOGGING}
  structured.format.console: logstash


ibm:
  mq:
    host: ${t24.host}
    port: ${t24.port}
    queueManager: ${t24.queueManager}
    channel: ${t24.appchannel}
    enquiry:
      queueRequest: CMNCHL.T24.INQ.RQ
      queueResponse: CMNCHL.T24.INQ.RS
    transaction:
      queueRequest: CMNCHL.T24.TRX.RQ
      queueResponse: CMNCHL.T24.TRX.RS


acctstmt:
  backend:
    host: ${t24.soap.host} #**********
    protocol: ${t24.soap.protocol} #http
    port: ${t24.soap.port} #9091
    path: ${t24.soap.path} #"/CBOJSTMTV3/services"
    soapAction: ""

t24Interfaces:
  billerAaccount:
    version: "CBOJ.ESB.BILLER"
  safeDepositBox:
    version: "CBOJ.GET.SDB.DETAILS"
  postingRestriction:
    version: "CBOJ.ACC.POST.RES.MW"
  accountArrangements:
    version: "CBOJ.GET.LINKED.ACCOUNTS"
  accountInterest:
    version: "E.CBOJ.LOS.GET.ACCOUNT.INTEREST"
  accountDetails:
    version: "CBOJ.MB.GET.ACCOUNTS.DETS"
  accountDetailsBpmchlOther:
    version: "CBOJ.NOFILE.ACCOUNT.DET"
  accountDetailsBpmchl:
    version: "CBOJ.GET.BPM.DETAIL"
  accountTransactionsList:
    version: "CBOJ.NOFILE.ESB.SRCH.TRAN.IND"
    version2: "CBOJ.GET.STATEMENT.DETAILS"
  accountDepositValid:
    version: "CBOJ.NOFILE.CUS.ACC"
  accountReservationAmounts:
    version: "CBOJ.ESB.KOFAX.LOCKED.AMT"
  accountByLimit:
    version: "CBOJ.LIM.TXN"
  dormentAccount:
    version: "CBOJ.ESB.KOFAX.DOR.AC.HO"
  threshold:
    version: "CBOJ.NOFILE.ALLOW.PRD.AMT"
  outstandingBills:
    version: "CBOJ.ACCOUNTS.WITH.OS.BILLS"
  outstandingBillsOverDrawn:
    version: "CBOJ.GET.OD.ACCOUNTS.WITH.OS.BILLS"
  outstandingBillDetailsKofax:
    version: "CBOJ.ESB.KOFAX.OS.BILLS"
  accountEstatementList:
    operation: "CBOJ2.CCM.ACCT.STMT"
    version: "ONLINE"
  updateNickname:
    version: "AMEND.NICKNAME"
    operation: "ACCOUNT"
  accountCreationRequestGeneric:
    version: "CBOJ.FTI.INT.NEW"
    operation: "ACCOUNT"
  accountCreationOnboarding:
    version: "CBOJ.DIGITAL.ONBOARD"
    operation: "AA.ARRANGEMENT.ACTIVITY"
  updateAccountInternal:
    version: "CBOJ.FTI.INT.NEW"
    operation: "ACCOUNT"
  updateAccountArrangement:
    version: "CBOJ.KOFAX"
    operation: "AA.ARRANGEMENT.ACTIVITY"
  accountCardCreation:
    version: "CBOJ.LOS.CC.NEW.ACCOUNT"
    operation: "AA.ARRANGEMENT.ACTIVITY"
  safeDepositBoxAccountClosure:
    version: "ESB.INPUT"
    operation: "ACCOUNT.CLOSURE"
  settlementAccount:
    operation: "AA.ARRANGEMENT.ACTIVITY"
    version: "CBOJ.UPDATE.SETTLEMENT"
  accountForCredit:
    operation: "AA.ARRANGEMENT.ACTIVITY"
    version: "CBOJ.LOS.CC.NEW.ACCOUNT"










errorlist:
  errors:
    - "TownCountry Missing in Customer TownCountry Missing in Customer|T2426199"
    - "Street Missing in Customer Street Missing in Customer|T2446199"
    - "BuildingNo Missing in Customer BuildingNo Missing in Customer|T2456199"
    - "Missing in Customer MX Fields Missing in Customer|T2457199"
    - "is a Mandatory Input is a Mandatory Input|T2458199"
    - "ALT KEY ALREADY ASSIGNED ALT KEY ALREADY ASSIGNED TO|T2458198"
    - "Customer age less than 18 , not eligible Customer age less than 18 , not eligible|T2450598"
    - "INPUT MISSING|T24001624"
    - "Amount/Fees cannot be in negative. Amount/Fees cannot be in negative.|T2450011"
    - "ACCOUNT CLOSED/MISSING EGP1356011110001 ACCOUNT CLOSED/MISSING|T24003838"
    - "Invalid Amount/Amount not divisible by 10 Invalid Amount/Amount not divisible
    by 10|T24100005"
    - "Account Currency should be JOD Account Currency should be JOD|T24100006"
    - "Current and Saving Account Allow Current and Saving Account Allow|T24100007"
    - "No Debit for Posting Restrict No Debit for Posting Restrict|T24100008"
    - "Daily Limit Exceeded Daily Limit Exceeded|T24100009"
    - "Request is In-Correct with Data Request is In-Correct with Data|T24003840"
    - "INACTIVE CUSTOMER INACTIVE CUSTOMER|T24150039"
    - "Not Eligible to open Account Not Eligible to open Account - Posting Restrict
    Flag|T24001938"
    - "NOT AUTH. RECORD NOT CHANGED NOT AUTH. RECORD NOT CHANGED|T2440001"
    - "Posting Restriction 2 on Credit Account Customer 2034946 Posting Restriction
    2 on Credit Account Customer|T24000935"
    - "ACCOUNT has Debit Posting Restriction ACCOUNT has Debit Posting Restriction|T24003820"
    - "Transaction Currency Mandatory Transaction Currency Mandatory|T24003822"
    - "Account Inactive Account Inactive|T24003827"
    - "MISSING VERSION-RECORD MISSING VERSION-RECORD|T24003828"
    - "VALIDATION ERROR - REJECTED VALIDATION ERROR - REJECTED|T24003829"
    - "TD Amount Greater than Threshold Value TD Amount Greater than Threshold Value|T2400245"
    - "MISSING CUSTOMER.DEFAULT - RECORD MISSING CUSTOMER.DEFAULT - RECORD|T240475"
    - "failed to create deposit failed to create deposit|T2450071"
    - "Below minimum value (5000) minimum Amount (5000)|T249594"
    - "Invalid TD Reference Invalid TD Reference|T249595"
    - "Insufficient Balance in Payer Debit/Fees Account Insufficient Balance in Payer
    Debit/Fees Account|T2400325"
    - "Posting Restriction 13 on Payer Debit/Fees Account Posting Restriction 13 on
    Payer Debit/Fees Account|********"
    - "exceeded number of retries exceeded number of retries|*********"
    - "OTP Expired OTP Expired|*********"
    - "Invalid Otp Invalid OTP|*********"
    - "Exceed Daily Limit Exceed Daily Limit|*********"
    - "INSUFFICIENT BALANCE INSUFFICIENT BALANCE|*********"
    - "INPUT NOT NUMERIC Amount is not numeric|*********"
    - "OTP expired OTP expired|*********"
    - "CardLes withdrawal Transaction amount range 10 JOD to 250 JOD per txn CardLes
    withdrawal Transaction amount range 10 JOD to 250 JOD per txn|*********"
    - "Invalid Amount Invalid Amount|*********"
    - "Invalid Mobile Number Invalid Mobile Number|*********"
    - "MISSING EB.CBOJ.EYEPAY.PAYERS - RECORD INVALID PAYERS|********"
    - "MISSING EB.CBOJ.EYEPAY.AGENTS - RECORD INVALID AGENTS|********"
    - "MISSING EB.CBOJ.EYEPAY.IRIS - RECORD INVALID Provider Code|********"
    - "ACCOUNT CLOSED ACCOUNT CLOSED|********"
    - "ACCOUNT CLOSED/MISSING EGP ACCOUNT CLOSED/MISSING EGP|*********"
    - "Insufficient Balance Insufficient Balance|*********"
    - "Currently Status is active of same customer Currently Status is active of same
    customer|*********"
    - "Currently Status is active/please withdrawal or change mobile no Currently Status
    is active/please withdrawal or change mobile no|*********"
    - "HISTORY RECORD MISSING HISTORY RECORD MISSING|********"
    - "Account Number Input Missing Account Number Input Missing|*********"
    - "Transaction Amount Mandatory Transaction Amount Mandatory|T24003824"
    - "ACCOUNT CLOSED/MISSING ACCOUNT CLOSED/MISSING|T24003825"
    - "Local Equivalent was not Zero Local Equivalent was not Zero|T24003826"
    - "FT-OMNI.SELECT.POST.REST Posting Resriction for Retail Customer|T2450039"
    - "Customer Target is not Capital Select Customer Target is not Capital Select|T24000128"
    - "Invalid TD Reference|T24000523"
    - "Customer Target is not Regular Customer Target is not Regular|T24000253"
    - "ACCOUNT.INACTIVE ACCOUNT.INACTIVE|T24003818"
    - "ACCT.WITH.BANK should be present|T2450037"
    - "MISSING REC IN FILE FBNK.MNEMONIC.ACCOUNT MISSING REC IN FILE|T24003284"
    - "Customer is CBJ Blacklisted Customer is CBJ Blacklisted|T2450050"
    - "Employee not allowed to request cheque over omni Employee not allowed to request
    cheque over omni|T2450051"
    - "Debit Customer is Joint Debit Customer is Joint|T2450052"
    - "Your Cheque Request is rejected. Please contact BM/RM Your Cheque Request is
    rejected. Please contact BM/RM|T2454258"
    - "Invalid IBAN Entered Invalid IBAN Entered|T24001924"
    - "Recomendation Rejected.Submit Request To BM/RM Recomendation Rejected.Submit
    Request To BM/RM|T24000276"
    - "INPUT MISSING Input data not found|T24003888"
    - "MISSING ACCOUNT - RECORD MISSING ACCOUNT RECORD|T24003889"
    - "NO NEW RECORD (ALREADY STORED IN HISTORY FILE) Record already exist|T24009546"
    - "CREDIT ACCT CCY NOT EQ CREDIT CCY Credit Account Currency is not matching with
    Credit Currency|T24007546"
    - "MUST BE NOSTRO/VOSTRO ACCOUNT Account must be NOSTRO/VOSTRO|T24001777"
    - "LIMIT EXCESS*OMNI LIMIT EXCESS*OMNI|T24001629"
    - "RECORD IS LOCKED BY USER RECORD IS LOCKED BY USER|T2450048"
    - "Your Transaction Amount is greater than Available Balance Your Transaction Amount
    is greater than Available Balance|T2450049"
    - "@ID - MANDATORY INPUT ID is mandatory|T24004745"
    - "RECORD IS LOCKED BY USER|T24501002"
    - "Submit Request To BM Rejected.Submit Request To BM/RM|T24000127"
    - "HOLD - OVERRIDE Ac HOLD - OVERRIDE Ac|T24501003"
    - "No Loans Found No Loans Found|T24501004"
    - "Arrangement status - Dormant, you dont have privilege Arrangement status -
    Dormant, you dont have privilege|T24003819"
    - "Cannot trigger another activity Cannot trigger another activity|T24000934"
    - "Settlemet Currency NE TD Currency Settlemet Currency NE TD Currency|T24001928"
    - "Ordering Account Customer is Mandatory Ordering Account Customer is Mandatory|T2450041"
    - "UNAUTH. RECORD MISSING Record not found|T2400429"
    - "******** T24 Unkonw Error|T24 Unkonw Error"
    - "INPUT MISSING Invalid account number|T24003801"
    - "DUPLICATE BENEFICIARY DUPLICATE BENEFICIARY|T24003795"
    - "& & ACCOUNT & & ACCOUNT|T24000001"
    - "& already exists in DM.APPLN.GRP & already exists in DM.APPLN.GRP|T24000002"
    - "& CANNOT CREATE DEFAULT & CANNOT CREATE DEFAULT|T24000003"
    - "& CANNOT OPEN SUSP. ACCT & CANNOT OPEN SUSP. ACCT|T24000004"
    - "& characters allowed for this line & characters allowed for this line|T24000005"
    - "& COMPANY CODE & COMPANY CODE|T24000006"
    - "& CONDITION & FOR & PROPERTY MISSING & CONDITION & FOR & PROPERTY MISSING|T24000007"
    - "& CONDITION OF & HAS INCOMPATIBLE TARGET.PRODUCT ON & & CONDITION OF & HAS INCOMPATIBLE
    TARGET.PRODUCT ON &|T24000008"
    - "& CUS. COMPANY & CUS. COMPANY|T24000009"
    - "& Defined as NON-NEGOTIABLE,can not delete mv/sv set. & Defined as NON-NEGOTIABLE,can
    not delete mv/sv set.|T24000010"
    - "& DEPARTMENT & DEPARTMENT|T24000011"
    - "& EXISTS ON UNAUTHORISED FILE & EXISTS ON UNAUTHORISED FILE|T24000012"
    - "& expired on & & expired on &|T24000013"
    - "& is not property defined in Product & is not property defined in Product|T24000030"
    - "& is not property Interest or Charge type & is not property Interest or Charge
    type|T24000031"
    - "& is not published & is not published|T24000032"
    - "& Is Not Valid For & & Is Not Valid For &|T24000033"
    - "& is not Valid MC.FIELD to map to & is not Valid MC.FIELD to map to|T24000034"
    - "& is now set to INTEREST method & is now set to INTEREST method|T24000035"
    - "& MISSING FROM & & MISSING FROM &|T24000036"
    - "& MISSING IN COMPANY & & MISSING IN COMPANY &|T24000037"
    - "& MISSING IN FILE & & MISSING IN FILE &|T24000038"
    - "& MISSING REC IN FILE & & MISSING REC IN FILE &|T24000039"
    - "& MUST OCCUPY OWN LINE & MUST OCCUPY OWN LINE|T24000040"
    - "& NOT ALLOWED FOR BALANCE TYPE & & NOT ALLOWED FOR BALANCE TYPE &|T24000041"
    - "& not allowed for Product Lines without Interest Property Class & not allowed
    for Product Lines without Interest Property Class|T24000042"
    - "& NOT DEFINED ON DE.PARM & NOT DEFINED ON DE.PARM|T24000043"
    - "& NOT FOUND ! & NOT FOUND !|T24000044"
    - "& NOT FOUND IN DE.I.HEADER & NOT FOUND IN DE.I.HEADER|T24000045"
    - "& NOT FOUND IN DE.SWIFT.ADDRESS & NOT FOUND IN DE.SWIFT.ADDRESS|T24000046"
    - "& NOT FOUND ON DE.MESSAGE & NOT FOUND ON DE.MESSAGE|T24000047"
    - "& NOT ON BASIC INTEREST TABLE & NOT ON BASIC INTEREST TABLE|T24000048"
    - "& NOT ON DE.CARRIER & NOT ON DE.CARRIER|T24000049"
    - "& NOT RECEIVED & NOT RECEIVED|T24000050"
    - "& Property is not a valid Property for this field & Property is not a valid
    Property for this field|T24000051"
    - "& RECORD MISSING IN ACCOUNT FILE & RECORD MISSING IN ACCOUNT FILE|T24000052"
    - "& SET TO BOTH & SET TO BOTH|T24000053"
    - "& TRANS REF & TRANS REF|T24000054"
    - "&-& CONDITION & FOR & PROPERTY MISSING &-& CONDITION & FOR & PROPERTY MISSING|T24000055"
    - "&: CONVERSION DIFF.|T24000056"
    - ". NOT ALLOWED . NOT ALLOWED|T24000058"
    - "MUST BE PREFIXED WITH ROUTINE @ MUST BE PREFIXED WITH ROUTINE|T24000059"
    - "+ MISSING + MISSING|T24000060"
    - "+ OR - MISSING + OR - MISSING|T24000061"
    - "4 #-CHAR. ARE MINIMUM 4 #-CHAR. ARE MINIMUM|T24000062"
    - "999 NOT ALLOWED 999 NOT ALLOWED|T24000063"
    - "A DEBIT OR CREDIT ACCOUNT IS REQUIRED A DEBIT OR CREDIT ACCOUNT IS REQUIRED|T24000064"
    - "A DOT MUST BE USED AS IN 1USD.******** A DOT MUST BE USED AS IN 1USD.********|T24000065"
    - "A maximum of 6 information fields are allowed A maximum of 6 information fields
    are allowed|T24000066"
    - "A Rented Box Can not be Chnaged to & A Rented Box Can not be Chnaged to &|T24000067"
    - "A statement entry id must be captured A statement entry id must be captured|T24000068"
    - "A STATEMENT HAS BEEN PRODUCED SINCE INPUT FUNCTION A STATEMENT HAS BEEN PRODUCED
    SINCE INPUT FUNCTION|T24000069"
    - "A/C number is not for the same Customer A/C number is not for the same Customer|T24000070"
    - "A4 Basis Not Allowed For Last Day Inclusive A4 Basis Not Allowed For Last Day
    Inclusive|T24000071"
    - "AA ACCOUNT ALLOWED ONLY FOR AC.BALANCE.TYPE AA ACCOUNT ALLOWED ONLY FOR AC.BALANCE.TYPE|T24000072"
    - "AA or AZ accounts not allowed. AA or AZ accounts not allowed.|T24000073"
    - "AA.PRODUCT RECORD MISSING AA.PRODUCT RECORD MISSING|T24000074"
    - "AC API update not present AC API update not present|T24000075"
    - "Ac Debit Rule - FULL valid only for ACCOUNTS Ac Debit Rule - FULL valid only
    for ACCOUNTS|T24000076"
    - "AC.DB.RULE is mandatory for given Payment Type. AC.DB.RULE is mandatory for
    given Payment Type.|T24000077"
    - "AC.LINK.ACCT.CLOS & AC.LINK.ACCT.CLOS &|T24000078"
    - "AC.OFFORY NOT NUMERIC AC.OFFORY NOT NUMERIC|T24000079"
    - "AC.PENDING Recs Exist. Online Closure Not Allowed AC.PENDING Recs Exist. Online
    Closure Not Allowed|T24000080"
    - "ACC Balance type not allowed for FIXED type interest ACC Balance type not allowed
    for FIXED type interest|T24000081"
    - "Account access is blocked. Please contact bank Account access is blocked. Please
    contact bank|T24000083"
    - "Account access is blocked.Contact Bank (E-135660) Account access is blocked.Contact
    Bank (E-135660)|T24000084"
    - "ACCOUNT IS ALREADY A PART OF GROUP ID ACCOUNT IS ALREADY A PART OF GROUP ID|T24000105"
    - "ACCOUNT IS ALREADY COMPENSATED ACCOUNT IS ALREADY COMPENSATED|T24000106"
    - "ACCOUNT IS ALREADY LINKED TO AN ACCOUNT SWEEP ACCOUNT IS ALREADY LINKED TO AN
    ACCOUNT SWEEP|T24000107"
    - "ACCOUNT IS AN INTEREST COMPENSATION ACCOUNT ACCOUNT IS AN INTEREST COMPENSATION
    ACCOUNT|T24000108"
    - "ALREADY DEFINED IN ACCT.CATEGORY ALREADY DEFINED IN ACCT.CATEGORY|T24000286"
    - "Already Exist in & Already Exist in &|T24000287"
    - "ACCOUNT IS AN INTEREST LIQUIDATION ACCOUNT ACCOUNT IS AN INTEREST LIQUIDATION
    ACCOUNT|T24000109"
    - "ACCOUNT IS HAVING SOME UNPRINTED ENTRIES ACCOUNT IS HAVING SOME UNPRINTED ENTRIES|T24000110"
    - "ACCOUNT IS IN SUSPENSE ACCOUNT IS IN SUSPENSE|T24000111"
    - "ACCOUNT IS LINKED TO ARRANGEMENT ACCOUNT IS LINKED TO ARRANGEMENT|T24000112"
    - "Account is linked to Debit Account in CUSTOMER.CHARGE record with ID=& Account
    is linked to Debit Account in CUSTOMER.CHARGE record with ID=&|T24000113"
    - "Account is not a Master Account Account is not a Master Account|T24000114"
    - "Account is not for the customer entered Account is not for the customer entered|T24000115"
    - "ACCOUNT IS SET FOR CLOSURE ACCOUNT IS SET FOR CLOSURE|T24000116"
    - "ACCOUNT LINKED TO PD - CHANGE NOT ALLOWED ACCOUNT LINKED TO PD - CHANGE NOT
    ALLOWED|T24000117"
    - "ACCOUNT LINKED TO PD - SINGLE LIMIT SHOULD BE Y ACCOUNT LINKED TO PD - SINGLE
    LIMIT SHOULD BE Y|T24000118"
    - "ACCOUNT MANDATORY FOR CALC TYPE ACCOUNT MANDATORY FOR CALC TYPE|T24000119"
    - "ACCOUNT MISSING ACCOUNT MISSING|T24000120"
    - "ACCOUNT NOT AN ARRANGEMENT ACCOUNT NOT AN ARRANGEMENT|T24000121"
    - "Account not defined in your portfolio (E-135311) Account not defined in your
    portfolio (E-135311)|T24000122"
    - "ACCOUNT NOT FOR SAME CURRENCY ACCOUNT NOT FOR SAME CURRENCY|T24000123"
    - "ACCOUNT NOT FOUND OR NOT SELECTED REC NOT PROCESSED ACCOUNT NOT FOUND OR NOT
    SELECTED REC NOT PROCESSED|T24000125"
    - "ACCOUNT not found ACCOUNT not found|T24000124"
    - "ACCOUNT NOT IN THIS COMPANY ACCOUNT NOT IN THIS COMPANY|T24000126"
    - "ACCOUNT NUMBER INVALID ACCOUNT NUMBER INVALID|T24000129"
    - "ACCOUNT OR CATEGORY MUST BE ENTERED ACCOUNT OR CATEGORY MUST BE ENTERED|T24000130"
    - "ACCOUNT PART OF A CASH POOL ACCOUNT PART OF A CASH POOL|T24000131"
    - "ACCOUNT PART OF AN ACCOUNT SWEEP ACCOUNT PART OF AN ACCOUNT SWEEP|T24000132"
    - "ACCOUNT PART OF ANOTHER POOL ACCOUNT PART OF ANOTHER POOL|T24000133"
    - "ACCOUNT PART OF ANOTHER SUB GROUP ACCOUNT PART OF ANOTHER SUB GROUP|T24000134"
    - "Account property is mandatory for Advance rule type Account property is mandatory
    for Advance rule type|T24000135"
    - "Account property only allowed for APPL.TYPE Account property only allowed for
    APPL.TYPE|T24000136"
    - "Account property only allowed for PIA APPL.TYPE Account property only allowed
    for PIA APPL.TYPE|T24000137"
    - "ACCOUNT RECORD DOES NOT EXIST ACCOUNT RECORD DOES NOT EXIST|T24000138"
    - "Account record is closed Account record is closed|T24000139"
    - "ACCOUNT RECORD MISSING & ACCOUNT RECORD MISSING &|T24000141"
    - "ACCOUNT RECORD MISSING ID = & ACCOUNT RECORD MISSING ID = &|T24000142"
    - "ACCOUNT RECORD MISSING ACCOUNT RECORD MISSING|T24000140"
    - "Account Ref is already linked to another Arrangement Account Ref is already
    linked to another Arrangement|T24000143"
    - "Account Reference and LinkAcNumber values cannot be the same. Account Reference
    and LinkAcNumber values cannot be the same.|T24000144"
    - "Account scheduled for closure Account scheduled for closure|T24000145"
    - "Account should be in DD Currency. Account should be in DD Currency.|T24000146"
    - "Account specified is not defined in your portfolio Account specified is not
    defined in your portfolio|T24000147"
    - "ACCOUNT STATEMENT MISSING FOR ACCOUNT ACCOUNT STATEMENT MISSING FOR ACCOUNT|T24000148"
    - "ACCOUNT TYPE NOT NUMERIC ACCOUNT TYPE NOT NUMERIC|T24000149"
    - "ACCOUNT.ACCRUAL OF ACCOUNT.ACCRUAL OF|T24000150"
    - "ACCOUNT.ACCRUAL SET FOR ALL & ACCOUNT.ACCRUAL SET FOR ALL &|T24000151"
    - "ACCOUNT.ACCRUAL SET TO ALL TYPES ACCOUNT.ACCRUAL SET TO ALL TYPES|T24000152"
    - "ACCOUNT.CLASS not found ACCOUNT.CLASS not found|T24000153"
    - "ACCOUNT.CLASS OFFSPINT NOT PRESENT ACCOUNT.CLASS OFFSPINT NOT PRESENT|T24000154"
    - "ACCOUNT.NUMBER MISSING ACCOUNT.NUMBER MISSING|T24000155"
    - "ACCOUNTING DATE MUST BE & ACCOUNTING DATE MUST BE &|T24000156"
    - "ACCOUNTING RULE FOR & MISSING ACCOUNTING RULE FOR & MISSING|T24000157"
    - "ACCOUNTING RULE FOR ACTION & MISSING ACCOUNTING RULE FOR ACTION & MISSING|T24000158"
    - "ACCOUNTS ARE DIFFERENT ACCOUNTS ARE DIFFERENT|T24000159"
    - "Accrual by bills property is not allowed for Advance Accrual by bills property
    is not allowed for Advance|T24000160"
    - "ACCRUAL DUPLICATION ACCRUAL DUPLICATION|T24000161"
    - "Accrual Rule can not be changed Accrual Rule can not be changed|T24000162"
    - "Accrual rule is allowed for Charge property if it is accrue or amort Accrual
    rule is allowed for Charge property if it is accrue or amort|T24000163"
    - "Accrue is allowed only for charge property Accrue is allowed only for charge
    property|T24000164"
    - "Accrue Period should be SCHEDULE for Accrue. Accrue Period should be SCHEDULE
    for Accrue.|T24000165"
    - "ACCRUE.AMORT cannot be left blank ACCRUE.AMORT cannot be left blank|T24000166"
    - "ACCRUE.AMORT must be AMORT ACCRUE.AMORT must be AMORT|T24000167"
    - "Activity & not belongs to PRODUCT. Activity & not belongs to PRODUCT.|T24000171"
    - "ACTIVITY ALREADY DEFINED IN THE RECORD ACTIVITY ALREADY DEFINED IN THE RECORD|T24000172"
    - "Activity and charge mandatory Activity and charge mandatory|T24000173"
    - "Activity can be run in simulation mode only Activity can be run in simulation
    mode only|T24000174"
    - "Activity cannot be reversed Activity cannot be reversed|T24000175"
    - "ACTIVITY CLASS OR ID IS MANDATORY ACTIVITY CLASS OR ID IS MANDATORY|T24000176"
    - "Activity date less than arrangement linked date Activity date less than arrangement
    linked date|T24000177"
    - "Activity does not belong to & Product Line. Activity does not belong to & Product
    Line.|T24000178"
    - "ACTIVITY DOES NOT MATCH WITH RELATED ACTIVITY & ACTIVITY DOES NOT MATCH WITH
    RELATED ACTIVITY &|T24000179"
    - "Activity ID mandatory for Charge Property. Activity ID mandatory for Charge
    Property.|T24000180"
    - "ACTIVITY ID MISSING ACTIVITY ID MISSING|T24000181"
    - "Activity is Mandatory Activity is Mandatory|T24000182"
    - "Activity is not allowed for & payment method Activity is not allowed for & payment
    method|T24000183"
    - "Activity is not allowed with future effective date Activity is not allowed with
    future effective date|T24000184"
    - "Activity is not relevant to the current product line. Activity is not relevant
    to the current product line.|T24000185"
    - "Activity mandatory for Manual and online closure Activity mandatory for Manual
    and online closure|T24000186"
    - "Activity mandatory for Online closure Activity mandatory for Online closure|T24000187"
    - "Activity Name mandatory to check for recalculate type Activity Name mandatory
    to check for recalculate type|T24000188"
    - "Activity not allowed. Arrangement is in closed status Activity not allowed.
    Arrangement is in closed status|T24000189"
    - "Activity not defined for ProductLine & Activity not defined for ProductLine
    &|T24000190"
    - "ACTIVITY NOT RELATED TO PRODUCT LINE ACTIVITY NOT RELATED TO PRODUCT LINE|T24000191"
    - "ACTIVITY NOT UNDER THIS PRODUCT ACTIVITY NOT UNDER THIS PRODUCT|T24000192"
    - "& FROM & IS IN EXPIRED STATUS & FROM & IS IN EXPIRED STATUS|T24000014"
    - "& FROM & IS IN INVALID STATUS & FROM & IS IN INVALID STATUS|T24000015"
    - "& FROM & NOT RECEIVED & FROM & NOT RECEIVED|T24000016"
    - "& HAS BATCH IN USE & HAS BATCH IN USE|T24000019"
    - "& Invalid Type for & & Invalid Type for &|T24000021"
    - "& INVALID & INVALID|T24000020"
    - "& is already locked by another user & is already locked by another user|T24000022"
    - "& is available only from & & is available only from &|T24000023"
    - "& is Mandatory & is Mandatory|T24000024"
    - "& is missing from LOCAL.REF.TABLE & is missing from LOCAL.REF.TABLE|T24000025"
    - "& is NON-CONTINGENT & is NON-CONTINGENT|T24000026"
    - "& is not a property defined in Product & is not a property defined in Product|T24000027"
    - "& is not a valid currency. & is not a valid currency.|T24000028"
    - "& IS NOT A VALID SYSTEM ID & IS NOT A VALID SYSTEM ID|T24000029"
    - "Balance type is mandatory for Advance payment rules Balance type is mandatory
    for Advance payment rules|T24000395"
    - "Actual Profit Amt cannot be less than already realised profit amount Actual
    Profit Amt cannot be less than already realised profit amount|T24000206"
    - "ADDRESS NO. NOT ALLOWED FOR EUCLID ADDRESS NO. NOT ALLOWED FOR EUCLID|T24000210"
    - "ADDRESS.NO MUST BE NUMERIC ADDRESS.NO MUST BE NUMERIC|T24000211"
    - "ADDRESSEE, ALLOW MAXIMUM 6 LINES ADDRESSEE, ALLOW MAXIMUM 6 LINES|T24000212"
    - "ADJ.PROP.AMT is greater than Property Amount ADJ.PROP.AMT is greater than Property
    Amount|T24000213"
    - "Adjusted amount is greater than Bill Amount Adjusted amount is greater than
    Bill Amount|T24000215"
    - "Adjust balance is not allowed for fixed type of interest Adjust balance is not
    allowed for fixed type of interest|T24000214"
    - "ADJUSTMENT ALREADY DONE ADJUSTMENT ALREADY DONE|T24000216"
    - "Adjustment amount Greater than Outstanding Amount Adjustment amount Greater
    than Outstanding Amount|T24000217"
    - "Adjustment Pending! Trigger Retrospect Activity for & Adjustment Pending! Trigger
    Retrospect Activity for &|T24000218"
    - "Advance Disbursement not allowed Advance Disbursement not allowed|T24000219"
    - "Advance payment is allowed only for Lending products Advance payment is allowed
    only for Lending products|T24000220"
    - "Advance payment method mandatory for Advance Rule Advance payment method mandatory
    for Advance Rule|T24000221"
    - "Advance payment not allowed for call type of contracts Advance payment not allowed
    for call type of contracts|T24000222"
    - "Advance Payment Schedule Falls before Cooling Date Advance Payment Schedule
    Falls before Cooling Date|T24000223"
    - "ADVANCE RATIO IS NOT SET ADVANCE RATIO IS NOT SET|T24000224"
    - "Advance type cannot settle & property Advance type cannot settle & property|T24000225"
    - "Advice cannot be input without activities Advice cannot be input without activities|T24000226"
    - "ADVSUSPENSE not allowed for Lending Payout rule, it is allowed only for Deposits
    Payout rule ADVSUSPENSE not allowed for Lending Payout rule, it is allowed only
    for Deposits Payout rule|T24000227"
    - "AGE.ALL.BILLS should be YES when SUSPEND is YES AGE.ALL.BILLS should be YES
    when SUSPEND is YES|T24000228"
    - "AGENCY & STILL LINKED TO ACCOUNT AGENCY & STILL LINKED TO ACCOUNT|T24000229"
    - "Agent arrangement & (&) not authorised for this product Agent arrangement &
    (&) not authorised for this product|T24000230"
    - "Agent Arrangement does not belong to Agent Agent Arrangement does not belong
    to Agent|T24000231"
    - "Agent Arrangement is unauthorised Agent Arrangement is unauthorised|T24000232"
    - "Agent Arrangement not belong to Agent Line Agent Arrangement not belong to Agent
    Line|T24000233"
    - "Agent Event already processed Agent Event already processed|T24000234"
    - "AGREED.CUSTS ALLOWED FOR FT ONLY AGREED.CUSTS ALLOWED FOR FT ONLY|T24000235"
    - "ALL CHAR MUST BE &amp;apos;A...Z&amp;apos; ALL CHAR MUST BE &amp;apos;A...Z&amp;apos;|T24003814"
    - "ALL CHAR MUST BE &apos;A...Z&apos; ALL CHAR MUST BE &amp;apos;A...Z&amp;apos;|T24003813"
    - "ALL CHAR. MUST BE A...Z ALL CHAR. MUST BE A...Z|T24000236"
    - "ALL GROUP CONDITIONS NOT VALID ALL GROUP CONDITIONS NOT VALID|T24000237"
    - "ALL MUST NOT BE MIXED WITH OTHER INT.TYPEs ALL MUST NOT BE MIXED WITH
    OTHER INT.TYPEs|T24000238"
    - "All the progressive payment percentages defined should be same All the progressive
    payment percentages defined should be same|T24000239"
    - "ALL.ACCOUNT IS ALREADY SET ALL.ACCOUNT IS ALREADY SET|T24000240"
    - "Allocation parameter setup does not exist Allocation parameter setup does not
    exist|T24000241"
    - "Allow Commission type only for charge property Allow Commission type only for
    charge property|T24000242"
    - "Allow Credit type only for Charge property Allow Credit type only for Charge
    property|T24000243"
    - "Allow Rebate Unamortised type only for charge property Allow Rebate Unamortised
    type only for charge property|T24000244"
    - "Allowed basis points & - exceeds by & Allowed basis points & - exceeds by &|T24000245"
    - "Allowed Inputs are & Allowed Inputs are &|T24000246"
    - "Allowed lines are Accounts, Deposits, Lending, Safe Deposit box, Rewards or
    Agent Allowed lines are Accounts, Deposits, Lending, Safe Deposit box, Rewards
    or Agent|T24000247"
    - "ALLOWED ONLY FOR 101 AND 102 ALLOWED ONLY FOR 101 AND 102|T24000248"
    - "ALLOWED ONLY FOR 103,202 AND 203 ALLOWED ONLY FOR 103,202 AND 203|T24000249"
    - "ALLOWED ONLY FOR 942 MSG TYPE ALLOWED ONLY FOR 942 MSG TYPE|T24000250"
    - "Allowed only for AC or LD Product Type Allowed only for AC or LD Product Type|T24000251"
    - "Allowed only for Advance rule type Allowed only for Advance rule type|T24000252"
    - "Allowed only for direct accounting activities Allowed only for direct accounting
    activities|T24000254"
    - "Allowed only for INTEREST and CHARGE Property Class Allowed only for INTEREST
    and CHARGE Property Class|T24000255"
    - "Allowed only for NORR and NOREPLAY activities Allowed only for NORR and NOREPLAY
    activities|T24000256"
    - "Allowed only for Numeric and Amount types Allowed only for Numeric and Amount
    types|T24000257"
    - "Allowed only for OTHER Product line Allowed only for OTHER Product line|T24000258"
    - "Allowed only for product Collection Allowed only for product Collection|T24000259"
    - "Allowed only for product Discounting Allowed only for product Discounting|T24000260"
    - "Allowed only for SC Product Type Allowed only for SC Product Type|T24000261"
    - "ALLOWED ONLY FOR VALUE DATED BALANCE ALLOWED ONLY FOR VALUE DATED BALANCE|T24000262"
    - "ALLOWED ONLY IF BACK.VALUE.ADJ IS YES ALLOWED ONLY IF BACK.VALUE.ADJ IS YES|T24000265"
    - "Allowed only if product PR is installed Allowed only if product PR is installed|T24000266"
    - "Allowed only if service availability is OPTIONAL Allowed only if service availability
    is OPTIONAL|T24000267"
    - "ALLOWED ONLY IF SHARED.BALANCES IS NO ALLOWED ONLY IF SHARED.BALANCES IS NO|T24000268"
    - "ALLOWED ONLY IF STANDALONE IS Y ALLOWED ONLY IF STANDALONE IS Y|T24000269"
    - "ALLOWED ONLY WHEN ACCT.SYNC IS SET TO YES ALLOWED ONLY WHEN ACCT.SYNC IS SET
    TO YES|T24000270"
    - "Allowed only when change product in BL.TYPE is allowed Allowed only when change
    product in BL.TYPE is allowed|T24000271"
    - "Allowed only when SUSPEND is set Allowed only when SUSPEND is set|T24000272"
    - "Allowed only with Advance Disbursement Allowed only with Advance Disbursement|T24000273"
    - "ALLOWED ONLY WITH MATURITY INSTR AS ROLLOVER ALLOWED ONLY WITH MATURITY INSTR
    AS ROLLOVER|T24000274"
    - "Allowed only with Partial Recourse Allowed only with Partial Recourse|T24000275"
    - "Allowed only with Retention Margin Allowed only with Retention Margin|T24000277"
    - "Allowed Product Line are ACCOUNT, LENDING and DEPOSITS Allowed Product Line
    are ACCOUNT, LENDING and DEPOSITS|T24000278"
    - "Allowed Property Class INTEREST OR CHARGE Allowed Property Class INTEREST OR
    CHARGE|T24000279"
    - "Allowed tolerance & - Difference & Allowed tolerance &% - Difference &%|T24000280"
    - "Allowed variance & - Actual variance & Allowed variance & - Actual variance
    &|T24000281"
    - "Allowed when RETENTION.MARGIN in BL.TYPE is allowed Allowed when RETENTION.MARGIN
    in BL.TYPE is allowed|T24000282"
    - "ALPHABETIC EXPECTED ALPHABETIC EXPECTED|T24000283"
    - "ALREADY A VALID ACCOUNT NUMBER ALREADY A VALID ACCOUNT NUMBER|T24000284"
    - "ALREADY CUSTOMER ACCOUNT ATTACHED TO LIMIT ALREADY CUSTOMER ACCOUNT ATTACHED
    TO LIMIT|T24000285"
    - "Already Processed - Amendment not allowed. Already Processed - Amendment not
    allowed.|T24000288"
    - "ALREADY SHUTDOWN ALREADY SHUTDOWN|T24000289"
    - "ALREADY SINGLE PAYMENT ALREADY SINGLE PAYMENT|T24000290"
    - "Accelerated payment type not allowed for fixed type of interest Accelerated
    payment type not allowed for fixed type of interest|T24000082"
    - "Account already exists, cannot be reused Account already exists, cannot be reused|T24000085"
    - "Account already in a Cash Pool Account already in a Cash Pool|T24000086"
    - "ACCOUNT AND AMOUNT CURRENCIES ARE DIFFERENT ACCOUNT AND AMOUNT CURRENCIES ARE
    DIFFERENT|T24000087"
    - "Account and Beneficiary are mutually exclusive Account and Beneficiary are mutually
    exclusive|T24000088"
    - "ACCOUNT ATTACHED TO AZ.ACCOUNT, CANNOT CLOSE ACCOUNT ATTACHED TO AZ.ACCOUNT,
    CANNOT CLOSE|T24000089"
    - "Account balance less than Blocked amount. Contact Bank Account balance less
    than Blocked amount. Contact Bank|T24000090"
    - "ACCOUNT CANT BE CLOSED WHEN TILL STATUS IS OPEN ACCOUNT CANT BE CLOSED WHEN
    TILL STATUS IS OPEN|T24000091"
    - "Account Categ does not match with SUB.LOAN.TYPE Categ Account Categ does not
    match with SUB.LOAN.TYPE Categ|T24000092"
    - "Account ccy should not be equal to contract ccy Account ccy should not be equal
    to contract ccy|T24000093"
    - "ACCOUNT CLASS - SUSACONLINE NOT CREATED ACCOUNT CLASS - SUSACONLINE NOT CREATED|T24000094"
    - "Account does not belong to Customer & Account does not belong to Customer &|T24000095"
    - "Account does not belong to your portfolio ( E-135310) Account does not belong
    to your portfolio ( E-135310)|T24000097"
    - "Account does not belong to your portfolio Account does not belong to your portfolio|T24000096"
    - "ACCOUNT DOES NOT EXIST IN THIS COMPANY ACCOUNT DOES NOT EXIST IN THIS COMPANY|T24000098"
    - "Account does not match the selection Account does not match the selection|T24000099"
    - "Account has a Debit Balance Account has a Debit Balance|T24000100"
    - "ACCOUNT HAS ANOTHER CURRENCY ACCOUNT HAS ANOTHER CURRENCY|T24000101"
    - "ACCOUNT ID IS MANDATORY ACCOUNT ID IS MANDATORY|T24000102"
    - "ACCOUNT ID/CATEGORY NOT FOUND IN ER.PARAMETER ACCOUNT ID/CATEGORY NOT FOUND
    IN ER.PARAMETER|T24000103"
    - "ACCOUNT IS A CHARGE ACCOUNT ACCOUNT IS A CHARGE ACCOUNT|T24000104"
    - "AMOUNT <= LAST ONE AMOUNT <= LAST ONE|T24000305"
    - "AMOUNT AND INTEREST CLASS MANDATORY FOR CALC TYPE AMOUNT AND INTEREST CLASS
    MANDATORY FOR CALC TYPE|T24000306"
    - "Amount exceeds Max available amount Amount exceeds Max available amount|T24000307"
    - "AMOUNT FROM MISSING AMOUNT FROM MISSING|T24000308"
    - "Amount in FCY should be NULL for Local currency Account Amount in FCY should
    be NULL for Local currency Account|T24000309"
    - "Amount mandatory when term is present Amount mandatory when term is present|T24000310"
    - "AMOUNT MISSING AMOUNT MISSING|T24000311"
    - "AMOUNT MUST BE FCY OR LCY AMOUNT MUST BE FCY OR LCY|T24000312"
    - "AMOUNT MUST BE GREATER THAN 0 AMOUNT MUST BE GREATER THAN 0|T24000313"
    - "AMOUNT NOT NUMERIC AMOUNT NOT NUMERIC|T24000314"
    - "AMOUNT TO < AMOUNT FROM AMOUNT TO < AMOUNT FROM|T24000315"
    - "Amt should be less than or equal to Available Amt Amt should be less than or
    equal to Available Amt|T24000316"
    - "AMT SHOULD NOT BE LESS THAN UTILISED LIMIT AMT SHOULD NOT BE LESS THAN UTILISED
    LIMIT|T24000317"
    - "An EB.TABLE.DEFINITION record already exit for & An EB.TABLE.DEFINITION record
    already exit for &|T24000318"
    - "ANNUITY AND LINEAR TYPE NOT ALLOWED ANNUITY AND LINEAR TYPE NOT ALLOWED|T24000319"
    - "Annuity should contain fixed interest property till its final schedule Annuity
    should contain fixed interest property till its final schedule|T24000320"
    - "Answer is Invalid Answer is Invalid|T24000321"
    - "ANSWER is Mandatory ANSWER is Mandatory|T24000322"
    - "Answer not Allowed Answer not Allowed|T24000323"
    - "Any 2 among TERM, TERM.AMOUNT or ACTUAL.AMT reqd Any 2 among TERM, TERM.AMOUNT
    or ACTUAL.AMT reqd|T24000324"
    - "App Method Cannot be Capitalise for Agent product line App Method Cannot be
    Capitalise for Agent product line|T24000325"
    - "App Method Cannot be Defer when type is Rebate Unamort App Method Cannot be
    Defer when type is Rebate Unamort|T24000326"
    - "APPLICATION & MISSING APPLICATION & MISSING|T24000327"
    - "Application already recorded in COLLATERAL ID=& Application already recorded
    in COLLATERAL ID=&|T24000328"
    - "APPLICATION FORMAT CODE LENGTH INCORRECT APPLICATION FORMAT CODE LENGTH INCORRECT|T24000329"
    - "APPLICATION FORMAT CODE MUST BE NUMERIC APPLICATION FORMAT CODE MUST BE NUMERIC|T24000330"
    - "APPLICATION SHOULD BE MM.MONEY.MARKET APPLICATION SHOULD BE MM.MONEY.MARKET|T24000333"
    - "APPLICATION TELLER NOT INSTALLED APPLICATION TELLER NOT INSTALLED|T24000334"
    - "Application type must be OLDEST.FIRST Application type must be OLDEST.FIRST|T24000335"
    - "Application Type should be BILL.DATE or BILL.PROPERTY Application Type should
    be BILL.DATE or BILL.PROPERTY|T24000336"
    - "Applicn undefined in DM.APPLICATION.INFO Applicn undefined in DM.APPLICATION.INFO|T24000337"
    - "APPLY DATE TO BE > TODAY APPLY DATE TO BE > TODAY|T24000338"
    - "Arr Start Date (&) after value date (&) for A/c & (&) Arr Start Date (&) after
    value date (&) for A/c & (&)|*********"
    - "ARR.BILL.TYPE Missing ARR.BILL.TYPE Missing|*********"
    - "ARR.BILL.TYPE not specified in BILL.TYPE ARR.BILL.TYPE not specified in BILL.TYPE|*********"
    - "ARRANGEMENT / PRODUCT ID MISSING ARRANGEMENT / PRODUCT ID MISSING|*********"
    - "Arrangement allowed for only Mandatory class Arrangement allowed for only Mandatory
    class|*********"
    - "Arrangement Already Active Arrangement Already Active|*********"
    - "Arrangement already in bundle Arrangement already in bundle|*********"
    - "Arrangement Already InActive Arrangement Already InActive|*********"
    - "Arrangement already redeemed Arrangement already redeemed|*********"
    - "Arrangement Already Suspended Arrangement Already Suspended|*********"
    - "Arrangement does not belong to Customer & Arrangement does not belong to Customer
    &|*********"
    - "ARRANGEMENT DOES NOT BELONGS TO PROD LINE ARRANGEMENT DOES NOT BELONGS TO PROD
    LINE|*********"
    - "Arrangement Dosent Belong to Rewards Product Line Arrangement Dosent Belong
    to Rewards Product Line|*********"
    - "Arrangement ID is not in Product group Arrangement ID is not in Product group|*********"
    - "Arrangement ID is not in Product Arrangement ID is not in Product|*********"
    - "Arrangement id mandatory Arrangement id mandatory|*********"
    - "Arrangement is already eligible for AUTOMATIC review Arrangement is already
    eligible for AUTOMATIC review|*********"
    - "Arrangement is already eligible for MANUAL review Arrangement is already eligible
    for MANUAL review|T24000356"
    - "ARRANGEMENT IS NOT A DEPOSIT ARRANGEMENT IS NOT A DEPOSIT|T24000357"
    - "Arrangement is not authorised Arrangement is not authorised|T24000358"
    - "Arrangement Key Not Allowed at Product Level Arrangement Key Not Allowed at
    Product Level|T24000359"
    - "Arrangement not charged Off, Cust Balance not allowed Arrangement not charged
    Off, Cust Balance not allowed|T24000360"
    - "Arrangement number is mandatory Arrangement number is mandatory|T24000361"
    - "Arrangement of this account is unauthorised. Arrangement of this account is
    unauthorised.|T24000362"
    - "Arrangement status invalid for closure Arrangement status invalid for closure|T24000364"
    - "Arrangement Status Invalid Arrangement Status Invalid|T24000363"
    - "Arrangement status not request closure Arrangement status not request closure|T24000365"
    - "Arrangements created between & and & will be affected. Arrangements created
    between & and & will be affected.|T24000366"
    - "Arrangment is Mandatory when mandatory flag is Yes Arrangment is Mandatory when
    mandatory flag is Yes|T24000367"
    - "ASSET.TYPE ID IS INVALID ASSET.TYPE ID IS INVALID|T24000368"
    - "ASSOC.FLD HAS TO CO-EXIST WITH MULTI.CUST ASSOC.FLD HAS TO CO-EXIST WITH MULTI.CUST|T24000369"
    - "At least one customer must be Beneficial Owner At least one customer must be
    Beneficial Owner|T24000370"
    - "At least one Property must be Mandatory. At least one Property must be Mandatory.|T24000371"
    - "AUTO.PAY.ACCT CANNOT BE ACCOUNT NUMBER AUTO.PAY.ACCT CANNOT BE ACCOUNT NUMBER|T24000382"
    - "Automatic Closing 90-99 Cannot be removed manually. Automatic Closing 90-99
    Cannot be removed manually.|T24000383"
    - "AUTOMATIC ROLLOVER NOT ALLOWED WHEN PD EXISTS AUTOMATIC ROLLOVER NOT ALLOWED
    WHEN PD EXISTS|T24000384"
    - "AVAILABLE AND EXPIRY DATES DO NOT MATCH AVAILABLE AND EXPIRY DATES DO NOT MATCH|T24000385"
    - "Available Balance Update Miss Credit Check Available Balance Update Miss Credit
    Check|T24000386"
    - "AVAILABLE.BAL.UPD MISSING FOR CREDIT.CHECK AVAILABLE.BAL.UPD MISSING FOR CREDIT.CHECK|T24000387"
    - "AX Product is not installed AX Product is not installed|T24000388"
    - "Ayoub ENTER VALID FT PURPOSE CODE INVALID PURPOSE CODE|T24003803"
    - "AZ and AA accounts not allowed for HVT AZ and AA accounts not allowed for HVT|T24000389"
    - "Backvalue Date Exceeded Backvalue Date Exceeded|T24000390"
    - "BAD PARITY 0=NONE 1=EVEN 2=ODD BAD PARITY 0=NONE 1=EVEN 2=ODD|T24000391"
    - "Balance calc type should be of CHARGE property class Balance calc type should
    be of CHARGE property class|T24000392"
    - "Balance type is already exist Balance type is already exist|T24000393"
    - "Balance type is Invalid without Property Balance type is Invalid without Property|T24000394"
    - "Balance type mandatory for periodic attribute Balance type mandatory for periodic
    attribute|T24000396"
    - "ACCRUE.BY.BILLS not allowed for FIXED type of interest ACCRUE.BY.BILLS not allowed
    for FIXED type of interest|T24000168"
    - "ACCT.UNAUTH.OD Please note that your transaction was not processed successfully
    due to insufficient balance|T2450031"
    - "ACK CANNOT BE INPUT ACK CANNOT BE INPUT|T24000169"
    - "ACTIVE CARRIER HAS BEEN REMOVED ACTIVE CARRIER HAS BEEN REMOVED|T24000170"
    - "Activity or Balance Calc type mandatory for CALCULATED Activity or Balance Calc
    type mandatory for CALCULATED|T24000193"
    - "Activity type & could not be removed Activity type & could not be removed|T24000194"
    - "ACTIVITY.CLASS/ACTIVITY PROPERTY/CLASS MANDATORY ACTIVITY.CLASS/ACTIVITY PROPERTY/CLASS
    MANDATORY|T24000195"
    - "ACTIVITY.UPDATE IN AC.BALANCE.TYPE MUST BE Y ACTIVITY.UPDATE IN AC.BALANCE.TYPE
    MUST BE Y|T24000196"
    - "Actual Amount allowed only in 1st sub value position Actual Amount allowed only
    in 1st sub value position|T24000197"
    - "Actual Amount is lesser to recalculate term. Actual Amount is lesser to recalculate
    term.|T24000198"
    - "ACTUAL AMOUNT NOT VALID FOR PAYMENT TYPE ACTUAL AMOUNT NOT VALID FOR PAYMENT
    TYPE|T24000199"
    - "Actual amount shouyld be defined for all multi value for fixed interest Actual
    amount shouyld be defined for all multi value for fixed interest|T24000200"
    - "Actual amt not allowed for fixed type of interest Actual amt not allowed for
    fixed type of interest|T24000201"
    - "Actual and Clear Balances should be equal Actual and Clear Balances should be
    equal|T24000202"
    - "Actual not allowed after constant type for fixed interest Actual not allowed
    after constant type for fixed interest|T24000203"
    - "Actual Profit amount is mandatory if RATE.TYPE is FLAT.AMOUNT Actual Profit
    amount is mandatory if RATE.TYPE is FLAT.AMOUNT|T24000204"
    - "Actual Profit amount set only for Fixed Interest Actual Profit amount set only
    for Fixed Interest|T24000205"
    - "ACTUAL.AMT not allowed for Periodic Charges ACTUAL.AMT not allowed for Periodic
    Charges|T24000207"
    - "ACTUAL.AMT NOT ALLOWED IF CALC.TYPE IS PERCENTAGE ACTUAL.AMT NOT ALLOWED IF
    CALC.TYPE IS PERCENTAGE|T24000208"
    - "ACTUAL.AMT NOT ALLOWED IF CALC.TYPE IS PROGRESSIVE ACTUAL.AMT NOT ALLOWED IF
    CALC.TYPE IS PROGRESSIVE|T24000209"
    - "AUTO PAYMENT CATEGORY NOT YET SPECIFIED AUTO PAYMENT CATEGORY NOT YET SPECIFIED|T24000380"
    - "AUTO.EXPIRY should be set to NO AUTO.EXPIRY should be set to NO|T24000381"
    - "Balance type not allowed if Activity is inputted Balance type not allowed if
    Activity is inputted|T24000397"
    - "BALANCE.INTRNL should be YES BALANCE.INTRNL should be YES|T24000398"
    - "BALANCE.SIGN MISSING BALANCE.SIGN MISSING|T24000399"
    - "BALANCE.TYPE not found BALANCE.TYPE not found|T24000400"
    - "BANK OPERATION CODE MISSING BANK OPERATION CODE MISSING|T24000401"
    - "BANKS OWN SORT CODE BANKS OWN SORT CODE|T24000402"
    - "Base Amount (Interest/Charge) Missing. Base Amount (Interest/Charge) Missing.|T24000403"
    - "BASE CCY must be either BUY or SELL CCY BASE CCY must be either BUY or SELL
    CCY|T24000404"
    - "Base date type cannot be changed Base date type cannot be changed|T24000405"
    - "BASE MESSAGE NOT SET UP BASE MESSAGE NOT SET UP|T24000406"
    - "Base Property & not belongs to Product. Base Property & not belongs to Product.|T24000407"
    - "BASIC OR INT. RATE MISSING BASIC OR INT. RATE MISSING|T24000408"
    - "BATCH ALREADY AUTHORISED BATCH ALREADY AUTHORISED|T24000409"
    - "BATCH ALREADY BALANCES BATCH ALREADY BALANCES|T24000410"
    - "BATCH ALREADY USED BATCH ALREADY USED|T24000411"
    - "BATCH DOES NOT EXIST BATCH DOES NOT EXIST|T24000412"
    - "BATCH FULL BATCH FULL|T24000413"
    - "BATCH HAS NO ITEMS BATCH HAS NO ITEMS|T24000414"
    - "BATCH MISSING BATCH MISSING|T24000415"
    - "BATCH NUMBER EXCEEDS 999 BATCH NUMBER EXCEEDS 999|T24000416"
    - "BILATERAL AGREEMENT NOT EXIST FOR TODAY BILATERAL AGREEMENT NOT EXIST FOR TODAY|T24000427"
    - "BILATERAL AGREEMENT REC NOT FOUND BILATERAL AGREEMENT REC NOT FOUND|T24000428"
    - "BILL AMOUNT IS IN NEGATIVE BILL AMOUNT IS IN NEGATIVE|T24000429"
    - "Bill Currency and Account Currency are same Bill Currency and Account Currency
    are same|T24000430"
    - "Bill date cant be greater than payment date Bill date cant be greater than payment
    date|T24000431"
    - "Bill date cant be less than orig contract date Bill date cant be less than orig
    contract date|T24000432"
    - "BILL EXISTS ALREADY BILL EXISTS ALREADY|T24000433"
    - "Bill Produced Must be same for all Installments Bill Produced Must be same for
    all Installments|T24000434"
    - "Bill Produced not allowed for Advance Payment Type Bill Produced not allowed
    for Advance Payment Type|T24000435"
    - "Bill produced not allowed for Defered payment type Bill produced not allowed
    for Defered payment type|T24000436"
    - "BILL RECORD NOT FOUND BILL RECORD NOT FOUND|T24000437"
    - "Bill Reference can not be null. Bill Reference can not be null.|T24000438"
    - "Bill reference should be NEW or valid bill id Bill reference should be NEW or
    valid bill id|T24000439"
    - "BILL TYPE DISBURSEMENT NOT ALLOWED FOR DEPOSITS PRODUCT BILL TYPE DISBURSEMENT
    NOT ALLOWED FOR DEPOSITS PRODUCT|T24000440"
    - "BILL TYPE EXPECTED NOT ALLOWED FOR LENDING PRODUCT LINE BILL TYPE EXPECTED NOT
    ALLOWED FOR LENDING PRODUCT LINE|T24000441"
    - "Bill-Related balance will not allow for this Activity Bill-Related balance will
    not allow for this Activity|T24000442"
    - "BILLS.COMBINED CANNOT BE YES WHEN CAPITALISE BILLS.COMBINED CANNOT BE YES WHEN
    CAPITALISE|T24000443"
    - "Blackout end time cant be null Blackout end time cant be null|T24000444"
    - "Blackout start time cant be greater than end time Blackout start time cant
    be greater than end time|T24000445"
    - "Blackout start time cant be null Blackout start time cant be null|T24000446"
    - "BLANK FIELD INVALID FOR RAISE.ACTIVITY BLANK FIELD INVALID FOR RAISE.ACTIVITY|T24000448"
    - "Blank field invalid Blank field invalid|T24000447"
    - "BLANK VALUE NOT ALLOWED BLANK VALUE NOT ALLOWED|T24000449"
    - "Block already expired - expiry date less than today Block already expired -
    expiry date less than today|T24000450"
    - "Block priority allowed only for RC and AC Block priority allowed only for RC
    and AC|T24000451"
    - "Block priority is mandatory when retry is set Block priority is mandatory when
    retry is set|T24000452"
    - "Block prority shuld not be given Block prority shuld not be given|T24000453"
    - "Block/Unblock Reason codes cannot be delinked Block/Unblock Reason codes cannot
    be delinked|T24000454"
    - "Blocking code not linked to posting restrict Blocking code not linked to posting
    restrict|T24000455"
    - "BOOKING COMPANY ALLOWED ONLY FOR CHARGE PROPERY CLASS BOOKING COMPANY ALLOWED
    ONLY FOR CHARGE PROPERY CLASS|T24000456"
    - "BOOKING.CM Or AMORT.CATEGORY Either one alowed BOOKING.CM Or AMORT.CATEGORY
    Either one alowed|T24000457"
    - "BOTH  AND ACTUAL AMOUNT CANNOT BE GIVEN BOTH % AND ACTUAL AMOUNT CANNOT BE GIVEN|T24000458"
    - "Both amount and percentage not allowed Both amount and percentage not allowed|T24000459"
    - "Both Balance and Activity calc type not allowed Both Balance and Activity calc
    type not allowed|T24000460"
    - "BOTH BEN BANK AND CUSTOMER NOT ALLOWED BOTH BEN BANK AND CUSTOMER NOT ALLOWED|T24000461"
    - "Both bill and current balances mandatory for Partial Both bill and current
    balances mandatory for Partial|T24000462"
    - "BOTH CAP AND PAY FREQUENCIES SHOULD BE D/W/M BOTH CAP AND PAY FREQUENCIES SHOULD
    BE D/W/M|T24000463"
    - "Both CCY and OPT.CCY cannot be input Both CCY and OPT.CCY cannot be input|T24000464"
    - "Both Commission and Periodic Commission Type not allowed together Both Commission
    and Periodic Commission Type not allowed together|T24000465"
    - "BOTH DATE AND PERIOD CANNOT BE INPUT BOTH DATE AND PERIOD CANNOT BE INPUT|T24000466"
    - "Both Disbursement and Settlement not allowed Both Disbursement and Settlement
    not allowed|T24000467"
    - "BOTH END DATE AND NUM PAYMENTS CANNOT BE DEFINED BOTH END DATE AND NUM PAYMENTS
    CANNOT BE DEFINED|T24000468"
    - "BOTH FLOATING AND PI INVALID BOTH FLOATING AND PI INVALID|T24000469"
    - "BOTH ID AND COMPONENTS GIVEN BOTH ID AND COMPONENTS GIVEN|T24000470"
    - "Both Linked interest and Commission type should not be defined Both Linked interest
    and Commission type should not be defined|T24000471"
    - "Both Live and Simulated Arrangement Nos. not allowed Both Live and Simulated
    Arrangement Nos. not allowed|T24000472"
    - "Both Maturity Date and Term cannot be changed Both Maturity Date and Term cannot
    be changed|T24000473"
    - "Both MATURITY.DATE and TERM cannot be NULL Both MATURITY.DATE and TERM cannot
    be NULL|T24000474"
    - "BOTH NOTICE DAYS AND FREQUENCY CANNOT BE ENTERED BOTH NOTICE DAYS AND FREQUENCY
    CANNOT BE ENTERED|T24000475"
    - "BOTH option cannot be stated BOTH option cannot be stated|T24000476"
    - "CALC. INVALID WITH WORDSCCY CONVERSION CALC. INVALID WITH WORDSCCY CONVERSION|T24000495"
    - "CALC. INVALID WITH WORDSCCY*xxx CONVERSION CALC. INVALID WITH WORDSCCY*xxx CONVERSION|T24000496"
    - "CALC.PROPERTY IS MISSING FOR & PROPERTY CALC.PROPERTY IS MISSING FOR & PROPERTY|T24000497"
    - "CALC.TYPE SHOULD BE PROGRESSIVE CALC.TYPE SHOULD BE PROGRESSIVE|T24000498"
    - "CALCUL.TYPE MISSING CALCUL.TYPE MISSING|T24000499"
    - "CALCULATED not allowed if TARGET.PRODUCT is NULL CALCULATED not allowed if TARGET.PRODUCT
    is NULL|T24000500"
    - "CALCULATION INVALID FOR TEXT CALCULATION INVALID FOR TEXT|T24000502"
    - "CALCULATION PROPERTY & MISSING FROM PRODUCT CALCULATION PROPERTY & MISSING FROM
    PRODUCT|T24000503"
    - "CALCULATION SOURCE PROPERTY & MISSING IN PRODUCT CALCULATION SOURCE PROPERTY
    & MISSING IN PRODUCT|T24000504"
    - "ALLOWED ONLY FOR VALUE.DATED ALLOWED ONLY FOR VALUE.DATED|T24000263"
    - "Allowed only for Writeoff Balance activity Allowed only for Writeoff Balance
    activity|T24000264"
    - "ALREADY SPECIFIED IN THE ID ALREADY SPECIFIED IN THE ID|T24000291"
    - "ALREADY USED AS A MAIN MASTER ALREADY USED AS A MAIN MASTER|T24000292"
    - "ALREADY USED AS A MNEMONIC ALREADY USED AS A MNEMONIC|T24000293"
    - "ALREADY USED FOR MAIN.MASTER ALREADY USED FOR MAIN.MASTER|T24000294"
    - "ALT KEY NOT IN THIS COMPANY Invalid Customer ID|T24003794"
    - "Amendement not allowed Amendement not allowed|T24000295"
    - "Amendment Not Allowed Amendment Not Allowed|T24000296"
    - "AMENDMENT OF CHILD DEAL IS NOT ALLOWED AMENDMENT OF CHILD DEAL IS NOT ALLOWED|T24000297"
    - "Amendment of Disbursed/Settled Bill is not allowed Amendment of Disbursed/Settled
    Bill is not allowed|T24000298"
    - "Amendment to authorised record prohibited. Amendment to authorised record prohibited.|T24000299"
    - "AMENDMENTS NOT ALLOWED AMENDMENTS NOT ALLOWED|T24000300"
    - "AMORT PERIOD CANNOT BE MATURITY FOR & LINE AMORT PERIOD CANNOT BE MATURITY FOR
    & LINE|T24000301"
    - "Amortisation Term allowed for Annuity, Linear, Other Amortisation Term allowed
    for Annuity, Linear, Other|T24000302"
    - "AMORTISATION TERM LESS THAN CONTRACT TERM AMORTISATION TERM LESS THAN CONTRACT
    TERM|T24000303"
    - "Amortisation term not allowed for matured contract Amortisation term not allowed
    for matured contract|T24000304"
    - "Application Module is Mandatory Application Module is Mandatory|T24000331"
    - "Application not valid Application not valid|T24000332"
    - "CCY: INVALID LENGTH|T24000701"
    - "Change Date Should not be less than effective date Change Date Should not be
    less than effective date|T24000702"
    - "CHANGE OF PRODUCT TO A DIFFERENT GROUP & NOT ALLOWED CHANGE OF PRODUCT TO A
    DIFFERENT GROUP & NOT ALLOWED|T24000703"
    - "Both PERFORMING and SUSPENDED options must be defined Both PERFORMING and SUSPENDED
    options must be defined|T24000477"
    - "CAMT TYPES ALLOWED, ONLY IF IX PRODUCT IS INSTALLED CAMT TYPES ALLOWED,
    ONLY IF IX PRODUCT IS INSTALLED|T24000509"
    - "Can not make as blank Can not make as blank|T24000510"
    - "CAN.PROP.CLASS Missing CAN.PROP.CLASS Missing|T24000511"
    - "Cancel date less than take over effective date Cancel date less than take over
    effective date|T24000512"
    - "Cancel Period Should be less than Maturity Date Cancel Period Should be less
    than Maturity Date|T24000513"
    - "Cannont Reverse as Cr or Dr settle activity exist Cannont Reverse as Cr or Dr
    settle activity exist|T24000514"
    - "Cannot Amend Processed Holiday Definition Cannot Amend Processed Holiday Definition|T24000515"
    - "Cannot Amend Record which is in Liquidated status Cannot Amend Record which
    is in Liquidated status|T24000516"
    - "Cannot amend the limit managed by AA Cannot amend the limit managed by AA|T24000517"
    - "Cannot authorise, UNAU REPORT.CONTROL exists Cannot authorise, UNAU REPORT.CONTROL
    exists|T24000518"
    - "Cannot Backdate Activity due to & constraint. Cannot Backdate Activity due to
    & constraint.|T24000519"
    - "CANNOT BALANCE, AMOUNT TOO LARGE CANNOT BALANCE, AMOUNT TOO LARGE|T24000520"
    - "Cannot batch BL.REGISTERs with different product Cannot batch BL.REGISTERs with
    different product|T24000521"
    - "CANNOT BE < DATE.OF.ISSUE CANNOT BE < DATE.OF.ISSUE|T24000522"
    - "CANNOT BE < STOP.DATE CANNOT BE < STOP.DATE|T24000524"
    - "CANNOT BE > ACTION.DATE CANNOT BE > ACTION.DATE|T24000525"
    - "CANNOT BE > TODAY CANNOT BE > TODAY|T24000526"
    - "CANNOT BE > VALUE.DATE CANNOT BE > VALUE.DATE|T24000528"
    - "CANNOT BE AFTER TODAYS DATE CANNOT BE AFTER TODAYS DATE|T24000529"
    - "CANNOT BE AMENDED - CARRIER IS ACTIVE CANNOT BE AMENDED - CARRIER IS ACTIVE|T24000530"
    - "CANNOT BE CAPITALISED CANNOT BE CAPITALISED|T24000531"
    - "CANNOT BE CONTINGENT, & IS NON-CONT CANNOT BE CONTINGENT, & IS NON-CONT|T24000532"
    - "CANNOT BE DEFINED FOR CALC TYPE CANNOT BE DEFINED FOR CALC TYPE|T24000533"
    - "CANNOT BE ENTERED SINCE OPERAND IS BLANK CANNOT BE ENTERED SINCE OPERAND IS
    BLANK|T24000534"
    - "CANNOT BE FORWARD CANNOT BE FORWARD|T24000535"
    - "CANNOT BE GREATER THAN 100 CANNOT BE GREATER THAN 100%|T24000536"
    - "CANNOT BE GREATER THAN 31 DAYS CANNOT BE GREATER THAN 31 DAYS|T24000537"
    - "CANNOT BE GREATER THAN BILL PRODUCED CANNOT BE GREATER THAN BILL PRODUCED|T24000538"
    - "CANNOT BE GREATER THAN SOURCE AMOUNT CANNOT BE GREATER THAN SOURCE AMOUNT|T24000539"
    - "CANNOT BE GREATER THAN TODAY CANNOT BE GREATER THAN TODAY|T24000540"
    - "CANNOT BE LESS THAN 1 YEAR CANNOT BE LESS THAN 1 YEAR|T24000541"
    - "CANNOT BE LESS THAN LAST PAYMENT DATE CANNOT BE LESS THAN LAST PAYMENT DATE|T24000542"
    - "CANNOT BE LESS THAN PREVIOUS SCHEDULE DATE CANNOT BE LESS THAN PREVIOUS SCHEDULE
    DATE|T24000543"
    - "CANNOT BE LESS THAN PREVIOUS START DATE CANNOT BE LESS THAN PREVIOUS START DATE|T24000544"
    - "CANNOT BE LESS THAN RECORD DATE CANNOT BE LESS THAN RECORD DATE|T24000545"
    - "CANNOT BE LESS THAN START DATE CANNOT BE LESS THAN START DATE|T24000546"
    - "CANNOT BE NON-CONTINGENT, & IS CONT CANNOT BE NON-CONTINGENT, & IS CONT|T24000547"
    - "CANNOT BE NULL CANNOT BE NULL|T24000548"
    - "CANNOT BE SAME AS ID CANNOT BE SAME AS ID|T24000550"
    - "CANNOT BE SAME AS PRIMARY OWNER CANNOT BE SAME AS PRIMARY OWNER|T24000551"
    - "Cannot be used when SINGLE.LIMIT is not set in LIMIT Cannot be used when SINGLE.LIMIT
    is not set in LIMIT|T24000552"
    - "Cannot be Y when ICA.POST.INTEREST is YES Cannot be Y when ICA.POST.INTEREST
    is YES|T24000553"
    - "CANNOT BE YES CANNOT BE YES|T24000554"
    - "Cannot be Zero Cannot be Zero|T24000556"
    - "CANNOT BEGIN OR END WITH A . CANNOT BEGIN OR END WITH A .|T24000558"
    - "CANNOT BLANK OUT - PREMIUM FREQ HAS BEEN ENTERED CANNOT BLANK OUT - PREMIUM
    FREQ HAS BEEN ENTERED|T24000559"
    - "Cannot capitalise if OVERDUE property is set to SUSPEND Cannot capitalise if
    OVERDUE property is set to SUSPEND|T24000560"
    - "CANNOT CHANGE , GROUP HAS LINKS CANNOT CHANGE , GROUP HAS LINKS|T24000561"
    - "Cannot change from Y to NO Cannot change from Y to NO|T24000562"
    - "Cannot change from YES to NO Cannot change from YES to NO|T24000563"
    - "CANNOT CHANGE TYPE FOR SUB TYPE CANNOT CHANGE TYPE FOR SUB TYPE|T24000566"
    - "Cannot change when accruals booked Cannot change when accruals booked|T24000567"
    - "Cannot change/delete once set up Cannot change/delete once set up|T24000568"
    - "Cannot choose, when Joint holder is No Cannot choose, when Joint holder is No|T24000569"
    - "CANNOT CLOSE - ENTRY IN AC.BLOCK.CLOSURE CANNOT CLOSE - ENTRY IN AC.BLOCK.CLOSURE|T24000570"
    - "CANNOT CLOSE ACCOUNT, FORWARD ENTRIES EXIST CANNOT CLOSE ACCOUNT, FORWARD ENTRIES
    EXIST|T24000571"
    - "CANNOT CLOSE ACCOUNT, FWD EXPOSURE ENTRIES EXIST CANNOT CLOSE ACCOUNT, FWD EXPOSURE
    ENTRIES EXIST|T24000572"
    - "CANNOT CLOSE ACCT,CONTINGENT BAL EXISTS CANNOT CLOSE ACCT,CONTINGENT BAL EXISTS|T24000573"
    - "CANNOT CLOSE AN ACTIVE ICA ACCOUNT CANNOT CLOSE AN ACTIVE ICA ACCOUNT|T24000574"
    - "CANNOT CLOSE AN ALL IN ONE ACCOUNT CANNOT CLOSE AN ALL IN ONE ACCOUNT|T24000575"
    - "CANNOT CLOSE AN INTERNAL TILL A/C CANNOT CLOSE AN INTERNAL TILL A/C|T24000576"
    - "CANNOT COLLAPSE THIS FIELD CANNOT COLLAPSE THIS FIELD|T24000577"
    - "CANNOT DEFINE BOTH CONSTANT PRORATA CALC TYPES CANNOT DEFINE BOTH CONSTANT PRORATA
    CALC TYPES|T24000578"
    - "Cannot define both MAP.TO.AAA.FIELD and MAP.TO.PROPERTY Cannot define both MAP.TO.AAA.FIELD
    and MAP.TO.PROPERTY|T24000579"
    - "CANNOT DELETE MESSAGE CANNOT DELETE MESSAGE|T24000580"
    - "CANNOT ENTER AUTO RECEIVE CCY CANNOT ENTER AUTO RECEIVE CCY|T24000582"
    - "CANNOT ENTER AUTO RECEIVE CCYS CANNOT ENTER AUTO RECEIVE CCYS|T24000583"
    - "CANNOT ENTER IF CHEQUE IND IS NOT Y CANNOT ENTER IF CHEQUE IND IS NOT Y|T24000584"
    - "CANNOT ENTER PENDING CATEGORY CODE CANNOT ENTER PENDING CATEGORY CODE|T24000585"
    - "CANNOT ENTER SINGLE-VALUED FIELD CANNOT ENTER SINGLE-VALUED FIELD|T24000586"
    - "CANNOT ENTER THIS FIELD IF TYPE NOT ENTERED CANNOT ENTER THIS FIELD IF TYPE
    NOT ENTERED|T24000587"
    - "CANNOT ENTER WITHOUT CHARGE TYPE CANNOT ENTER WITHOUT CHARGE TYPE|T24000588"
    - "CANNOT ENTER WITHOUT THE PREMIUM TYPE CANNOT ENTER WITHOUT THE PREMIUM TYPE|T24000589"
    - "CANNOT ENTER CANNOT ENTER|T24000581"
    - "Cannot exceed max available disburse amount Cannot exceed max available disburse
    amount|T24000590"
    - "CANNOT EXPAND MULTI-VALUE FOR INWARD REQUESTS CANNOT EXPAND MULTI-VALUE FOR
    INWARD REQUESTS|T24000591"
    - "Atleast one balance type must be adjusted. Atleast one balance type must be
    adjusted.|T24000372"
    - "Atleast one input is mandatory Atleast one input is mandatory|T24000373"
    - "Atleast one Master has to be defined Atleast one Master has to be defined|T24000374"
    - "Atleast one property amount must be adjusted. Atleast one property amount must
    be adjusted.|T24000375"
    - "Atleast one schedule Mandatory on/or before Maturity Atleast one schedule Mandatory
    on/or before Maturity|T24000376"
    - "Atleast One(Frequency/StartDate/EndDate) is Mandatory Atleast One(Frequency/StartDate/EndDate)
    is Mandatory|T24000377"
    - "Atleast two Sim Ref required to compare Atleast two Sim Ref required to compare|T24000378"
    - "Authorised record not allowed to change Authorised record not allowed to change|T24000379"
    - "BATCH only for APP.ID as CHEQUE.BATCH BATCH only for APP.ID as CHEQUE.BATCH|T24000417"
    - "BATCH=0 BATCH=0|T24000418"
    - "BC CODE MISSING - ACCOUNT OUTSIDE BANK BC CODE MISSING - ACCOUNT OUTSIDE BANK|T24000419"
    - "BC CODE NOT ALLOWED - ACCOUNT EXISTS BC CODE NOT ALLOWED - ACCOUNT EXISTS|T24000420"
    - "Begin.of range & > End of range & Begin.of range & > End of range &|T24000421"
    - "Below Fields cannot be Null Below Fields cannot be Null|T24000422"
    - "Below Fields Should be Null Below Fields Should be Null|T24000423"
    - "Below minimum value (&) Below minimum value (&)|T24000424"
    - "FIELD NOT VALID FOR THIS TXN TYPE|T24003816"
    - "BENEFICIARY & RECEIVER BANK THE SAME BENEFICIARY & RECEIVER BANK THE SAME|T24000425"
    - "Beneficiary is Mandatory when Po Product is defined. Beneficiary is Mandatory
    when Po Product is defined.|T24000426"
    - "CANNOT LINK FROM A MAIN MASTER CANNOT LINK FROM A MAIN MASTER|T24000602"
    - "CANNOT LOCATE HISTORY FILE RECORD CANNOT LOCATE HISTORY FILE RECORD|T24000603"
    - "Cannot modify authorised record Cannot modify authorised record|T24000604"
    - "CANNOT OPEN & CANNOT OPEN &|T24000605"
    - "CANNOT OPEN COMO.OUTPUT.PTR - PRESS RETURN CANNOT OPEN COMO.OUTPUT.PTR - PRESS
    RETURN|T24000606"
    - "CANNOT OPEN FILE CANNOT OPEN FILE|T24000607"
    - "CANNOT OPEN GROUP.CAPITALISATION FILE CANNOT OPEN GROUP.CAPITALISATION FILE|T24000608"
    - "CANNOT OPEN LOCAL VOC FILE CANNOT OPEN LOCAL VOC FILE|T24000609"
    - "CANNOT OPEN VOC FILE CANNOT OPEN VOC FILE|T24000610"
    - "CANNOT OVERWRITE PASSWORD CANNOT OVERWRITE PASSWORD|T24000611"
    - "Cannot perform funds diversion from internal account to customer account Cannot
    perform funds diversion from internal account to customer account|T24000612"
    - "CANNOT READ ACCOUNT & CANNOT READ ACCOUNT &|T24000613"
    - "CANNOT READ ACCT.GROUP.CONDITION & CANNOT READ ACCT.GROUP.CONDITION &|T24000614"
    - "CANNOT READ PREM.PROC.PREMPTED RECORD & CANNOT READ PREM.PROC.PREMPTED RECORD
    &|T24000615"
    - "CANNOT READ STMT.ENTRY RECORD & CANNOT READ STMT.ENTRY RECORD &|T24000616"
    - "Cannot Reduce the processed Holiday Dates Cannot Reduce the processed Holiday
    Dates|T24000617"
    - "Cannot remove currency from already published Product Cannot remove currency
    from already published Product|T24000618"
    - "Cannot remove, sub accounts linked Cannot remove, sub accounts linked|T24000619"
    - "Cannot reverse - EOD running. Cannot reverse - EOD running.|T24000620"
    - "Cannot reverse - F.DE.O.MSG. & is not empty. Cannot reverse - F.DE.O.MSG. &
    is not empty.|T24000621"
    - "Cannot reverse as status is pending closure Cannot reverse as status is pending
    closure|T24000629"
    - "Cannot Reverse Live HVT Parameter Cannot Reverse Live HVT Parameter|T24000630"
    - "Cannot set WFM from POS Cannot set WFM from POS|T24000631"
    - "Cannot Settle Account Cannot Settle Account|T24000632"
    - "Cannot Suspend Account Cannot Suspend Account|T24000633"
    - "Cannot trigger another activity when & is unauthorised Cannot trigger another
    activity when & is unauthorised|T24000634"
    - "Cannot Write Off a Matched Message Cannot Write Off a Matched Message|T24000635"
    - "Cant capitalise CONSTANT payment type Cant capitalise CONSTANT payment type|T24000636"
    - "Cant change arrangement since its linked Cant change arrangement since
    its linked|T24000637"
    - "Cant change from null to NO Cant change from null to NO|T24000638"
    - "Cant change status date for expired doc Cant change status date for expired
    doc|T24000639"
    - "Cant close a master account Cant close a master account|T24000640"
    - "CANT CLOSE ONLINE PENDING ENTRIES EXIST CANT CLOSE ONLINE PENDING ENTRIES EXIST|T24000641"
    - "Cant Enter Pending Category Code Cant Enter Pending Category Code|T24000642"
    - "CANT OPEN &HOLD& CANT OPEN &HOLD&|T24000643"
    - "CANT OPEN F.HOLD.CONTROL CANT OPEN F.HOLD.CONTROL|T24000644"
    - "Cant read & from STANDARD.SELECTION Cant read & from STANDARD.SELECTION|T24000645"
    - "Cant read SYSTEM.STATUS from F.DE.PARM Cant read SYSTEM.STATUS from F.DE.PARM|T24000646"
    - "CANT WRITE TO F.HOLD.CONTROL CANT WRITE TO F.HOLD.CONTROL|T24000647"
    - "CANT WRITE TO F.REPORT.CONTROL CANT WRITE TO F.REPORT.CONTROL|T24000648"
    - "CAP.BACK.VALUE FIELD NOT EMPTY CAP.BACK.VALUE FIELD NOT EMPTY|T24000649"
    - "CAPITAL DATE LESS THAN TODAY CAPITAL DATE LESS THAN TODAY|T24000650"
    - "Capitalise method not allowed for fixed interest Capitalise method not allowed
    for fixed interest|T24000651"
    - "CAPITALISE SHOULD NOT DEFINE IN ACT.CHARGE OF AGENT CAPITALISE SHOULD NOT DEFINE
    IN ACT.CHARGE OF AGENT|T24000652"
    - "CAPITALISE SHOULD NOT DEFINE IN SCHEDULE OF AGENT CAPITALISE SHOULD NOT DEFINE
    IN SCHEDULE OF AGENT|T24000653"
    - "Capture bill allowed only on takeover arrangements Capture bill allowed only
    on takeover arrangements|T24000654"
    - "CARRIER DOES NOT EXIST ON ADDRESS FILE CARRIER DOES NOT EXIST ON ADDRESS FILE|T24000655"
    - "CARRIER IS MISSING CARRIER IS MISSING|T24000656"
    - "CARRIER IS NOT AN ADDRESS CARRIER CARRIER IS NOT AN ADDRESS CARRIER|T24000657"
    - "Carrier Module should be ISOMX Carrier Module should be ISOMX|T24000658"
    - "CARRIER NOT DEFINED ON DE.CARRIER CARRIER NOT DEFINED ON DE.CARRIER|T24000659"
    - "CARRIER NOT PRESENT CARRIER NOT PRESENT|T24000660"
    - "CARRIER NOT SHUTDOWN CARRIER NOT SHUTDOWN|T24000661"
    - "CARRIER NOT USED CARRIER NOT USED|T24000662"
    - "CARRIER PROGRAM DOES NOT EXIST CARRIER PROGRAM DOES NOT EXIST|T24000663"
    - "CARRIER.NO MUST BE NUMERIC CARRIER.NO MUST BE NUMERIC|T24000664"
    - "CATEG ENTRY RECORD MISSING ID = & CATEG ENTRY RECORD MISSING ID = &|T24000665"
    - "Categories cannot be null Categories cannot be null|T24000666"
    - "Category code in range 10000-19999 only valid Category code in range 10000-19999
    only valid|T24000667"
    - "Category code in range 50000-59999 only valid Category code in range 50000-59999
    only valid|T24000668"
    - "CATEGORY CODE NOT ALLOWED CATEGORY CODE NOT ALLOWED|T24000669"
    - "CATEGORY DOES NOT MATCH APPLICATION CATEGORY DOES NOT MATCH APPLICATION|T24000670"
    - "Category FROM cannot be greater than Category TO Category FROM cannot be greater
    than Category TO|T24000671"
    - "Category FROM Mandatory when Category TO Defined Category FROM Mandatory when
    Category TO Defined|T24000672"
    - "CATEGORY MUST BE IN RANGE 01 - 09 CATEGORY MUST BE IN RANGE 01 - 09|T24000673"
    - "Category TO Mandatory when Category FROM Defined Category TO Mandatory when
    Category FROM Defined|T24000688"
    - "Category To Should be greater than Category From Category To Should be greater
    than Category From|T24000689"
    - "CATEGORY VALUE MISSING CATEGORY VALUE MISSING|T24000690"
    - "CCY AMOUNT IS MANDATORY CCY AMOUNT IS MANDATORY|T24000691"
    - "CCY CODE MISSING CCY CODE MISSING|T24000692"
    - "CCY MKT MISSING FOR LOCAL CCY CCY MKT MISSING FOR LOCAL CCY|T24000693"
    - "CCY MKT not set up for & CCY MKT not set up for &|T24000694"
    - "CCY MKT not set up for BUY currency & CCY MKT not set up for BUY currency &|T24000695"
    - "CCY MKT not set up for FIX currency & CCY MKT not set up for FIX currency &|T24000696"
    - "CCY MKT not set up for local currency & CCY MKT not set up for local currency
    &|T24000697"
    - "CCY MKT not set up for SELL currency & CCY MKT not set up for SELL currency
    &|T24000698"
    - "CCY MUST BE LOCAL CCY MUST BE LOCAL|T24000699"
    - "CCY NOT EQUAL TO ACCOUNT CCY CCY NOT EQUAL TO ACCOUNT CCY|T24000700"
    - "BOTH PERIODIC AND FIXED NOT ALLOWED BOTH PERIODIC AND FIXED NOT ALLOWED|T24000478"
    - "BOTH PI AND BI NOT ALLOWED BOTH PI AND BI NOT ALLOWED|T24000479"
    - "Both Refer Limit and Linked Rate shouldnt set Both Refer Limit and Linked
    Rate shouldnt set|T24000480"
    - "Both start and end category should be given Both start and end category should
    be given|T24000481"
    - "Both the options cannot be selected (E-137917) Both the options cannot be selected
    (E-137917)|T24000483"
    - "Both the options cannot be selected Both the options cannot be selected|T24000482"
    - "Box & Could Not be Found Please provide Valid Box to Proceed Box & Could Not
    be Found Please provide Valid Box to Proceed|T24000484"
    - "Box Type Mismatch ( Type entered is & But Box Number & is of type &) Box Type
    Mismatch ( Type entered is & But Box Number & is of type &)|T24000485"
    - "Buy amount not numeric Buy amount not numeric|T24000486"
    - "BUY CCY = SELL CCY BUY CCY = SELL CCY|T24000487"
    - "BUY CCY MKT suspended & BUY CCY MKT suspended &|T24000488"
    - "Buy currency record missing Buy currency record missing|T24000489"
    - "Buy fixed rate missing Buy fixed rate missing|T24000490"
    - "Buyer and Seller cannot be same Customer Buyer and Seller cannot be same Customer|T24000491"
    - "C OR W MISSING C OR W MISSING|T24000492"
    - "Calc type schould be ACTUAL for advance payment mode Calc type schould be ACTUAL
    for advance payment mode|T24000493"
    - "CALC. INVALID WITH WORDS CONVERSION CALC. INVALID WITH WORDS CONVERSION|T24000494"
    - "Calculated not allowed when an Accounting accrue flag set as ACCRUE Calculated
    not allowed when an Accounting accrue flag set as ACCRUE|T24000501"
    - "CALCULATION TYPE REQUIRED CALCULATION TYPE REQUIRED|T24000505"
    - "CALCULATION TYPE SHOULD BE CONSTANT OR LINEAR CALCULATION TYPE SHOULD BE CONSTANT
    OR LINEAR|T24000506"
    - "CALENDAR OR WORKING DAYS MUST BE SPECIFIED CALENDAR OR WORKING DAYS MUST BE
    SPECIFIED|T24000507"
    - "CALENDAR OR WORKING DAYS NOT SPECIFIED CALENDAR OR WORKING DAYS NOT SPECIFIED|T24000508"
    - "CATEGORY must be in the range 0-69999 CATEGORY must be in the range 0-69999|T24000678"
    - "CATEGORY NOT ALLOWED CATEGORY NOT ALLOWED|T24000679"
    - "Category not defined in ACCOUNT.PARAMETER Category not defined in ACCOUNT.PARAMETER|T24000680"
    - "CATEGORY not found CATEGORY not found|T24000681"
    - "CATEGORY NOT NUMERIC CATEGORY NOT NUMERIC|T24000682"
    - "CATEGORY OR SUB-DIVISION MISMATCH CATEGORY OR SUB-DIVISION MISMATCH|T24000683"
    - "CATEGORY OUT OF RANGE CATEGORY OUT OF RANGE|T24000684"
    - "Category Range Overlapping Category Range Overlapping|T24000685"
    - "Category record missing Category record missing|T24000686"
    - "CATEGORY SELECTION ALREADY DEFINED CATEGORY SELECTION ALREADY DEFINED|T24000687"
    - "CHANGE ONLY ALLOW FOR THE SENDER.ID CHANGE ONLY ALLOW FOR THE SENDER.ID|T24000704"
    - "CHANGE ONLY ALLOWED FOR NOSTRO CHANGE ONLY ALLOWED FOR NOSTRO|T24000705"
    - "Change Period cannot be greater than Term Change Period cannot be greater than
    Term|T24000706"
    - "CHARGE ACCOUNT NOT SPECIFIED CHARGE ACCOUNT NOT SPECIFIED|T24000722"
    - "CHARGE AMOUNT NOT SPECIFIED CHARGE AMOUNT NOT SPECIFIED|T24000723"
    - "Charge amount should not be zero. Charge amount should not be zero.|T24000724"
    - "Charge Calculation not supported for Band Tier Type & Tier Source Charge Calculation
    not supported for Band Tier Type & Tier Source|T24000725"
    - "Charge cannot exceed Principal Amount Charge cannot exceed Principal Amount|T24000726"
    - "CHARGE CATEGORY NOT DEFINED CHARGE CATEGORY NOT DEFINED|T24000727"
    - "CHARGE CCY MKT & not set up for & CHARGE CCY MKT & not set up for &|T24000728"
    - "Charge Date is not a Working Day Charge Date is not a Working Day|T24000729"
    - "CHARGE KEY HAS TO BE INPUT CHARGE KEY HAS TO BE INPUT|T24000731"
    - "Charge Frequency can be specified only if Status is specified Charge Frequency
    can be specified only if Status is specified|T24000730"
    - "CHARGE KEY NOT DEFINED CHARGE KEY NOT DEFINED|T24000732"
    - "CHARGE NOT APPLICABLE FOR APPLICATION.TYPE CURRENT CHARGE NOT APPLICABLE FOR
    APPLICATION.TYPE CURRENT|T24000733"
    - "Charge not defined as Commission type Charge not defined as Commission type|T24000734"
    - "Charge off Balance cannot be captured Charge off Balance cannot be captured|T24000735"
    - "Charge off not allowed. Charge off not allowed.|T24000736"
    - "Charge Override Method should not same as Default value Charge Override Method
    should not same as Default value|T24000737"
    - "Charge Property Not Set Charge Property Not Set|T24000738"
    - "CHARGE ROUTINE IS VALID ONLY FOR ROUTINE CALC TYPE CHARGE ROUTINE IS VALID ONLY
    FOR ROUTINE CALC TYPE|T24000739"
    - "CHARGE ROUTINE REQUIRED CHARGE ROUTINE REQUIRED|T24000740"
    - "Charge type mandatory Charge type mandatory|T24000741"
    - "CHARGE TYPE NOT SPECIFIED CHARGE TYPE NOT SPECIFIED|T24000742"
    - "Charge type should be mandatory Charge type should be mandatory|T24000743"
    - "CHARGE.CODE IN PAYMENT.STOP.TYPE SHOULD BE NULL CHARGE.CODE IN PAYMENT.STOP.TYPE
    SHOULD BE NULL|T24000744"
    - "CHARGE.CODE.LEVEL COM MISSING CHARGE.CODE.LEVEL COM MISSING|T24000745"
    - "Charge-off Amount is greater than available amount of & Charge-off Amount is
    greater than available amount of &|T24000746"
    - "Charge-off Amount Should Not be Zero or Null Charge-off Amount Should Not be
    Zero or Null|T24000747"
    - "Chargeoff Balance not allowed & Chargeoff Balance not allowed &|T24000748"
    - "Chargeoff Category is allowed only for Account property Chargeoff Category is
    allowed only for Account property|T24000749"
    - "Charge-off category is Mandatory Charge-off category is Mandatory|T24000750"
    - "Charge-off Order Input missing Charge-off Order Input missing|T24000751"
    - "CHARGES CANT BE CALCULATED FOR EXISTING CHARGE AMOUNTS CHARGES CANT BE CALCULATED
    FOR EXISTING CHARGE AMOUNTS|T24000752"
    - "CHARGES NOT ALLOWED FOR WAIVE.CHARGE YES CHARGES NOT ALLOWED FOR WAIVE.CHARGE
    YES|T24000753"
    - "Charges taken not equal to default Charges Charges taken not equal to default
    Charges|T24000754"
    - "CHARS 1 - 6 MUST BE ALPHA CHARS 1 - 6 MUST BE ALPHA|T24000755"
    - "CHARS 5-6 MUST BE VALID COUNTRY CODE CHARS 5-6 MUST BE VALID COUNTRY CODE|T24000756"
    - "CHARS 9-11 MUST BE ALPHANUM CHARS 9-11 MUST BE ALPHANUM|T24000757"
    - "CHARS 9-12 MUST BE ALPHANUM CHARS 9-12 MUST BE ALPHANUM|T24000758"
    - "Check for revised contingent accrual AC Check for revised contingent accrual
    AC|T24000759"
    - "Checking for the component Checking for the component|T24000760"
    - "CHEQUE HAS BEEN ALREADY STOCKED CHEQUE HAS BEEN ALREADY STOCKED|T24000761"
    - "CHEQUE NUMBER MUST BE ENTERED CHEQUE NUMBER MUST BE ENTERED|T24000762"
    - "CHEQUE NUMBER NOT INSTRUCTED FOR STOP CHEQUE NUMBER NOT INSTRUCTED FOR STOP|T24000763"
    - "CHEQUE TYPE DOES NOT MATCH WITH ISSUED CHQ TYPES CHEQUE TYPE DOES NOT MATCH
    WITH ISSUED CHQ TYPES|T24000764"
    - "CHEQUE TYPE REQUIRED CHEQUE TYPE REQUIRED|T24000765"
    - "CHEQUE.PROCESS Service not yet run CHEQUE.PROCESS Service not yet run|T24000766"
    - "CHEQUES ARE NOT ALREADY STOCKED CHEQUES ARE NOT ALREADY STOCKED|T24000767"
    - "CHEQUES ARE NOT STOCKED ALREADY CHEQUES ARE NOT STOCKED ALREADY|T24000768"
    - "Cheques have not been issued for this account Cheques have not been issued for
    this account|T24000769"
    - "Cheques have not been issued for this account(E-135634) Cheques have not been
    issued for this account(E-135634)|T24000770"
    - "CHG OR COMM CODE DOES NOT HAVE FLAT AMT CHG OR COMM CODE DOES NOT HAVE FLAT
    AMT|T24000771"
    - "Child reference not allowed for a limit managed by AA Child reference not allowed
    for a limit managed by AA|T24000772"
    - "CHQ COL RECORD & IS IN EXCEPTION CHQ COL RECORD & IS IN EXCEPTION|T24000773"
    - "CHQ COLL RECORD & NOT IN DEPOSITED STATUS CHQ COLL RECORD & NOT IN DEPOSITED
    STATUS|T24000774"
    - "CHRG ACCOUNT TO BE CLOSED CHRG ACCOUNT TO BE CLOSED|T24000775"
    - "Claim account missing Claim account missing|T24000776"
    - "CLASS AND ACTIVITY BOTH CANNOT BE ENTERED CLASS AND ACTIVITY BOTH CANNOT BE
    ENTERED|T24000777"
    - "CLASS CANNOT BE AMENDED FROM NOSTRO CLASS CANNOT BE AMENDED FROM NOSTRO|T24000778"
    - "Class name should begin with alphabet X Class name should begin with alphabet
    X|T24000779"
    - "Class or Instance is Requided on a Definition Line Class or Instance is Requided
    on a Definition Line|T24000780"
    - "Class Type & Does Not Exist in AA.CLASS.TYPE Class Type & Does Not Exist in
    AA.CLASS.TYPE|T24000781"
    - "Class type & Is Invalid for Product & Class type & Is Invalid for Product &|T24000782"
    - "Class type defined not equal to default Class type Class type defined not equal
    to default Class type|T24000783"
    - "COLL.TOTAL MUST BE AFTER TRAILER COLL.TOTAL MUST BE AFTER TRAILER|T24000803"
    - "Collateral Code and Type Dont Match Collateral Code and Type Dont Match|T24000804"
    - "COLLATERAL.TYPE ID IS INVALID COLLATERAL.TYPE ID IS INVALID|T24000805"
    - "Collection type not allowed. Collection type not allowed.|T24000806"
    - "COMB.TRNS.CHRG.CDE MISSING COMB.TRNS.CHRG.CDE MISSING|T24000807"
    - "Communications buffer cant be located F.COMM.BUFFER Communications buffer cant
    be located F.COMM.BUFFER|T24000808"
    - "CANNOT CHANGE ONCE SET UP CANNOT CHANGE ONCE SET UP|T24000564"
    - "Cannot change to OSC type Cannot change to OSC type|T24000565"
    - "Cannot find DE.BIC.PARAMETER record SYSTEM Cannot find DE.BIC.PARAMETER record
    SYSTEM|T24000592"
    - "CANNOT GIVE  WHEN PAY METHOD IS CAPITALISE CANNOT GIVE % WHEN PAY METHOD IS
    CAPITALISE|T24000593"
    - "Cannot give different drawback types for the same Activity Or Charge Cannot
    give different drawback types for the same Activity Or Charge|T24000594"
    - "CANNOT GIVE MV/SV DEFINITIONS CANNOT GIVE MV/SV DEFINITIONS|T24000595"
    - "CANNOT HAVE BOTH BASIC.RATE AND INT.RATE CANNOT HAVE BOTH BASIC.RATE AND INT.RATE|T24000596"
    - "Cannot have more than one Interest Type Cannot have more than one Interest Type|T24000597"
    - "Cannot have more than two tiers Cannot have more than two tiers|T24000598"
    - "Cannot have values for all CUSTOMER fields Cannot have values for all CUSTOMER
    fields|T24000599"
    - "Cannot input both adjustment and new amounts Cannot input both adjustment and
    new amounts|T24000600"
    - "Cannot Input When Settle Method is Null Cannot Input When Settle Method is Null|T24000601"
    - "Cannot reverse - F.DE.O.PRI. & , N is not empty. Cannot reverse - F.DE.O.PRI.
    & , N is not empty.|T24000622"
    - "Cannot reverse - F.DE.O.PRI. & , P is not empty. Cannot reverse - F.DE.O.PRI.
    & , P is not empty.|T24000623"
    - "Cannot reverse - F.DE.O.PRI. & , U is not empty. Cannot reverse - F.DE.O.PRI.
    & , U is not empty.|T24000624"
    - "Cannot reverse - outward formating is running. Cannot reverse - outward formating
    is running.|T24000625"
    - "Cannot reverse - outward formating not yet shutdown. Cannot reverse - outward
    formating not yet shutdown.|T24000626"
    - "CANNOT REVERSE , GROUP HAS LINKS CANNOT REVERSE , GROUP HAS LINKS|T24000627"
    - "CANNOT REVERSE , SUB GROUP HAS LINKS CANNOT REVERSE , SUB GROUP HAS LINKS|T24000628"
    - "Define Charge-off order for above charge-off status Define Charge-off order
    for above charge-off status|T24001001"
    - "Define same values for all payment types Define same values for all payment
    types|T24001002"
    - "DEFINED AS A SUB GROUP DEFINED AS A SUB GROUP|T24001003"
    - "CURRENCY CAN NOT BE CHANGED CURRENCY CAN NOT BE CHANGED|T24000865"
    - "CURRENCY CODE MISSING CURRENCY CODE MISSING|T24000866"
    - "Current version is lesser than the previous available version Current version
    is lesser than the previous available version|T24000887"
    - "Cust opportunity history error Cust opportunity history error|T24000891"
    - "Class Type is Required Class Type is Required|T24000784"
    - "Cleared Balance less than Min Bal. Please try later. Cleared Balance less than
    Min Bal. Please try later.|T24000785"
    - "Cleared Balance still Less than Minimum Balance. Cleared Balance still Less
    than Minimum Balance.|T24000786"
    - "CLEARING INTERFACE MUST BE DEFINED CLEARING INTERFACE MUST BE DEFINED|T24000787"
    - "CLEARING SYSTEM MUST BE PRESENT CLEARING SYSTEM MUST BE PRESENT|T24000788"
    - "Close activity only allowed if Arrangement status is in PENDING.CLOSURE Close
    activity only allowed if Arrangement status is in PENDING.CLOSURE|T24000789"
    - "Close arrangement activity only allowed Close arrangement activity only allowed|T24000790"
    - "Close cannot be combined with other Failure Actions Close cannot be combined
    with other Failure Actions|T24000791"
    - "CLOSE DELIVERY PHANTOM FIRST CLOSE DELIVERY PHANTOM FIRST|T24000792"
    - "CLOSE option not allowed for Override CLOSE option not allowed for Override|T24000793"
    - "Close Option Valid only for Relationship Products & Close Option Valid only
    for Relationship Products &|T24000794"
    - "Closing posting restrict not allowed Closing posting restrict not allowed|T24000795"
    - "Closure activity is not allowed. Box is in & Status Closure activity is not
    allowed. Box is in & Status|T24000796"
    - "Closure Period must be Null for online closure Closure Period must be Null for
    online closure|T24000797"
    - "Closure type not allowed for MANUAL Closure method Closure type not allowed
    for MANUAL Closure method|T24000798"
    - "CO.MARGIN.CHANGE WRITE.ERROR. EITHER APPLICATION OR MODE MISSING CO.MARGIN.CHANGE
    WRITE.ERROR. EITHER APPLICATION OR MODE MISSING|T24000799"
    - "CODE & NOT DEFINED IN DD.REASON.CODES CODE & NOT DEFINED IN DD.REASON.CODES|T24000800"
    - "CODE MUST BE 020 OR 025 CODE MUST BE 020 OR 025|T24000801"
    - "CODE MUST NOT BE 020 OR 025 CODE MUST NOT BE 020 OR 025|T24000802"
    - "COMP ACCOUNT TO BE CLOSED COMP ACCOUNT TO BE CLOSED|T24000809"
    - "Company already defined as Merge Company Company already defined as Merge Company|T24000810"
    - "COMPANY DC.TOLERANCE.RATE MISSING COMPANY DC.TOLERANCE.RATE MISSING|T24000811"
    - "COMPANY NOT ON COMPANY.CHECK NOSTRO RECORD COMPANY NOT ON COMPANY.CHECK
    NOSTRO RECORD|T24000812"
    - "COMPANY.CHECK RECORD NOSTRO - MISSING COMPANY.CHECK RECORD NOSTRO -
    MISSING|T24000813"
    - "Comparison Type Cannot Be Changed Comparison Type Cannot Be Changed|T24000814"
    - "COMPENS.ACCOUNT HAS ANOTHER CURRENCY COMPENS.ACCOUNT HAS ANOTHER CURRENCY|T24000815"
    - "COMPILE COMPLETE COMPILE COMPLETE|T24000816"
    - "Composite screen value mandatory for Real Time Opp Composite screen value mandatory
    for Real Time Opp|T24000817"
    - "Compounded Calculation not allowed with Bands Compounded Calculation not allowed
    with Bands|T24000818"
    - "CONCENTRATION CAP MUST BE BETWEEN 0 TO 100 CONCENTRATION CAP MUST BE BETWEEN
    0 TO 100|T24000819"
    - "CONDITION & FOR PROPERTY & HAS INCOMPATIBLE ARR LINK CONDITION & FOR PROPERTY
    & HAS INCOMPATIBLE ARR LINK|T24000820"
    - "Condition does not belong to the defined Class/Instance Condition does not belong
    to the defined Class/Instance|T24000821"
    - "CONDITION NOT EFFECTIVE AS OF & CONDITION NOT EFFECTIVE AS OF &|T24000822"
    - "CONDITION REQUIRES ... BEFORE OR AFTER VALUE CONDITION REQUIRES ... BEFORE OR
    AFTER VALUE|T24000823"
    - "CONDITIONS EXIST CONDITIONS EXIST|T24000824"
    - "Constraint date cannot be on a future date Constraint date cannot be on a future
    date|T24000825"
    - "Context ID missing in AA.CONTEXT.TYPE Context ID missing in AA.CONTEXT.TYPE|T24000826"
    - "Context Type is not valid for Commission Context Type is not valid for Commission|T24000827"
    - "Context Type not valid for Source Type Context Type not valid for Source Type|T24000828"
    - "Contingent Account is not allowed for this Batch Contingent Account is not allowed
    for this Batch|T24000829"
    - "Contingent accounts are not allowed Contingent accounts are not allowed|T24000830"
    - "Contingent Accounts cannot be closed online Contingent Accounts cannot be closed
    online|T24000831"
    - "CONTINGENT TO NON-CONT NOT ALLOWED CONTINGENT TO NON-CONT NOT ALLOWED|T24000832"
    - "CONTINGENT.INT does not match CONTINGENT.INT does not match|T24000833"
    - "CONTINGENT.INT for ID and SETTLE.ACCT must match CONTINGENT.INT for ID and SETTLE.ACCT
    must match|T24000834"
    - "CONTINGENT.INT SHD BE I FOR INTERNAL CONT A/C CONTINGENT.INT SHD BE I FOR INTERNAL
    CONT A/C|T24000835"
    - "CONTRACT NUMBER TO BE > ZERO CONTRACT NUMBER TO BE > ZERO|T24000837"
    - "CONVERSION ALREADY RUN CONVERSION ALREADY RUN|T24000838"
    - "CONVERSION COMPLETE CONVERSION COMPLETE|T24000839"
    - "CONVERSION HAS ALREADY BEEN RUN CONVERSION HAS ALREADY BEEN RUN|T24000840"
    - "Contract and Link Account Number currencies must be the same. Contract and Link
    Account Number currencies must be the same.|T24000836"
    - "CONVERSION INVALID FOR FIELD TAG CONVERSION INVALID FOR FIELD TAG|T24000841"
    - "CONVERSION INVALID FOR TEXT CONVERSION INVALID FOR TEXT|T24000842"
    - "CONVERSION NOT ALLOWED FOR TOTAL.n CONVERSION NOT ALLOWED FOR TOTAL.n|T24000843"
    - "CONVERSION USED FOR FIELD TAG CONVERSION USED FOR FIELD TAG|T24000844"
    - "Cooling Period Not allowed for Take over Contracts Cooling Period Not allowed
    for Take over Contracts|T24000845"
    - "Cooling period should be less than term Cooling period should be less than term|T24000846"
    - "COORDINATES MISSING COORDINATES MISSING|T24000847"
    - "COPIES ARE NOT ALLOWED FOR SWIFT COPIES ARE NOT ALLOWED FOR SWIFT|T24000848"
    - "COPIES NOT ALLOWED COPIES NOT ALLOWED|T24000849"
    - "Correct Proofing Errors prior to Publishing Correct Proofing Errors prior to
    Publishing|T24000850"
    - "CORRECTION ALREADY AUTHORISED CORRECTION ALREADY AUTHORISED|T24000851"
    - "CORRESPONDING DEPOSIT PERIOD MISSING CORRESPONDING DEPOSIT PERIOD MISSING|T24000852"
    - "Corresponding entry printed.Masking Not Allowed Corresponding entry printed.Masking
    Not Allowed|T24000853"
    - "Corressponding Deposit Record Miss Corressponding Deposit Record Miss|T24000854"
    - "COULD NOT LOCK ACCOUNT RECORD COULD NOT LOCK ACCOUNT RECORD|T24000855"
    - "COULD NOT WRITE TO OFS DIRECTORY COULD NOT WRITE TO OFS DIRECTORY|T24000856"
    - "COUNTRY AND CITY CODES SEPARATED BY - COUNTRY AND CITY CODES SEPARATED BY -|T24000857"
    - "CREDIT CATEGORY NOT DEFINED CREDIT CATEGORY NOT DEFINED|T24000858"
    - "CREDIT ENTRY MISSING CREDIT ENTRY MISSING|T24000859"
    - "Credit property not allowed for Payment Method Credit property not allowed for
    Payment Method|T24000860"
    - "Credit Settle or Debit Settle activity only allowed Credit Settle or Debit Settle
    activity only allowed|T24000861"
    - "CREDIT TXN CODE CANNOT BE BLANK CREDIT TXN CODE CANNOT BE BLANK|T24000862"
    - "CREDIT2 CATEGORY NOT DEFINED CREDIT2 CATEGORY NOT DEFINED|T24000863"
    - "CATEGORY MUST BE IN RANGE 01 - 19 CATEGORY MUST BE IN RANGE 01 - 19|T24000674"
    - "CATEGORY MUST BE IN RANGE 10 - 19 CATEGORY MUST BE IN RANGE 10 - 19|T24000675"
    - "CATEGORY MUST BE IN RANGE 1000 - 9999 CATEGORY MUST BE IN RANGE 1000 - 9999|T24000676"
    - "CATEGORY MUST BE IN RANGE 10000 TO 19999 CATEGORY MUST BE IN RANGE 10000 TO
    19999|T24000677"
    - "Change period shouldnt go beyond the effective date Change period shouldnt
    go beyond the effective date|T24000707"
    - "Change Product & not available in company of product & Change Product & not
    available in company of product &|T24000708"
    - "Change Product can happen only within the same Group Change Product can happen
    only within the same Group|T24000709"
    - "CHANGE PRODUCT CANNOT BE INHRT ONLY CHANGE PRODUCT CANNOT BE INHRT ONLY|T24000710"
    - "Change product missing in product Change product missing in product|T24000711"
    - "Change product not allowed for company. Change product not allowed for company.|T24000712"
    - "Change product not allowed with existing profit amount Change product not allowed
    with existing profit amount|T24000713"
    - "Change to Product not in Allowed Products Change to Product not in Allowed Products|T24000714"
    - "Change to same customer is not allowed Change to same customer is not allowed|T24000715"
    - "CHANGE.DATE cannot be greater than Maturity Date CHANGE.DATE cannot be greater
    than Maturity Date|T24000716"
    - "CHAR 7 MUST BE A-Z or 2-9 CHAR 7 MUST BE A-Z or 2-9|T24000717"
    - "CHAR 8 MUST BE A-Z or 1-9 CHAR 8 MUST BE A-Z or 1-9|T24000718"
    - "CHAR 9 MUST BE A-Z or 2-9 CHAR 9 MUST BE A-Z or 2-9|T24000719"
    - "CHARACTER NOT VALID IN ID CHARACTER NOT VALID IN ID|T24000720"
    - "Charge / Amort End flag mandatory Charge / Amort End flag mandatory|T24000721"
    - "Customer id missing Customer id missing|T24000902"
    - "Customer Id not allowed in Range definition Customer Id not allowed in Range
    definition|T24000903"
    - "Customer Id Should not be changed Customer Id Should not be changed|T24000904"
    - "Customer id should not be removed Customer id should not be removed|T24000905"
    - "CUSTOMER IS NOT A VALID AGENT CUSTOMER IS NOT A VALID AGENT|T24003809"
    - "Customer itself excluded, account invalid Customer itself excluded, account
    invalid|T24000906"
    - "CUSTOMER KEYWORD INVALID CUSTOMER KEYWORD INVALID|T24000907"
    - "CUSTOMER KEYWORD MUST BE ENTERED CUSTOMER KEYWORD MUST BE ENTERED|T24000908"
    - "CUSTOMER MUST BE A BANK FOR SIC CUSTOMER MUST BE A BANK FOR SIC|T24000909"
    - "Customer not allowed on account selection Customer not allowed on account selection|T24000910"
    - "Customer number is mandatory Customer number is mandatory|T24000911"
    - "CUSTOMER Property class mandatory for external product line CUSTOMER Property
    class mandatory for external product line|T24000912"
    - "CUSTOMER PROSPECT NOT ALLOWED & CUSTOMER PROSPECT NOT ALLOWED &|T24000913"
    - "Customer rate not numeric Customer rate not numeric|T24000914"
    - "CUSTOMER RECORD EXISTS FOR THIS PERSON OR ENTITY CUSTOMER RECORD EXISTS FOR
    THIS PERSON OR ENTITY|T24000915"
    - "CUSTOMER RECORD MISSING CUSTOMER RECORD MISSING|T24000916"
    - "Customer record not exists Customer record not exists|T24000917"
    - "CUSTOMER RECORD NOT FOUND & CUSTOMER RECORD NOT FOUND &|T24000918"
    - "Customer role is valid Only when Customer is entered Customer role is valid
    Only when Customer is entered|T24000919"
    - "Customer spread age not numeric Customer spread %age not numeric|T24000920"
    - "Data not defined in POST.SIM.DATA Data not defined in POST.SIM.DATA|T24000929"
    - "Data type for & not equal to D Data type for & not equal to D|T24000930"
    - "Data Type not valid for the Comparison Type Data Type not valid for the Comparison
    Type|T24000931"
    - "DATE < MAXIMUM BACK VALUE DATE < MAXIMUM BACK VALUE|T24000932"
    - "DATE < TODAY DATE < TODAY|T24000933"
    - "DATE > MAXIMUM FORWARD VALUE DATE > MAXIMUM FORWARD VALUE|T24000936"
    - "DATE CANNOT BE < TODAY DATE CANNOT BE < TODAY|T24000937"
    - "DATE CANNOT BE CHANGED DATE CANNOT BE CHANGED|T24000938"
    - "DATE CANNOT BE GREATER THAN TODAY DATE CANNOT BE GREATER THAN TODAY|T24000939"
    - "Date convention should be same across all currencies Date convention should
    be same across all currencies|T24000940"
    - "DATE EXCEEDS MAXIMUM FORWARD DATE EXCEEDS MAXIMUM FORWARD|T24003800"
    - "Date exposure record missing Date exposure record missing|T24000941"
    - "DATE LESS THAN TODAY DATE LESS THAN TODAY|T24000942"
    - "DATE MUST BE >= TODAY DATE MUST BE >= TODAY|T24000943"
    - "DATE MUST BE GREATER THAN CYCLE FREQ DATE DATE MUST BE GREATER THAN CYCLE FREQ
    DATE|T24000944"
    - "DATE MUST NOT BE EQUAL TO LESS THAN TODAY DATE MUST NOT BE EQUAL TO LESS THAN
    TODAY|T24000945"
    - "Date not allowed which is less than next working day. Date not allowed which
    is less than next working day.|T24000946"
    - "DATE NOT NUMERIC (YYYYMMDD) DATE NOT NUMERIC (YYYYMMDD)|T24000947"
    - "Date should be < arrangement start date Date should be < arrangement start date|T24000948"
    - "Date should be > original contract date Date should be > original contract date|T24000949"
    - "Date should greater than Arrangement Date. Date should greater than Arrangement
    Date.|T24000950"
    - "DATES RECORD MISSING DATES RECORD MISSING|T24000951"
    - "Dates Should be in ascending order Dates Should be in ascending order|T24000952"
    - "DATES SHOULD BE IN CHRONOLOGICAL ORDER DATES SHOULD BE IN CHRONOLOGICAL ORDER|T24000953"
    - "DAY CAN BE (0)1-31 DAY CAN BE (0)1-31|T24003811"
    - "DAY NO CANNOT BE GT END DAY NO DAY NO CANNOT BE GT END DAY NO|T24000954"
    - "DAY NO CANNOT BE LT START DAY NO DAY NO CANNOT BE LT START DAY NO|T24000955"
    - "DAY NUMBER CANNOT BE GT END DAY NO DAY NUMBER CANNOT BE GT END DAY NO|T24000956"
    - "Day Type Input Twice Day Type Input Twice|T24000957"
    - "DAYS GREATER THAN 9 DAYS DAYS GREATER THAN 9 DAYS|T24000958"
    - "DAYS IN EXPOSURE.DATE-FIELD MISSING DAYS IN EXPOSURE.DATE-FIELD MISSING|T24000959"
    - "DAYS: &|T24000960"
    - "DC.BATCH.NOT.BALANCED DC.BATCH.NOT.BALANCED|T24000961"
    - "DD ACCT NO # CONTRACT ACCT NO DD ACCT NO # CONTRACT ACCT NO|T24000962"
    - "DD and Benficiary are mutually exlusive DD and Benficiary are mutually exlusive|T24000963"
    - "DD MANDATE REF MANDATORY DD MANDATE REF MANDATORY|T24000964"
    - "DD Reference or Account. Enter any one. DD Reference or Account. Enter any one.|T24000965"
    - "DD REPAY METHOD MISSING DD REPAY METHOD MISSING|T24000966"
    - "DD.PARAM Mandatory fields OUT.FILE,OUT.REC,OUT.FORMAT DD.PARAM Mandatory fields
    OUT.FILE,OUT.REC,OUT.FORMAT|T24000967"
    - "DDI DIRECTION SHOULD BE OUTWARD DDI DIRECTION SHOULD BE OUTWARD|T24000968"
    - "DE.ADDRESS IS NOT SET DE.ADDRESS IS NOT SET|T24000969"
    - "DE.FORMAT.SWIFT Key & does not exist DE.FORMAT.SWIFT Key & does not exist|T24000970"
    - "DE.I.MSG.SC ALREADY PROCESSED & DE.I.MSG.SC ALREADY PROCESSED &|T24000971"
    - "DEALER.DESK differs from first Disbursement DEALER.DESK differs from first Disbursement|T24000972"
    - "DEBIT / CREDIT TXN CODE CANNOT BE BLANK DEBIT / CREDIT TXN CODE CANNOT BE BLANK|T24000973"
    - "DEBIT AC & CURR & STMT CURRENCY & DEBIT AC & CURR & STMT CURRENCY &|T24000974"
    - "Debit and credit account entered are the same Debit and credit account entered
    are the same|T24000975"
    - "DEBIT CATEGORY NOT DEFINED DEBIT CATEGORY NOT DEFINED|T24000976"
    - "DEBIT ENTRY MISSING DEBIT ENTRY MISSING|T24000977"
    - "DEBIT MANDATORY WHEN CHEQUE.IND Y DEBIT MANDATORY WHEN CHEQUE.IND
    Y|T24000978"
    - "Default product should not have eligibility conditions Default product should
    not have eligibility conditions|T24000993"
    - "Default rule can only be first value Default rule can only be first value|T24000994"
    - "Defer Date definition should be before Payment Date Defer Date definition should
    be before Payment Date|T24000995"
    - "Defer Date not allowed for Account Property in Bill Defer Date not allowed for
    Account Property in Bill|T24000996"
    - "Defer Not Allowed for Same Payment Types Defer Not Allowed for Same Payment
    Types|T24000997"
    - "Defer Period Not Allowed for Account Property Defer Period Not Allowed for Account
    Property|T24000998"
    - "Defer Period Not Allowed for ADVANCE Payment type Defer Period Not Allowed for
    ADVANCE Payment type|T24000999"
    - "Define all payment types if bills are combined Define all payment types if bills
    are combined|T24001000"
    - "CURRENCY DIFFERS FROM ACCOUNT CURRENCY DIFFERS FROM ACCOUNT|T24000867"
    - "CURRENCY ENTERED WITH NO ACCOUNT CURRENCY ENTERED WITH NO ACCOUNT|T24000868"
    - "CURRENCY EQUIVALENT NOT SET UP CURRENCY EQUIVALENT NOT SET UP|T24000869"
    - "Currency is mandatory. Currency is mandatory.|T24000870"
    - "Currency mandatory if Amount is specified Currency mandatory if Amount is specified|T24000871"
    - "Currency mandatory if amount specified Currency mandatory if amount specified|T24000872"
    - "Currency Mandatory when Amount is input Currency Mandatory when Amount is input|T24000873"
    - "Currency Mandatory when Tolerance Amount is inputted Currency Mandatory when
    Tolerance Amount is inputted|T24000874"
    - "Currency market record missing : &|T24000875"
    - "CURRENCY MISSING CURRENCY MISSING|T24000876"
    - "CURRENCY MUST BE SAME AS MAIN GROUP CURRENCY CURRENCY MUST BE SAME AS MAIN GROUP
    CURRENCY|T24000878"
    - "CURRENCY MUST MATCH FOREIGN POSITION ACCOUNT CURRENCY MUST MATCH FOREIGN POSITION
    ACCOUNT|T24000879"
    - "CURRENCY NOT ALLOWED FOR PRODUCT LINE CURRENCY NOT ALLOWED FOR PRODUCT LINE|T24000880"
    - "Currency not applicable for Netting Currency not applicable for Netting|T24000881"
    - "CURRENCY REQUIRED CURRENCY REQUIRED|T24000882"
    - "CURRENCY TYPE PROPERTY.CLASS NOT ALLOWED CURRENCY TYPE PROPERTY.CLASS NOT ALLOWED|T24000883"
    - "Currency value to be specified Currency value to be specified|T24000884"
    - "CURRENT Payment rule type not allowed CURRENT Payment rule type not allowed|T24000885"
    - "Current Rule id was not defined in Rule based fields Current Rule id was not
    defined in Rule based fields|T24000886"
    - "CURRENTLY THIS OPTION IS NOT ALLOWED CURRENTLY THIS OPTION IS NOT ALLOWED|T24000888"
    - "Currently Trade Dated GL Accounting Not Allowed Currently Trade Dated GL Accounting
    Not Allowed|T24000889"
    - "Cust field not present in DM.APPLICATION.INFO for appln Cust field not present
    in DM.APPLICATION.INFO for appln|T24000890"
    - "ENTER SYS.CODE FIRST ENTER SYS.CODE FIRST|T24001197"
    - "DEBIT TXN CANNOT BE BLANK DEBIT TXN CANNOT BE BLANK|T24000979"
    - "DEBIT VALUE LATER THAN CREDIT VALUE DEBIT VALUE LATER THAN CREDIT VALUE|T24500998"
    - "DEBIT.INT.ADDON ALREADY DEFINED DEBIT.INT.ADDON ALREADY DEFINED|T24000980"
    - "DEBIT2 CATEGORY NOT DEFINED DEBIT2 CATEGORY NOT DEFINED|T24000981"
    - "Decrease Exceeds Available Balance of & Decrease Exceeds Available Balance of
    &|T24000982"
    - "Default account should be last multi value Default account should be last multi
    value|T24000983"
    - "DEFAULT DATE & MISSING DEFAULT DATE & MISSING|T24000984"
    - "DEPENDENT ON MUST BE PRESENT DEPENDENT ON MUST BE PRESENT|T24001016"
    - "DEPENDENT ON OPERAND MUST BE PRESENT DEPENDENT ON OPERAND MUST BE PRESENT|T24001017"
    - "DEPT & HAS NO BATCHES DEPT & HAS NO BATCHES|T24001018"
    - "DEPT HAS CHANGED DEPT HAS CHANGED|T24001019"
    - "DEPT NO. MUST BE SPECIFIED DEPT NO. MUST BE SPECIFIED|T24001020"
    - "DEPT=0 DEPT=0|T24001021"
    - "DESCRIPTION IS MANDATORY DESCRIPTION IS MANDATORY|T24001022"
    - "DICTIONARY INCORRECT FOR & DICTIONARY INCORRECT FOR &|T24001023"
    - "Different frequency is not allowed for same schedule name Different frequency
    is not allowed for same schedule name|T24001024"
    - "Different Payment methods not allowed for same Pay type Different Payment methods
    not allowed for same Pay type|T24001025"
    - "DIRECT DEBIT MANDATES EXIST - CANNOT CLOSE. DIRECT DEBIT MANDATES EXIST - CANNOT
    CLOSE.|T24001026"
    - "Direct Input to Sub account is not allowed Direct Input to Sub account is not
    allowed|T24001027"
    - "Direction field is mandatory Direction field is mandatory|T24001028"
    - "Directory & not found Directory & not found|T24001029"
    - "Directory name not found Directory name not found|T24001030"
    - "Disburse allot type differs from previous Disburse allot type differs from previous|T24001031"
    - "Disbursed partially, settlement cant be full Disbursed partially, settlement
    cant be full|T24001032"
    - "DISBURSEMENT Bill Type not allowed DISBURSEMENT Bill Type not allowed|T24001033"
    - "Disbursement of & & exceeds available amount of & Disbursement of & & exceeds
    available amount of &|T24001034"
    - "Discount not allowed,Amortisation Suspended Discount not allowed,Amortisation
    Suspended|T24001035"
    - "Discounted interest is not allowed for tier negative rate = YES or BLOCK.MARGIN
    Discounted interest is not allowed for tier negative rate = YES or BLOCK.MARGIN|T24001036"
    - "Distribution type not defined Distribution type not defined|T24001037"
    - "DMD name / file name invalid DMD name / file name invalid|T24001038"
    - "DMD name not found DMD name not found|T24001039"
    - "DMD-Flatfile name mismatch DMD-Flatfile name mismatch|T24001040"
    - "Document Status not Active Document Status not Active|T24001041"
    - "DOES NOT EXIST ON MESSAGE TABLE DOES NOT EXIST ON MESSAGE TABLE|T24001042"
    - "DOES NOT MATCH ID (&) CUSTOMER DOES NOT MATCH ID (&) CUSTOMER|T24001043"
    - "Donate Type Missing Donate Type Missing|T24001044"
    - "Donor Accrual Type Mandatory Donor Accrual Type Mandatory|T24001045"
    - "Donor Balance Type Missing Donor Balance Type Missing|T24001046"
    - "Dont Sub-value the property class when ppty is input. Dont Sub-value the property
    class when ppty is input.|T24001047"
    - "Dont Sub-value the property when ppty class is input. Dont Sub-value the property
    when ppty class is input.|T24001048"
    - "Dormancy Charge Frequency has been defined. Please configure Activity Charges
    for the Activity & Dormancy Charge Frequency has been defined. Please configure
    Activity Charges for the Activity &|T24001049"
    - "DOUBLE INPUT OF ADDRESSEE DOUBLE INPUT OF ADDRESSEE|T24001050"
    - "DR AND CR FLOOR LIMITS ARE EQUAL DR AND CR FLOOR LIMITS ARE EQUAL|T24001051"
    - "DR CATEGORY NOT DEFINED DR CATEGORY NOT DEFINED|T24001052"
    - "DR FLOOR LIMIT MISSING DR FLOOR LIMIT MISSING|T24001053"
    - "Dr.and Cr. account entered are the same. Please rectify Dr.and Cr. account entered
    are the same. Please rectify|T24001054"
    - "Drawdown and Int Liquidation account currency differs Drawdown and Int Liquidation
    account currency differs|T24001055"
    - "Due date cannot be greater than today Due date cannot be greater than today|T24001056"
    - "Due Date cannot be less than original contract date Due Date cannot be less
    than original contract date|T24001057"
    - "Due exists, Account cannot be closed Due exists, Account cannot be closed|T24001058"
    - "Due frequency not allowed for matured contract Due frequency not allowed for
    matured contract|T24001059"
    - "DUE FRQUENCY SHOULD BE EQUAL TO PAYMENT FREQUENCY DUE FRQUENCY SHOULD BE EQUAL
    TO PAYMENT FREQUENCY|T24001060"
    - "DUE method allowed only for PAYMENT and DEF.CHARGE DUE method allowed only for
    PAYMENT and DEF.CHARGE|T24001061"
    - "Due Method not allowed for Credit type of property Due Method not allowed for
    Credit type of property|T24001062"
    - "DUE payment method not allowed DUE payment method not allowed|T24001063"
    - "Dues Not Settled Dues Not Settled|T24001064"
    - "DUMMY STARTING RECORD 1 MISSING DUMMY STARTING RECORD 1 MISSING|T24001065"
    - "DUPLICATE - SAME AS FREQU.1 DUPLICATE - SAME AS FREQU.1|T24001068"
    - "DUPLICATE - SAME AS FREQU.2 DUPLICATE - SAME AS FREQU.2|T24001069"
    - "DUPLICATE & DUPLICATE &|T24001070"
    - "DUPLICATE ACCOUNT, PLEASE RE-ENTER DUPLICATE ACCOUNT, PLEASE RE-ENTER|T24001071"
    - "Duplicate Activity, Commission and Drawback Type Duplicate Activity, Commission
    and Drawback Type|T24001072"
    - "Duplicate Arrangement ID Duplicate Arrangement ID|T24001073"
    - "Duplicate Asset Class Duplicate Asset Class|T24001074"
    - "DUPLICATE CONDITIONS DUPLICATE CONDITIONS|T24001075"
    - "DUPLICATE CONSTANT / PRORATA PAYMENT TYPES DUPLICATE CONSTANT / PRORATA PAYMENT
    TYPES|T24001077"
    - "DUPLICATE CURRENCY DUPLICATE CURRENCY|T24001078"
    - "Duplicate Customer Not Allowed Duplicate Customer Not Allowed|T24001079"
    - "DUPLICATE DATA NAME DUPLICATE DATA NAME|T24001080"
    - "DUPLICATE DE.PRODUCT.ID DUPLICATE DE.PRODUCT.ID|T24001081"
    - "DUPLICATE DEFINITION NOT ALLOWED, USER.INPUT YES DUPLICATE DEFINITION NOT
    ALLOWED, USER.INPUT YES|T24001082"
    - "Duplicate donor product Duplicate donor product|T24001083"
    - "DUPLICATE ENTRY DUPLICATE ENTRY|T24001084"
    - "Duplicate Future Dated condition already exist Duplicate Future Dated condition
    already exist|T24001085"
    - "Duplicate Holiday payment types not allowed Duplicate Holiday payment types
    not allowed|T24001086"
    - "Duplicate Line definition Duplicate Line definition|T24001087"
    - "DUPLICATE MESSAGE TYPE AND ACCOUNT DUPLICATE MESSAGE TYPE AND ACCOUNT|T24001089"
    - "DUPLICATE MESSAGE TYPE DUPLICATE MESSAGE TYPE|T24001088"
    - "Duplicate Payment Types not Allowed Duplicate Payment Types not Allowed|T24001090"
    - "Duplicate payment types with bill produced not allowed Duplicate payment types
    with bill produced not allowed|T24001091"
    - "CURRENCY & NOT INCLUDED IN DD.PARAMETER & CURRENCY & NOT INCLUDED IN DD.PARAMETER
    &|T24000864"
    - "Customer already availed the current plan Customer already availed the current
    plan|T24000892"
    - "Customer already blocked with this code Customer already blocked with this code|T24000893"
    - "CUSTOMER ALREADY EXISTS CUSTOMER ALREADY EXISTS|T24000894"
    - "Customer Balance cannot be less than Bank Balance Customer Balance cannot be
    less than Bank Balance|T24000895"
    - "CUSTOMER CODE INVALID CUSTOMER CODE INVALID|T24000896"
    - "CUSTOMER DOES NOT BELONGS TO ARRANGEMENT CUSTOMER DOES NOT BELONGS TO ARRANGEMENT|T24000897"
    - "Customer does not maintain & relationship Customer does not maintain & relationship|T24000898"
    - "Customer does not match the selection Customer does not match the selection|T24000899"
    - "Customer doesnot exist Customer doesnot exist|T24000900"
    - "Customer doesnt exist while forming limit structure Customer doesnt exist
    while forming limit structure|T24000901"
    - "Customer spread not numeric Customer spread not numeric|T24000921"
    - "Customer Visit activity is Restricted to Input since Box is not in Rented status
    Customer Visit activity is Restricted to Input since Box is not in Rented status|T24000922"
    - "CX CX|T24000923"
    - "CYCLE GREATER THAN 12 MONTHS CYCLE GREATER THAN 12 MONTHS|T24000924"
    - "CYCLE.NO IS NOT DEFINED CYCLE.NO IS NOT DEFINED|T24000925"
    - "DAILY FREQUENCY NOT ALLOWED DAILY FREQUENCY NOT ALLOWED|T24000926"
    - "Data field cannot be null Data field cannot be null|T24000927"
    - "DATA LENGTH IS MANDATORY DATA LENGTH IS MANDATORY|T24000928"
    - "FIELD MUST BE BLANK FIELD MUST BE BLANK|T24001303"
    - "FIELD MUST BE ENTERED FIELD MUST BE ENTERED|T24001306"
    - "FIELD MUST BE NUMERIC FIELD MUST BE NUMERIC|T24001307"
    - "Either InitiationType/ActivityClass/Activity should be mentioned Either InitiationType/ActivityClass/Activity
    should be mentioned|T24001113"
    - "Either input Amount or Rate or Percent, not all Either input Amount or Rate
    or Percent, not all|T24001114"
    - "Either Input Online or Schedule, not both Either Input Online or Schedule, not
    both|T24001115"
    - "Either input Product line or Group or Product, not all Either input Product
    line or Group or Product, not all|T24001116"
    - "EITHER INTEREST AMT OR TAX AMT SHOULD BE ENTERED EITHER INTEREST AMT OR TAX
    AMT SHOULD BE ENTERED|T24001117"
    - "Either Linear or Accelerate payment type only allowed Either Linear or Accelerate
    payment type only allowed|T24001118"
    - "Either Maturity Either Maturity|T24001119"
    - "Either mvmt or opp target mandatory for an event Either mvmt or opp target mandatory
    for an event|T24001120"
    - "EITHER NEGOTIABLE OR NON NEGOTIABLE EITHER NEGOTIABLE OR NON NEGOTIABLE|T24001121"
    - "EITHER ORDERING CUST..BANK MANDATORY EITHER ORDERING CUST..BANK MANDATORY|T24003791"
    - "Either Payin Account or Payin Po Product, only one is allowed. Either Payin
    Account or Payin Po Product, only one is allowed.|T24001122"
    - "Either Payout Account or Payout Po Product, only one is allowed. Either Payout
    Account or Payout Po Product, only one is allowed.|T24001123"
    - "Either Percent or Discount or Premimum Either Percent or Discount or Premimum|T24001124"
    - "EITHER PRODUCT GROUP OR PARENT MANDATORY EITHER PRODUCT GROUP OR PARENT MANDATORY|T24001126"
    - "EITHER PRODUCT OR PRODUCT.GRP OR CATEGORY MANDATORY EITHER PRODUCT OR PRODUCT.GRP
    OR CATEGORY MANDATORY|T24001127"
    - "EITHER PRODUCT OR PRODUCT.GRP OR CATEGORY OR CEP CAMPAIGN MANDATORY EITHER PRODUCT
    OR PRODUCT.GRP OR CATEGORY OR CEP CAMPAIGN MANDATORY|T24001128"
    - "Either Progressive or Accelerate payment type only allowed Either Progressive
    or Accelerate payment type only allowed|T24001129"
    - "EITHER PROPERTY OR PROPERTY CLASS IS MANDATORY EITHER PROPERTY OR PROPERTY CLASS
    IS MANDATORY|T24001130"
    - "EITHER PROPERTY.CLASS/PROPERTY/ACTIVITY MANDATORY EITHER PROPERTY.CLASS/PROPERTY/ACTIVITY
    MANDATORY|T24001131"
    - "EITHER RET CODE OR REASON MUST BE INPUT EITHER RET CODE OR REASON MUST BE INPUT|T24001132"
    - "Either Selection incorrect or No entries for the period Either Selection incorrect
    or No entries for the period|T24001133"
    - "Eligibility Def product & not available in company of product & Eligibility
    Def product & not available in company of product &|T24001134"
    - "Eligibility property is mandatory for variation product Eligibility property
    is mandatory for variation product|T24001135"
    - "Eligibility property must include all variations Eligibility property must include
    all variations|T24001136"
    - "Empty Line Empty Line|T24001137"
    - "End category cannot be lesser than the start category End category cannot be
    lesser than the start category|T24001138"
    - "End date cannot be entred for matured contract End date cannot be entred for
    matured contract|T24001139"
    - "End date cannot be greater than Today End date cannot be greater than Today|T24001140"
    - "End date cannot be greater than Today. Please rectify End date cannot be greater
    than Today. Please rectify|T24001141"
    - "End date cannot be less than effective date End date cannot be less than effective
    date|T24001142"
    - "End Date Cannot be Less than Today End Date Cannot be Less than Today|T24001143"
    - "End date cant be less than BEGIN.DATE End date cant be less than BEGIN.DATE|T24001144"
    - "End date is less than start date End date is less than start date|T24001145"
    - "END DATE LESS THAN START DATE END DATE LESS THAN START DATE|T24001146"
    - "End date must be greater than start date End date must be greater than start
    date|T24001147"
    - "End date must be null or GE TODAY End date must be null or GE TODAY|T24001149"
    - "End date must be null or GT TODAY End date must be null or GT TODAY|T24001150"
    - "END DATE must not be less than START DATE END DATE must not be less than START
    DATE|T24001151"
    - "END DATE SHOULD BE GREATER THAN START DATE END DATE SHOULD BE GREATER THAN START
    DATE|T24001152"
    - "END LINE MUST BE > START LINE END LINE MUST BE > START LINE|T24001153"
    - "END LINE MUST BE FIXED LINE NO. END LINE MUST BE FIXED LINE NO.|T24001154"
    - "END LINE MUST BE NUMERIC END LINE MUST BE NUMERIC|T24001155"
    - "END OF PERIOD RUNNING END OF PERIOD RUNNING|T24001156"
    - "END.DATE < START.DATE END.DATE < START.DATE|T24001157"
    - "END.HEADER ALREADY DEFINED END.HEADER ALREADY DEFINED|T24001158"
    - "END.HEADER DEFINITION DUPLICATED END.HEADER DEFINITION DUPLICATED|T24001159"
    - "END.HEADER MUST BE BEFORE TRAILER END.HEADER MUST BE BEFORE TRAILER|T24001160"
    - "END.HEADER MUST BE IN COL 1 END.HEADER MUST BE IN COL 1|T24001161"
    - "END.HEADER MUST OCCUPY OWN LINE END.HEADER MUST OCCUPY OWN LINE|T24001162"
    - "END.LINE MUST BE NUMERIC AFTER + END.LINE MUST BE NUMERIC AFTER +|T24001163"
    - "ENQUIRYS FILE.NAME IS NOT AS APP.ID ENQUIRYS FILE.NAME IS NOT AS APP.ID|T24001164"
    - "ENTER 1 - & ONLY ENTER 1 - & ONLY|T24001165"
    - "Enter a valid ARR application version Enter a valid ARR application version|T24001166"
    - "Enter a valid SIM application version Enter a valid SIM application version|T24001167"
    - "Enter any one. Property or Property class. Enter any one. Property or Property
    class.|T24001168"
    - "ENTER EITHER ACCOUNT OR CATEGORY ENTER EITHER ACCOUNT OR CATEGORY|T24001175"
    - "Enter either DD Ref or Account for given Payment Type. Enter either DD Ref or
    Account for given Payment Type.|T24001176"
    - "Enter Either Property class or Property Enter Either Property class or Property|T24001177"
    - "ENTER EVERYTHING BEFORE APPLICATION FORMAT ENTER EVERYTHING BEFORE APPLICATION
    FORMAT|T24001178"
    - "ENTER EVERYTHING BEFORE VERSION FORMAT ENTER EVERYTHING BEFORE VERSION FORMAT|T24001179"
    - "ENTER FUNDS AMOUNT ENTER FUNDS AMOUNT|T24001180"
    - "ENTER FUNDS CCY ENTER FUNDS CCY|T24001181"
    - "ENTER ID.CARRIER.ADDRESS.NO ENTER ID.CARRIER.ADDRESS.NO|T24001182"
    - "ENTER ID.CARRIER.ADDRESS-NO ENTER ID.CARRIER.ADDRESS-NO|T24001183"
    - "ENTER ID.CARRIER.CARRIER-NO ENTER ID.CARRIER.CARRIER-NO|T24001184"
    - "ENTER ID.TYPE.APPLICATION ENTER ID.TYPE.APPLICATION|T24001185"
    - "Enter Joint Holder Enter Joint Holder|T24001186"
    - "ENTER MSG.TYPE ONLY ENTER MSG.TYPE ONLY|T24001187"
    - "ENTER MSG-TYPE.APP-FORMAT.FORMAT ENTER MSG-TYPE.APP-FORMAT.FORMAT|T24001188"
    - "ENTER MSG-TYPE.APP-FORMAT.FORMAT.LANGUAGE ENTER MSG-TYPE.APP-FORMAT.FORMAT.LANGUAGE|T24001189"
    - "ENTER nnC OR nnW (W = WORKING, C = CALEN.) ENTER nnC OR nnW (W = WORKING, C
    = CALEN.)|T24001190"
    - "ENTER nnnC or nnnW (W = WORKING, C = CALEN.) ENTER nnnC or nnnW (W = WORKING,
    C = CALEN.)|T24001191"
    - "ENTER ONLY ACCOUNT NO OR CUSTOMER ENTER ONLY ACCOUNT NO OR CUSTOMER|T24001192"
    - "ENTER ONLY NON CONTRACT SPECIFIC DOCUMENT ENTER ONLY NON CONTRACT SPECIFIC DOCUMENT|T24001193"
    - "Enter Pending Category Code Enter Pending Category Code|T24001194"
    - "Enter suspension amt in Adj Susp Prop amt field Enter suspension amt in Adj
    Susp Prop amt field|T24001195"
    - "Enter suspension amt in New susp prop amt field Enter suspension amt in New
    susp prop amt field|T24001196"
    - "Default Event Activity is mandatory Default Event Activity is mandatory|T24000985"
    - "DEFAULT FORM TYPE NOT ON FILE DEFAULT FORM TYPE NOT ON FILE|T24000986"
    - "Default Marker not set for Default product Default Marker not set for Default
    product|T24000987"
    - "Default product and Current product currency not same Default product and Current
    product currency not same|T24000988"
    - "Default product is not under the same product group Default product is not under
    the same product group|T24000989"
    - "Default product is not valid Default product is not valid|T24000990"
    - "Default product missing Default product missing|T24000991"
    - "Default product not published Default product not published|T24000992"
    - "Defined Product is not a Saleable Product Defined Product is not a Saleable
    Product|T24001004"
    - "Definition Id mandatory Definition Id mandatory|T24001005"
    - "DEFINITIONS IN PROPERTY CLASS MUST BE INPUT DEFINITIONS IN PROPERTY CLASS MUST
    BE INPUT|T24001006"
    - "Delete Action not allowed for restore activity Delete Action not allowed for
    restore activity|T24001007"
    - "DELETE NOT ALLOWED DELETE NOT ALLOWED|T24001008"
    - "Deletion allowed only for manually created request Deletion allowed only for
    manually created request|T24001009"
    - "DELETION OF ENTRIES NOT PERMITTED DELETION OF ENTRIES NOT PERMITTED|T24001010"
    - "DELETION OF EXISTING MULTIVALUE NOT ALLOWED DELETION OF EXISTING MULTIVALUE
    NOT ALLOWED|T24001011"
    - "Deletion of seller not allowed Deletion of seller not allowed|T24001012"
    - "DELETIONS ARE NOT ALLOWED DELETIONS ARE NOT ALLOWED|T24001013"
    - "Delinquency amt should not be negative Delinquency amt should not be negative|T24001014"
    - "Delinquent amt cant be greater than original amt Delinquent amt cant be greater
    than original amt|T24001015"
    - "Enter WithDrawal Period Enter WithDrawal Period|T24001203"
    - "Entered Category Range overlaps with an existing record Entered Category Range
    overlaps with an existing record|T24001204"
    - "Entered Range overlaps with an existing records Entered Range overlaps with
    an existing records|T24001205"
    - "Entry already printed.Masking Not Allowed Entry already printed.Masking Not
    Allowed|T24001206"
    - "ENTRY FOR INPUT NAME IS MANDATORY ENTRY FOR INPUT NAME IS MANDATORY|T24001207"
    - "ENTRY FOR INPUT NAME NOT ALLOWED ENTRY FOR INPUT NAME NOT ALLOWED|T24001208"
    - "ENTRY IN INPUT FILE MANDATORY ENTRY IN INPUT FILE MANDATORY|T24001209"
    - "ENTRY IN INPUT POSITION MUST BE AN INTEGER ENTRY IN INPUT POSITION MUST BE AN
    INTEGER|T24001210"
    - "ENTRY IN INPUT RECORD MANDATORY ENTRY IN INPUT RECORD MANDATORY|T24001211"
    - "ENTRY MORE THAN ACCRUED AMT ENTRY MORE THAN ACCRUED AMT|T24001212"
    - "ENTRY ONLY ALLOWED IF ASSOCIATED AC.FIELD ENTERED ENTRY ONLY ALLOWED IF ASSOCIATED
    AC.FIELD ENTERED|T24001213"
    - "Entry record not passed for checking balance Entry record not passed for checking
    balance|T24001214"
    - "Entry record not passed for checking restriction Entry record not passed for
    checking restriction|T24001215"
    - "ENTRY.TYPE not set &. ENTRY.TYPE not set &.|T24001216"
    - "Equiv document specified as main document Equiv document specified as main document|T24001217"
    - "ER.PARAMETR NOT SETUP ER.PARAMETR NOT SETUP|T24001218"
    - "ERREUR DELIVERY ERREUR DELIVERY|T24001219"
    - "ERROR - Address record does not exist ERROR - Address record does not exist|T24001220"
    - "ERROR - ANSWERBACK MUST BE PRESENT ERROR - ANSWERBACK MUST BE PRESENT|T24001221"
    - "ERROR - CANNOT READ COMO ERROR - CANNOT READ COMO|T24001222"
    - "ERROR - Customer id not present ERROR - Customer id not present|T24001223"
    - "ERROR IN CALLING FIND.CCY.MKT & ERROR IN CALLING FIND.CCY.MKT &|T24001232"
    - "ERROR IN CALLING FIND.CCY.MKT WITH CALL TYPE 1 & ERROR IN CALLING FIND.CCY.MKT
    WITH CALL TYPE 1 &|T24001233"
    - "ERROR IN CALLING FIND.CCY.MKT WITH CALL TYPE 2 & ERROR IN CALLING FIND.CCY.MKT
    WITH CALL TYPE 2 &|T24001234"
    - "ERROR IN READING F.DE.I.MSG - RECORD NOT PROCESSED - ERROR IN READING F.DE.I.MSG
    - RECORD NOT PROCESSED -|T24001235"
    - "error in reading LOCAL CCY error in reading LOCAL CCY|T24001236"
    - "ERROR IN SELECTING RECORDS FROM F.ACCT.SUSP.SETTLE ERROR IN SELECTING RECORDS
    FROM F.ACCT.SUSP.SETTLE|T24001237"
    - "ERROR IN SELECTION OF & ERROR IN SELECTION OF &|T24001238"
    - "ERROR MESSAGE & ERROR MESSAGE &|T24001239"
    - "Error occurred. Please contact bank (E-167977) Error occurred. Please contact
    bank (E-167977)|T24001240"
    - "Error occurred. Please contact Bank for further deatils Error occurred. Please
    contact Bank for further deatils|T24001241"
    - "Error occurred. Please contact bank for further details Error occurred. Please
    contact bank for further details|T24001242"
    - "ERROR READING SWEEP TYPE ERROR READING SWEEP TYPE|T24001244"
    - "ERROR UPDATING INDEX FIELDS ERROR UPDATING INDEX FIELDS|T24001245"
    - "EUCLID = NETWORK PASSWORD EUCLID = NETWORK PASSWORD|T24001246"
    - "EUCLID CARRIER NOT PRESENT EUCLID CARRIER NOT PRESENT|T24001247"
    - "EUCLID INWARD CARRIER MUST BE PRESENT EUCLID INWARD CARRIER MUST BE PRESENT|T24001248"
    - "EUCLID OUTWARD NON-GENERIC CARRIER MUST BE PRESENT EUCLID OUTWARD NON-GENERIC
    CARRIER MUST BE PRESENT|T24001249"
    - "EUCLID USER NUMBER NOT ENTERED EUCLID USER NUMBER NOT ENTERED|T24001250"
    - "Event & Missing for the Property & Event & Missing for the Property &|T24001251"
    - "Event & Missing in Allocation rule & Event & Missing in Allocation rule &|T24001252"
    - "EVENT NOT ALLOWED EVENT NOT ALLOWED|T24001253"
    - "EVENT NOT LINKED TO THIS ACCOUNT GROUP EVENT NOT LINKED TO THIS ACCOUNT GROUP|T24001254"
    - "EXCEEDS THE MAX NO SEQ EXCEEDS THE MAX NO SEQ|T24001255"
    - "EXCEEDS THE MAXIMUM RATE EXCEEDS THE MAXIMUM RATE|T24001256"
    - "Exceeds Txn Amount Allowed (&) for Period by (&) Exceeds Txn Amount Allowed
    (&) for Period by (&)|T24001258"
    - "Exclusion not for collateral Exclusion not for collateral|T24001261"
    - "Existing Account should not be removed Existing Account should not be removed|T24001263"
    - "EXISTING CURRENCIES CANT BE CHANGED EXISTING CURRENCIES CANT BE CHANGED|T24001264"
    - "Exceeds the Maximum Tax liability Percentage for the role Exceeds the Maximum
    Tax liability Percentage for the role|T24001257"
    - "Exception API can be specified only if Status is specified Exception API can
    be specified only if Status is specified|T24001259"
    - "Exception Rule can be specified only if Status is specified Exception Rule can
    be specified only if Status is specified|T24001260"
    - "Execution value for customer - same as collateral type record Execution value
    for customer - same as collateral type record|T24001262"
    - "Expiry Date & Less Than Version Date & Expiry Date & Less Than Version Date
    &|T24001265"
    - "Expiry date cannot be before Value date Expiry date cannot be before Value date|T24001266"
    - "Expiry date should be <= next payment date Expiry date should be <= next payment
    date|T24001267"
    - "Expiry date should be after arrangement start date Expiry date should be after
    arrangement start date|T24001268"
    - "EXPIRY DATE SHOULD NOT BE BEFORE AVAILABLE DATE EXPIRY DATE SHOULD NOT BE BEFORE
    AVAILABLE DATE|T24001269"
    - "Expiry date should not be before start date Expiry date should not be before
    start date|T24001270"
    - "Exposure date cannot be less than today Exposure date cannot be less than today|T24001271"
    - "Exposure dates duplicated Exposure dates duplicated|T24001273"
    - "Exposure dates missing Exposure dates missing|T24001274"
    - "EXPOSURE KEY NOIT ALLOWED FOR DEBIT TYPES EXPOSURE KEY NOIT ALLOWED FOR DEBIT
    TYPES|T24001275"
    - "EXPOSURE KEY NOT ALLOWED FOR DEBIT TYPES EXPOSURE KEY NOT ALLOWED FOR DEBIT
    TYPES|T24001276"
    - "EXTEND.CYCLE is not allowed for this Payment Type EXTEND.CYCLE is not allowed
    for this Payment Type|T24001277"
    - "EXTERNAL ACCOUNT ONLY ALLOWED FOR NOSTRO EXTERNAL ACCOUNT ONLY ALLOWED FOR NOSTRO|T24001278"
    - "EXTERNAL ACCOUNTS NOT ALLOWED EXTERNAL ACCOUNTS NOT ALLOWED|T24001279"
    - "External DD processing fields are not set in DD.PARAM External DD processing
    fields are not set in DD.PARAM|T24001280"
    - "FIELD IS MANDATORY BY DEFAULT FIELD IS MANDATORY BY DEFAULT|T24001299"
    - "FIELD IS ONLY SINGLE VALUED FIELD IS ONLY SINGLE VALUED|T24001300"
    - "FIELD MANDATORY FOR CARRIER FIELD MANDATORY FOR CARRIER|T24001301"
    - "FIELD MUST BE 1 OR BLANK FOR CARRIER FIELD MUST BE 1 OR BLANK FOR CARRIER|T24001302"
    - "FIELD MUST BE BLANK FOR CARRIER FIELD MUST BE BLANK FOR CARRIER|T24001304"
    - "FIELD MUST BE BLANK FOR EUCLID FIELD MUST BE BLANK FOR EUCLID|T24001305"
    - "FIELD MUST BE PRESENT IN DD.ITEM FIELD MUST BE PRESENT IN DD.ITEM|T24001308"
    - "FIELD MUST EXIST IN FIELD.NAME FIELD MUST EXIST IN FIELD.NAME|T24001309"
    - "FIELD MUST NOT BE BLANK FIELD MUST NOT BE BLANK|T24001310"
    - "Field name ( & ) not in message table# & Field name ( & ) not in message table#
    &|T24001311"
    - "Duplicate Property and Currency Duplicate Property and Currency|T24001092"
    - "DUPLICATE STMT ENTRY ID NOT PERMITTED DUPLICATE STMT ENTRY ID NOT PERMITTED|T24001093"
    - "DUPLICATE SYSTEM ID DUPLICATE SYSTEM ID|T24001094"
    - "DUPLICATE VALUE NOT ALLOWED DUPLICATE VALUE NOT ALLOWED|T24001095"
    - "DUPLICATE VALUES NOT ALLOWED FOR THIS PAYMENT TYPE DUPLICATE VALUES NOT ALLOWED
    FOR THIS PAYMENT TYPE|T24001096"
    - "DUPLICATE WITH A NETWORK PASSWORD DUPLICATE WITH A NETWORK PASSWORD|T24001097"
    - "DUPLICATE DUPLICATE|T24001066"
    - "DUPLICATE(S) DUPLICATE(S)|T24001098"
    - "Each Balance type must have NEW.BAL.AMT Each Balance type must have NEW.BAL.AMT|T24001099"
    - "EC TYPE NOT ALLOWED EC TYPE NOT ALLOWED|T24001100"
    - "Effective date less than agent effective date Effective date less than agent
    effective date|T24001101"
    - "Effective date less than value date of Effective date less than value date of|T24001102"
    - "EITHER  OR ACTUAL AMOUNT MANDATORY EITHER % OR ACTUAL AMOUNT MANDATORY|T24001103"
    - "Either & or & can only be defined Either & or & can only be defined|T24001104"
    - "EITHER AA.PRODUCT OR CATEGORY VALUE MISSING EITHER AA.PRODUCT OR CATEGORY VALUE
    MISSING|T24001105"
    - "EITHER ACCT NO OR CUSTOMER MUST BE PRESENT EITHER ACCT NO OR CUSTOMER MUST BE
    PRESENT|T24001106"
    - "EITHER ACTIVITY OR CLASS IS MANDATORY EITHER ACTIVITY OR CLASS IS MANDATORY|T24001107"
    - "Either Arr Id OR Property List Mandatory Either Arr Id OR Property List Mandatory|T24001108"
    - "Either BL.REGISTER or BL.BATCH id accpted Either BL.REGISTER or BL.BATCH id accpted|T24001109"
    - "EITHER CHARGE DETAIL EXTRA DETAIL MUST CONTAIN INFO EITHER CHARGE DETAIL EXTRA
    DETAIL MUST CONTAIN INFO|T24001110"
    - "Either Constant or Accelerate payment type only allowed Either Constant or Accelerate payment type only allowed|T24001111"
    - "Either Discount or Amount must be input Either Discount or Amount must be input|T24001112"
    - "F.DE.I.PRI.SC RECORD NOT FOUND & F.DE.I.PRI.SC RECORD NOT FOUND &|T24001282"
    - "FAILED - MISSING LOCATION C.PROGS FAILED - MISSING LOCATION C.PROGS|T24001283"
    - "FAILED - MISSING LOCATION CPL.PROGS FAILED - MISSING LOCATION CPL.PROGS|T24001284"
    - "FAILED TO OPEN F.LOCKING FILE FAILED TO OPEN F.LOCKING FILE|T24001285"
    - "FAILED TO OPEN VOC FILE - CONTACT EBS FAILED TO OPEN VOC FILE - CONTACT EBS|T24001286"
    - "FATAL - account not nostro FATAL - account not nostro|T24001287"
    - "FATAL - cant read & with & FATAL - cant read & with &|T24001288"
    - "FATAL ERROR: CANNOT ASSIGN ID (LIMIT=&)|T24001289"
    - "FIELD ALREADY REFERENCED FIELD ALREADY REFERENCED|T24001290"
    - "Field cannot be empty, should be Y or NO Field cannot be empty, should be Y
    or NO|T24001291"
    - "FIELD CANNOT BE ENTERED FIELD CANNOT BE ENTERED|T24001292"
    - "Field cannot be modified. Field cannot be modified.|T24001293"
    - "FIELD NUMBER MUST BE INPUT FIELD NUMBER MUST BE INPUT|T24001325"
    - "FIELD OPERAND IS MANDATORY IF FIELD.NAME INPUT FIELD OPERAND IS MANDATORY IF
    FIELD.NAME INPUT|T24001326"
    - "FIELD OPERAND NOT ALLOWED IF FILE = ALL FIELD OPERAND NOT ALLOWED IF FILE =
    ALL|T24001327"
    - "FIELD OR HEADER NAME MUST BE ENTERED FIELD OR HEADER NAME MUST BE ENTERED|T24001328"
    - "FIELD RECORD.NAME.LOC MANDATORY WHEN FIELD RECORD.NAME.LOC MANDATORY WHEN|T24001329"
    - "Field Required for Swift Field Required for Swift|T24001330"
    - "FIELD SEND.MT942 ON ACCT.STMT NE Y FIELD SEND.MT942 ON ACCT.STMT NE Y|T24001331"
    - "FIELD SHOULD BE OF TYPE D FIELD SHOULD BE OF TYPE D|T24001332"
    - "FIELD TAG MISSING FIELD TAG MISSING|T24001333"
    - "Field type should be I Field type should be I|T24001334"
    - "FIELD VALUE IS MANDATORY IF FIELD.NAME INPUT FIELD VALUE IS MANDATORY IF FIELD.NAME
    INPUT|T24001335"
    - "FIELD VALUE NOT ALLOWED IF FILE = ALL FIELD VALUE NOT ALLOWED IF FILE = ALL|T24001336"
    - "fields inputted only for adjust dates fields inputted only for adjust dates|T24001338"
    - "Fields inputted only for Redefine ladder Fields inputted only for Redefine ladder|T24001339"
    - "File & does not exist File & does not exist|T24001340"
    - "File Name entered should have ID as Customer File Name entered should have ID
    as Customer|T24001341"
    - "FIELD, SEND.MSG.TYPE ON ACCT.STMT NE Y OR STP OR MOV FIELD, SEND.MSG.TYPE
    ON ACCT.STMT NE Y OR STP OR MOV|T24001337"
    - "FILE NOT ALLOWED IF SYSTEM.ID = ALL FILE NOT ALLOWED IF SYSTEM.ID = ALL|T24001342"
    - "FILL CHARACTERS ENTERED MORE THAN ONCE FILL CHARACTERS ENTERED MORE THAN ONCE|T24001343"
    - "FILL LENGTH INVALID FILL LENGTH INVALID|T24001344"
    - "Final Maturity Date cannot be less than Maturity Date Final Maturity Date cannot
    be less than Maturity Date|T24001345"
    - "Final schedule date of fixed interest cannot be greater than maturity date Final
    schedule date of fixed interest cannot be greater than maturity date|T24001346"
    - "FinalTerm is Mandatory for current Activity FinalTerm is Mandatory for current
    Activity|T24001347"
    - "FIRST 4 CHARS MUST BE ALPHA FIRST 4 CHARS MUST BE ALPHA|T24001348"
    - "FIRST 6 CHARS MUST BE ALPHA FIRST 6 CHARS MUST BE ALPHA|T24001349"
    - "First Cheque Book being issued to the Customer First Cheque Book being issued
    to the Customer|T24001350"
    - "FIRST CHEQUE NO IS REQUIRED FIRST CHEQUE NO IS REQUIRED|T24001351"
    - "FIRST ENTRY CANNOT BE NULL FIRST ENTRY CANNOT BE NULL|T24001352"
    - "FIRST LINE MUST BE FIXED LINE NO. FIRST LINE MUST BE FIXED LINE NO.|T24001353"
    - "FIRST PAGE DISPLAYED FIRST PAGE DISPLAYED|T24001354"
    - "First tier should be null - Refer limit is set First tier should be null - Refer
    limit is set|T24001355"
    - "First value must be Single Cap First value must be Single Cap|T24001356"
    - "FIRST.CHEQUE.NO MISSING FIRST.CHEQUE.NO MISSING|T24001357"
    - "Fixed amount should be mandatory Fixed amount should be mandatory|T24001358"
    - "Fixed currency record missing Fixed currency record missing|T24001359"
    - "Fixed Int Cannot Change From or To NULL on Mid Period Fixed Int Cannot Change
    From or To NULL on Mid Period|T24001360"
    - "Fixed interest not allowed for OTHER payment type Fixed interest not allowed
    for OTHER payment type|T24001361"
    - "Fixed Interest property should be part of payment schedule Fixed Interest property
    should be part of payment schedule|*********"
    - "FIXED RATE ON CCYS NOT YET ACTIVE FIXED RATE ON CCYS NOT YET ACTIVE|*********"
    - "Freq 2 must be greater than Freq 1 Freq 2 must be greater than Freq 1|*********"
    - "FREQ MANDATORY FOR THIS PREMIUM TYPE FREQ MANDATORY FOR THIS PREMIUM TYPE|*********"
    - "FREQ mandatory when FREQ.2 defined FREQ mandatory when FREQ.2 defined|*********"
    - "Fixed type of Interest cannot handle multiple payment type definition Fixed
    type of Interest cannot handle multiple payment type definition|*********"
    - "FIX-VALUE NOT ALLOWED, NEGOTIATION RULES DEFIEND FIX-VALUE NOT ALLOWED, NEGOTIATION
    RULES DEFIEND|*********"
    - "Flat amount can be defined only for term contracts Flat amount can be defined
    only for term contracts|*********"
    - "Flat rate can be defined only for term contracts Flat rate can be defined only
    for term contracts|*********"
    - "FLOOR LIMITS ARE NOT ALLOWED FLOOR LIMITS ARE NOT ALLOWED|*********"
    - "FOR AC.BALANCE.TYPE THEN SWEEP B/W AA.ACCOUNTS FOR AC.BALANCE.TYPE THEN SWEEP
    B/W AA.ACCOUNTS|*********"
    - "For component dependency For component dependency|*********"
    - "For REFER.LIMIT source balance type must be debit For REFER.LIMIT source balance
    type must be debit|*********"
    - "FOREIGN ENTRY MISSING FOREIGN ENTRY MISSING|*********"
    - "FORM TYPE NOT ON FILE FORM TYPE NOT ON FILE|*********"
    - "FORMAT CODE LENGTH INCORRECT FORMAT CODE LENGTH INCORRECT|*********"
    - "FORMAT CODE MUST BE 1 FORMAT CODE MUST BE 1|T24001379"
    - "For external products line attributes should be CCY or Single For external products
    line attributes should be CCY or Single|T24001371"
    - "For external products type should not MERGE or Trigger type For external products
    type should not MERGE or Trigger type|T24001372"
    - "For fixed type of interest repayment should be against REC. For fixed type of
    interest repayment should be against REC.|T24001373"
    - "For Restriction Code &, only the following & Codes are allowed: &|T24001375"
    - "FORMAT CODE MUST BE NUMERIC FORMAT CODE MUST BE NUMERIC|T24001380"
    - "FORMAT MUST BE & yydddNNNNN FORMAT MUST BE & yydddNNNNN|T24001381"
    - "FORMAT MUST BE 1 FOR FORMAT MODULE OF & FORMAT MUST BE 1 FOR FORMAT MODULE OF
    &|T24001382"
    - "FORMAT SHOULD BE CUSTOMER.NO*DOC.TYPE.ID FORMAT SHOULD BE CUSTOMER.NO*DOC.TYPE.ID|T24001383"
    - "FORMAT SHOULD BE NNN FORMAT SHOULD BE NNN|T24001384"
    - "FORMATTING PROGRAM DOES NOT EXIST FORMATTING PROGRAM DOES NOT EXIST|T24001385"
    - "Forward Exposure dates exist Forward Exposure dates exist|T24001387"
    - "Forward Value Date Exceeded Forward Value Date Exceeded|T24001388"
    - "Free Count is not allowed for Agent Product Line Free Count is not allowed for
    Agent Product Line|T24001389"
    - "Function not allowed for system created block Function not allowed for system
    created block|T24001417"
    - "Function not Allowed for this Activity Function not Allowed for this Activity|T24001418"
    - "ENTER APPLICATION OR ALL ENTER APPLICATION OR ALL|T24001169"
    - "Enter atleast one. Property or Property class Enter atleast one. Property or
    Property class|T24001170"
    - "ENTER CORRESPONDING MSG.FIELD.NAME FIRST ENTER CORRESPONDING MSG.FIELD.NAME
    FIRST|T24001171"
    - "ENTER CUSTOMER.ID OR ACCOUNT OFFICER ENTER CUSTOMER.ID OR ACCOUNT OFFICER|T24001172"
    - "ENTER D OR P ENTER D OR P|T24001173"
    - "Enter DOC.SEQUENCE NO Enter DOC.SEQUENCE NO|T24001174"
    - "ENTER THE CUSTOMER NO ENTER THE CUSTOMER NO|T24001198"
    - "ENTER TYPE.APPLICATION.SUB-CLASSIFICATION ENTER TYPE.APPLICATION.SUB-CLASSIFICATION|T24001199"
    - "Enter Valid Contingent Account Enter Valid Contingent Account|T24001200"
    - "Enter valid customer limit record Enter valid customer limit record|T24001201"
    - "Enter valid product group for product line mentioned Enter valid product group for product line mentioned|T24001202"
    - "ERROR - Delivery product does not exist ERROR - Delivery product does not exist|T24001224"
    - "ERROR - FIRST CHARACTER MUST BE R ERROR - FIRST CHARACTER MUST BE R|T24001225"
    - "ERROR - TELEX NUMBER MUST BE PRESENT ERROR - TELEX NUMBER MUST BE PRESENT|T24001226"
    - "ERROR : & IS NOT DEEP ENOUGH.%|T24001227"
    - "ERROR : & IS NOT WIDE ENOUGH.%|T24001228"
    - "ERROR : & TOO SMALL.|T24001229"
    - "ERROR : UNKNOWN SORT FIELD.|T24001230"
    - "ERROR FIELD & . & ( & ) & ERROR FIELD & . & ( & ) &|T24001231"
    - "GROUP CAPIT AND DEBIT INT NOT VALID GROUP CAPIT AND DEBIT INT NOT VALID|T24001447"
    - "GROUP CAPITALISATION NOT VALID GROUP CAPITALISATION NOT VALID|T24001448"
    - "GROUP CREDIT AND DEBIT INT NOT VALID GROUP CREDIT AND DEBIT INT NOT VALID|T24001449"
    - "GROUP CREDIT INTEREST NOT VALID GROUP CREDIT INTEREST NOT VALID|T24001450"
    - "From Value Yes, it can only change to No and not to Null From Value Yes, it
    can only change to No and not to Null|T24001407"
    - "FULL AMOUNT IS MATCHED FULL AMOUNT IS MATCHED|T24001410"
    - "Full commited amount must be deposited Full commited amount must be deposited|T24001411"
    - "Full commited amount must be redeemed Full commited amount must be redeemed|T24001412"
    - "Full committed amount must be disbursed Full committed amount must be disbursed|T24001413"
    - "Full Disbursement not allowed Full Disbursement not allowed|T24001414"
    - "Full redeemed amount must be paid out Full redeemed amount must be paid out|T24001415"
    - "Function is not allowed for the value defined Function is not allowed for the
    value defined|T24001416"
    - "Function Not Allowed for this Application Function Not Allowed for this Application|T24001419"
    - "Function not allowed for this record Function not allowed for this record|T24001427"
    - "FUNCTION NOT ALLOWED IN THIS FILE FUNCTION NOT ALLOWED IN THIS FILE|T24001428"
    - "FUNCTION NOT ALLOWED RECORD AT FINAL STATE FUNCTION NOT ALLOWED RECORD AT FINAL
    STATE|T24001429"
    - "Functionality not allowed in Non-Bulk mode. Use Browser Functionality not allowed
    in Non-Bulk mode. Use Browser|T24001430"
    - "FUNDS ALREADY MATCHED FUNDS ALREADY MATCHED|T24001431"
    - "FUNDS.TYPE should not be null FUNDS.TYPE should not be null|T24001432"
    - "FUNDSTYPE MISSING IN ER.PARAMETER FUNDSTYPE MISSING IN ER.PARAMETER|T24001433"
    - "Future & not allowed Future & not allowed|T24001434"
    - "Future Dated Condition Greater Than Next Condition Date Future Dated Condition
    Greater Than Next Condition Date|T24001435"
    - "Future Dated Condition lesser than prev condition Date Future Dated Condition
    lesser than prev condition Date|T24001436"
    - "FWD MVMT ONLY FOR VALUE DATED ACCOUNTING FWD MVMT ONLY FOR VALUE DATED ACCOUNTING|T24001437"
    - "Generated by activity & - cannot be processed directly Generated by activity
    & - cannot be processed directly|T24001438"
    - "GL Allocation percentage must be stated for atleast one customer GL Allocation
    percentage must be stated for atleast one customer|T24001439"
    - "GL Customer Should set to Yes GL Customer Should set to Yes|T24001440"
    - "GREATER THAN DC.TOLERANCE.RATE GREATER THAN DC.TOLERANCE.RATE|T24001441"
    - "Greater than today Greater than today|T24001444"
    - "Group Bill Type Should Be Entered Group Bill Type Should Be Entered|T24001445"
    - "GROUP CAPIT AND CREDIT INT NOT VALID GROUP CAPIT AND CREDIT INT NOT VALID|T24001446"
    - "GROUP DEBIT INT NOT VALID GROUP DEBIT INT NOT VALID|T24001451"
    - "Group Minimum Amount Should Be Greater Than 0 Group Minimum Amount Should Be
    Greater Than 0|T24001452"
    - "GROUP NOT INSTALLED - FUNCTION NOT ALLOWED GROUP NOT INSTALLED - FUNCTION NOT
    ALLOWED|T24001453"
    - "Header Id Required Header Id Required|T24001454"
    - "Hierarchy record not defined Hierarchy record not defined|T24001455"
    - "Higher version available for the latest date Higher version available for the
    latest date|T24001456"
    - "HIST FILE DAYS MUST BE PRESENT HIST FILE DAYS MUST BE PRESENT|T24001457"
    - "HIST FILE LOC MUST BE PRESENT HIST FILE LOC MUST BE PRESENT|T24001458"
    - "Hold allowed only for print carrier Hold allowed only for print carrier|T24001459"
    - "Hold mail start is not defined Hold mail start is not defined|T24001460"
    - "Hold output is not set to yes Hold output is not set to yes|T24001461"
    - "Holiday End Date is greater than or equal to Payment End Date Holiday End Date
    is greater than or equal to Payment End Date|T24001462"
    - "Holiday Number of Payments is Mandatory Holiday Number of Payments is Mandatory|T24001463"
    - "Holiday Payment Type is mandatory Holiday Payment Type is mandatory|T24001464"
    - "Holiday Start date is less than Effective Date Holiday Start date is less than
    Effective Date|T24001465"
    - "HOLIDAY TABLE MISSING FOR & HOLIDAY TABLE MISSING FOR &|T24001468"
    - "HOLIDAY TABLE MISSING HOLIDAY TABLE MISSING|T24001466"
    - "HVT Flag should be entered if category range exists HVT Flag should be entered
    if category range exists|T24001469"
    - "HVT setup allowed only for accounts product line HVT setup allowed only for
    accounts product line|T24001470"
    - "IAS.CLASSIFICATION is Mandatory for the Market Key. IAS.CLASSIFICATION is Mandatory
    for the Market Key.|T24001471"
    - "IAS.CLASSIFICATION is Mandatory for the Subtype IAS.CLASSIFICATION is Mandatory
    for the Subtype|T24001472"
    - "ID : CUSTOMER-COLLATERAL.TYPE|T24001473"
    - "ID ACCT IS A COMP ACCT ID ACCT IS A COMP ACCT|T24001474"
    - "ID ACCT IS A LIQU ACCT ID ACCT IS A LIQU ACCT|T24001475"
    - "Id already exists in AA.CONTEXT.TYPE Id already exists in AA.CONTEXT.TYPE|T24001476"
    - "Id already exists in AA.PROPERTY Id already exists in AA.PROPERTY|T24001477"
    - "ID cannot be created as EXTERNAL ID cannot be created as EXTERNAL|T24001478"
    - "ID Format: CUSTOMER-BATCH or
    CUSTOMER-PRODUCT|T24001479"
    - "ID IN FILE MISSING ID IN FILE MISSING|T24003810"
    - "ID IS A NOSTRO ACCOUNT NUMBER ID IS A NOSTRO ACCOUNT NUMBER|T24001480"
    - "If DD is defined as fixed, not possible to provide  or amount If DD is defined
    as fixed, not possible to provide % or amount|T24001490"
    - "If term is zero amount needs to be zero during takeover If term is zero amount
    needs to be zero during takeover|T24001491"
    - "IFRS not supported for Call/Notice IFRS not supported for Call/Notice|T24001492"
    - "IFRS REVALUE IS NOT SET IFRS REVALUE IS NOT SET|T24001493"
    - "Include/Exclude - Input Mandatory Include/Exclude - Input Mandatory|T24001494"
    - "INCOMING F.DE.I.MSG.SC NOT FOUND & INCOMING F.DE.I.MSG.SC NOT FOUND &|T24001495"
    - "INCOMPATIBLE GROUP CURRENCY INCOMPATIBLE GROUP CURRENCY|T24001496"
    - "INCOMPATIBLE PRODUCT DEFINITION INCOMPATIBLE PRODUCT DEFINITION|T24001497"
    - "Incompatible Type Definition Incompatible Type Definition|T24001498"
    - "Incorrect Account Number on Statement Entry & Incorrect Account Number on Statement
    Entry &|T24001499"
    - "INCORRECT ACTIVITY CLASS FOR GIVEN ACTIVITY ID INCORRECT ACTIVITY CLASS FOR
    GIVEN ACTIVITY ID|T24001500"
    - "INCORRECT CLOSE MODE INCORRECT CLOSE MODE|T24001501"
    - "INCORRECT DAY TYPE INCORRECT DAY TYPE|T24001502"
    - "INCORRECT FORMAT INCORRECT FORMAT|T24001503"
    - "INCORRECT IBLC CDS INCORRECT IBLC CDS|T24001504"
    - "INCORRECT IBLC CODE INCORRECT IBLC CODE|T24001505"
    - "Incorrect information entered. (E-167991) Incorrect information entered. (E-167991)|T24001506"
    - "Incorrect information entered.Please correct them Incorrect information entered.Please
    correct them|T24001507"
    - "INCORRECT NUMBER OF CHARACTERS INCORRECT NUMBER OF CHARACTERS|T24001508"
    - "INDEX FIELD CANNOT BE NULL INDEX FIELD CANNOT BE NULL|T24001509"
    - "F.DE.I.PRI.SC RECORD LOCKED & F.DE.I.PRI.SC RECORD LOCKED &|T24001281"
    - "FIELD DOES NOT EXIST ON DE.MESSAGE FIELD DOES NOT EXIST ON DE.MESSAGE|T24001294"
    - "FIELD DOES NOT EXIST ON FUNDS.TRANSFER FIELD DOES NOT EXIST ON FUNDS.TRANSFER|T24001295"
    - "FIELD DOES NOT EXIST ON THE FILE FIELD DOES NOT EXIST ON THE FILE|T24001296"
    - "FIELD INPUT NOT ALLOWED FOR LENDING PRODUCT LINE FIELD INPUT NOT ALLOWED FOR
    LENDING PRODUCT LINE|T24001297"
    - "FIELD INPUT NOT ALLOWED FOR SAVINGS PRODUCT LINE FIELD INPUT NOT ALLOWED FOR
    SAVINGS PRODUCT LINE|T24001298"
    - "FIELD NAME IS A RESERVED WORD FIELD NAME IS A RESERVED WORD|T24001312"
    - "Field Name is Mandatory to Input Field Name is Mandatory to Input|T24001313"
    - "FIELD NAME MANDATORY FIELD NAME MANDATORY|T24001314"
    - "Field name missing in STANDARD.SELECTION Field name missing in STANDARD.SELECTION|T24001315"
    - "FIELD NAME MUST BE ENTERED FIELD NAME MUST BE ENTERED|T24001316"
    - "FIELD NAME NOT ALLOWED IF FILE = ALL FIELD NAME NOT ALLOWED IF FILE = ALL|T24001317"
    - "FIELD NAME NOT IN FORMAT FIELD NAME NOT IN FORMAT|T24001318"
    - "FIELD NAME NOT ON MESSAGE FILE FIELD NAME NOT ON MESSAGE FILE|T24001319"
    - "FIELD NAME OR KEYWORD MISSING FIELD NAME OR KEYWORD MISSING|T24001320"
    - "Field not defined for property & Field not defined for property &|T24001321"
    - "Field not defined in & Field not defined in &|T24001322"
    - "Field not defined in AA.ARRANGEMENT.ACTIVITY Field not defined in AA.ARRANGEMENT.ACTIVITY|T24001323"
    - "Field not in correct position Field not in correct position|T24001324"
    - "Input allowed in Account Arrangement or Account Input allowed in Account Arrangement
    or Account|T24001515"
    - "Input allowed only for ACCOUNTS product line Input allowed only for ACCOUNTS
    product line|T24001516"
    - "Input allowed Only for APP Method Input allowed Only for APP Method|T24001518"
    - "INPUT ALLOWED ONLY FOR BAND TIER TYPE INPUT ALLOWED ONLY FOR BAND TIER TYPE|T24001519"
    - "Input Allowed only for Batch Disbursement Input Allowed only for Batch Disbursement|T24001520"
    - "Input allowed only for Beneficial Owner Role Input allowed only for Beneficial
    Owner Role|T24001521"
    - "Input allowed only for blocks with expiry date Input allowed only for blocks
    with expiry date|T24001522"
    - "INPUT ALLOWED ONLY FOR COVER INPUT ALLOWED ONLY FOR COVER|T24001523"
    - "Input allowed only for Customer Visit Activity Input allowed only for Customer
    Visit Activity|T24001524"
    - "Input Allowed Only for Forward Dated Type Properties Input Allowed Only for
    Forward Dated Type Properties|T24001525"
    - "Input allowed only for future dated blocks Input allowed only for future dated
    blocks|T24001526"
    - "Input Allowed Only For INTEREST Property Class Input Allowed Only For INTEREST
    Property Class|T24001527"
    - "Input allowed only for Lending Products Input allowed only for Lending Products|T24001528"
    - "Input Allowed only for LOWEST calc type Input Allowed only for LOWEST calc type|T24001529"
    - "Input Allowed only for monthly calculation Input Allowed only for monthly calculation|T24001530"
    - "INPUT ALLOWED ONLY FOR MSG 102 INPUT ALLOWED ONLY FOR MSG 102|T24001531"
    - "INPUT ALLOWED ONLY FOR MT102 INPUT ALLOWED ONLY FOR MT102|T24001532"
    - "Input allowed only for PERCENTAGE Input allowed only for PERCENTAGE|T24001533"
    - "INPUT ALLOWED ONLY FOR PRINT CARRIER INPUT ALLOWED ONLY FOR PRINT CARRIER|T24001534"
    - "Input Allowed only when Charge type is CALCULATED Input Allowed only when Charge
    type is CALCULATED|T24001541"
    - "Input Allowed only when charge type is FIXED Input Allowed only when charge
    type is FIXED|T24001542"
    - "Input allowed only when method is Fixed Input allowed only when method is Fixed|T24001543"
    - "Input allowed only when the charge is set to amortize Input allowed only when
    the charge is set to amortize|T24001544"
    - "Input allowed only when the status is MAT. Input allowed only when the status
    is MAT.|T24001545"
    - "Input allowed only with Cross Currency Input allowed only with Cross Currency|T24001546"
    - "INPUT ALLOWED TO REVOKE ONLY STOPPED AMOUNT INPUT ALLOWED TO REVOKE ONLY STOPPED
    AMOUNT|T24001547"
    - "Input allowed when bill settlement is BILL.TOTAL Input allowed when bill settlement
    is BILL.TOTAL|T24001548"
    - "Input any of the Joint Holders Input any of the Joint Holders|T24001549"
    - "Input any one field. TAX.CORR.CAP or CORR.AT.CAP.DATE. Input any one field.
    TAX.CORR.CAP or CORR.AT.CAP.DATE.|T24001550"
    - "INPUT AT LEAST ONE RATE VALUE FIELD INPUT AT LEAST ONE RATE VALUE FIELD|T24001551"
    - "INPUT BATCH NO. MISSING INPUT BATCH NO. MISSING|T24001552"
    - "Input Both Agent id and Agent Arrangement Input Both Agent id and Agent Arrangement|T24001553"
    - "Input CHANGE.PRODUCT or REBATCH Input CHANGE.PRODUCT or REBATCH|T24001554"
    - "INPUT DISALLOWED INPUT DISALLOWED|T24001555"
    - "INPUT EITHER ACTIVITY CLASS OR ACTIVITY INPUT EITHER ACTIVITY CLASS OR ACTIVITY|T24001556"
    - "Input Either Application or Version Input Either Application or Version|T24001557"
    - "Input Either Customer field or Account field Input Either Customer field or
    Account field|T24001558"
    - "INPUT EITHER DISCOUNT OR PREMIUM INPUT EITHER DISCOUNT OR PREMIUM|T24001559"
    - "INPUT EITHER DISCOUNT/PREMIUM OR FLAT AMOUNT INPUT EITHER DISCOUNT/PREMIUM OR
    FLAT AMOUNT|T24001560"
    - "INPUT EITHER MAX/MIN OR FLAT AMOUNT INPUT EITHER MAX/MIN OR FLAT AMOUNT|T24001561"
    - "Input either one Drawback Type/Defer Days Input either one Drawback Type/Defer
    Days|T24001562"
    - "Input either one Product Line/Group/Product Input either one Product Line/Group/Product|T24001563"
    - "Input Either Opportunity or Campaign generator ID Input Either Opportunity or
    Campaign generator ID|T24001564"
    - "INPUT EITHER TAX CODE OR TAX CONDITION INPUT EITHER TAX CODE OR TAX CONDITION|T24001565"
    - "INPUT EITHER TIER.AMOUNT OR TIER.PERCENT INPUT EITHER TIER.AMOUNT OR TIER.PERCENT|T24001566"
    - "Input end date for expired document Input end date for expired document|T24001567"
    - "INPUT FIELD 13-17,19 OR 20 MISSING INPUT FIELD 13-17,19 OR 20 MISSING|T24001568"
    - "INPUT FIELD 14-17 MISSING INPUT FIELD 14-17 MISSING|T24001569"
    - "INPUT FIELD 23-37 MISSING INPUT FIELD 23-37 MISSING|T24001570"
    - "INPUT FIELD 2-5 MISSING INPUT FIELD 2-5 MISSING|T24001571"
    - "INPUT FIELD 7 OR 8 MISSING INPUT FIELD 7 OR 8 MISSING|T24001572"
    - "INPUT FILE MANDATORY INPUT FILE MANDATORY|T24001573"
    - "Input I is allowed only for internal accounts Input I is allowed only for internal
    accounts|T24001574"
    - "INPUT IN RANGE 90 - 99 NOT ALLOWED INPUT IN RANGE 90 - 99 NOT ALLOWED|T24001575"
    - "Input is allowed for INTEREST.PERIOD option only Input is allowed for INTEREST.PERIOD
    option only|T24001576"
    - "Input is allowed for payment type with Account Property Input is allowed for
    payment type with Account Property|T24001577"
    - "Input is allowed only for CAPITALISE payment method Input is allowed only for
    CAPITALISE payment method|T24001578"
    - "Input is allowed only if bills are combined Input is allowed only if bills are
    combined|T24001579"
    - "Input is allowed only to DUE payment method Input is allowed only to DUE payment
    method|T24001580"
    - "Input is allowed only to Payment Bills. Input is allowed only to Payment Bills.|T24001581"
    - "Input is allowed only to the Actual Payment type. Input is allowed only to the
    Actual Payment type.|T24001582"
    - "Input is allowed only with PRICING.RULES property Input is allowed only with
    PRICING.RULES property|T24001583"
    - "INPUT IS MANDATORY FOR AC.BALANCE.TYPE SWEEP INPUT IS MANDATORY FOR AC.BALANCE.TYPE
    SWEEP|T24001584"
    - "Input is mandatory for MANUAL pricing Input is mandatory for MANUAL pricing|T24001585"
    - "INPUT IS MANDATORY IF FIELD.NAME HAS BEEN ENTERED INPUT IS MANDATORY IF FIELD.NAME
    HAS BEEN ENTERED|T24001586"
    - "Input is mandatory when RC condition is defined Input is mandatory when RC condition
    is defined|T24001587"
    - "Input mandatory for Automatic/Automatic.Or.Manual Input mandatory for Automatic/Automatic.Or.Manual|T24001601"
    - "Input Mandatory for Best Type Input Mandatory for Best Type|T24001602"
    - "Input mandatory for field WAIVING.CM Input mandatory for field WAIVING.CM|T24001603"
    - "Input mandatory for Interest period type Input mandatory for Interest period
    type|T24001604"
    - "Input mandatory for INTERNAL request type Input mandatory for INTERNAL request
    type|T24001605"
    - "Input mandatory for METHOD Automatic Input mandatory for METHOD Automatic|T24001606"
    - "Input Mandatory for other incomplete fields Input Mandatory for other incomplete
    fields|T24001608"
    - "Input Mandatory for PERIOD.TYPE other than LIFE Input Mandatory for PERIOD.TYPE
    other than LIFE|T24001609"
    - "Input mandatory for plan method NONE Input mandatory for plan method NONE|T24001610"
    - "Input mandatory for type PERIOD Input mandatory for type PERIOD|T24001611"
    - "Input mandatory if TYPE is specified Input mandatory if TYPE is specified|T24001612"
    - "Input mandatory if value in VAL.DATE.SYS.ID Input mandatory if value in VAL.DATE.SYS.ID|T24001613"
    - "Input Mandatory since Recourse is Partial Input Mandatory since Recourse is
    Partial|T24001614"
    - "Input mandatory when BILL.SETTLEMENT is BILL.TOTAL Input mandatory when BILL.SETTLEMENT
    is BILL.TOTAL|T24001615"
    - "Input Mandatory when charge type is Calculated Input Mandatory when charge type
    is Calculated|T24001616"
    - "Forward dated balance exists. Not allow to do closure activity Forward dated
    balance exists. Not allow to do closure activity|T24001386"
    - "FREQ MUST BE PRESENT IN CYCLE FREQUENCY FREQ MUST BE PRESENT IN CYCLE FREQUENCY|T24001393"
    - "FREQ.1 DUE DATE PRIOR TO TODAY FREQ.1 DUE DATE PRIOR TO TODAY|T24001394"
    - "FREQ.2 DUE DATE PRIOR TO TODAY FREQ.2 DUE DATE PRIOR TO TODAY|T24001395"
    - "Frequency can not be left blank Frequency can not be left blank|T24001396"
    - "Frequency duplicated Frequency duplicated|T24001397"
    - "Frequency is not null if interest property is defined Frequency is not null
    if interest property is defined|T24001398"
    - "Frequency less than Original Term date Frequency less than Original Term date|T24001399"
    - "Frequency Mandatory for non linear accruals Frequency Mandatory for non linear
    accruals|T24001400"
    - "FREQUENCY MUST BE NUMERIC 1 OR 2 FREQUENCY MUST BE NUMERIC 1 OR
    2|T24001401"
    - "FREQUENCY MUST BE WEEK1, M0131, OR BSNSS FREQUENCY MUST BE WEEK1, M0131, OR
    BSNSS|T24001402"
    - "FREQUENCY MUST GT TERM END DATE FREQUENCY MUST GT TERM END DATE|T24001403"
    - "FREQUENCY NOT ALLOWED AFTER MATURITY DATE FREQUENCY NOT ALLOWED AFTER MATURITY
    DATE|T24001404"
    - "FREQUENCY REQUIRED FREQUENCY REQUIRED|T24001405"
    - "FROM & TO REGISTER CANNOT BE THE SAME FROM & TO REGISTER CANNOT BE THE SAME|T24001406"
    - "FT NOT INCOMING FT NOT INCOMING|T24001408"
    - "FT REF ALREADY USED FT REF ALREADY USED|T24001409"
    - "GREATER THAN MAX 999 GREATER THAN MAX 999|T24001442"
    - "Greater than the MAX amount Greater than the MAX amount|T24001443"
    - "Input is not allowed when type is not defined Input is not allowed when type
    is not defined|T24001593"
    - "Input is not allowed Input is not allowed|T24001590"
    - "Input is not an Interest Property Input is not an Interest Property|T24001594"
    - "Input is not Opposite type of EXP Input is not Opposite type of EXP|T24001595"
    - "INPUT LOC.REF.APP FIRST INPUT LOC.REF.APP FIRST|T24001596"
    - "INPUT LOC.REF.NAME FIRST INPUT LOC.REF.NAME FIRST|T24001597"
    - "Input manadatory for type DATE Input manadatory for type DATE|T24001598"
    - "Input Mandatory for & calc type Input Mandatory for & calc type|T24001600"
    - "Input Mandatory When Charge type is FIXED Input Mandatory When Charge type is
    FIXED|T24001617"
    - "Input mandatory when FREQ.NO defined Input mandatory when FREQ.NO defined|T24001618"
    - "Input mandatory when PERIOD.TYPE is ACTUAL Input mandatory when PERIOD.TYPE
    is ACTUAL|T24001619"
    - "Input Mandatory when RETURN.AT.SOD is YES Input Mandatory when RETURN.AT.SOD
    is YES|T24001620"
    - "Input Mandatory when Taxable is set to Yes Input Mandatory when Taxable is set
    to Yes|T24001621"
    - "Input mandatory with APPLN.TXN.REF Input mandatory with APPLN.TXN.REF|T24001622"
    - "INPUT MANDATORY INPUT MANDATORY|T24001599"
    - "Input Missing - DECIS.FIELD Input Missing - DECIS.FIELD|T24001631"
    - "Input needed When PROCESS is ONLY.EXTRACT Input needed When PROCESS is ONLY.EXTRACT|T24001647"
    - "INPUT NO NOT ALLOWED WHEN DEFAULT.NEGOTIABLE IS NO INPUT NO NOT ALLOWED WHEN
    DEFAULT.NEGOTIABLE IS NO|T24001648"
    - "Input Not allowed for & Calc type Input Not allowed for & Calc type|T24001655"
    - "INPUT NOT ALLOWED FOR & SOURCE TYPE INPUT NOT ALLOWED FOR & SOURCE TYPE|T24001656"
    - "INPUT NOT ALLOWED FOR & INPUT NOT ALLOWED FOR &|T24001654"
    - "Input not allowed for ACCOUNT property Input not allowed for ACCOUNT property|T24001657"
    - "Input not allowed for activities other than takeover Input not allowed for activities
    other than takeover|T24001658"
    - "INPUT NOT ALLOWED FOR ALREADY STOPPED CHEQUES INPUT NOT ALLOWED FOR ALREADY
    STOPPED CHEQUES|T24001659"
    - "Input not allowed for amort end / clawback activities Input not allowed for
    amort end / clawback activities|T24001660"
    - "INPUT NOT ALLOWED FOR BACK VALUE PROCESS INPUT NOT ALLOWED FOR BACK VALUE PROCESS|T24001661"
    - "INPUT NOT ALLOWED FOR CALC TYPE & INPUT NOT ALLOWED FOR CALC TYPE &|T24001662"
    - "Input Not Allowed for Constraint Type DATE Input Not Allowed for Constraint
    Type DATE|T24001663"
    - "INPUT NOT ALLOWED FOR COVER INPUT NOT ALLOWED FOR COVER|T24001664"
    - "INPUT NOT ALLOWED FOR DEBIT INPUT NOT ALLOWED FOR DEBIT|T24001665"
    - "Input not allowed for FIXED charge type Input not allowed for FIXED charge type|T24001666"
    - "Input not allowed for FLAT calc types Input not allowed for FLAT calc types|T24001667"
    - "Input Not Allowed For HVT Account Input Not Allowed For HVT Account|T24001668"
    - "Input not allowed for non contingent accounts Input not allowed for non contingent
    accounts|T24001669"
    - "Input not allowed for non-INTERNAL request type Input not allowed for non-INTERNAL
    request type|T24001670"
    - "Input not allowed for PERIOD.TYPE other than ACTUAL Input not allowed for PERIOD.TYPE
    other than ACTUAL|T24001671"
    - "Input not allowed for PERIODIC.CHARGE property class Input not allowed for PERIODIC.CHARGE
    property class|T24001672"
    - "Input not allowed for PL category Input not allowed for PL category|T24001673"
    - "Input not allowed for property type ACCRUAL.BY.BILLS Input not allowed for property
    type ACCRUAL.BY.BILLS|T24001674"
    - "Input not allowed for settlement Input not allowed for settlement|T24001675"
    - "Input not allowed for single valued DECISN.FIELD Input not allowed for single
    valued DECISN.FIELD|T24001676"
    - "INPUT NOT ALLOWED FOR THIS PAYMENT TYPE INPUT NOT ALLOWED FOR THIS PAYMENT TYPE|T24001677"
    - "INPUT NOT ALLOWED FOR THIS TYPE INPUT NOT ALLOWED FOR THIS TYPE|T24001678"
    - "Input not allowed if Exclude is set Input not allowed if Exclude is set|T24001679"
    - "INPUT NOT ALLOWED IF FILE EQUALS ALL INPUT NOT ALLOWED IF FILE EQUALS ALL|T24001680"
    - "INPUT NOT ALLOWED IF SYSTEM.ID EQUALS ALL INPUT NOT ALLOWED IF SYSTEM.ID
    EQUALS ALL|T24001681"
    - "Input Not allowed if TARGET.PRODUCE is NULL Input Not allowed if TARGET.PRODUCE
    is NULL|T24001682"
    - "Input Not allowed since decision = & Input Not allowed since decision = &|T24001683"
    - "Input Not allowed to define more than one Customer Input Not allowed to define
    more than one Customer|T24001684"
    - "INPUT NOT ALLOWED UNLESS FIELD.NAME IS INPUT INPUT NOT ALLOWED UNLESS FIELD.NAME
    IS INPUT|T24001685"
    - "Input not allowed when BILL.SETTLEMENT is null Input not allowed when BILL.SETTLEMENT
    is null|T24001686"
    - "Input not allowed when charge routine is defined Input not allowed when charge
    routine is defined|T24001687"
    - "Input not allowed when Source Field or Source Type defined Input not allowed
    when Source Field or Source Type defined|T24001688"
    - "Input Not allowed when Source Type is inputted Input Not allowed when Source
    Type is inputted|T24001689"
    - "Input Not Allowed when Taxable is Not set Input Not Allowed when Taxable is
    Not set|T24001690"
    - "Input Not Allowed When the corresponding & is null Input Not Allowed When the
    corresponding & is null|T24001691"
    - "INPUT NOT ALLOWED WHEN UPDATE MANDATE IS NO INPUT NOT ALLOWED WHEN UPDATE MANDATE
    IS NO|T24001692"
    - "Input not allowed without LAST.UPD.APPLN Input not allowed without LAST.UPD.APPLN|T24001693"
    - "INPUT NOT ALLOWED WITHOUT NET.OD.APPL INPUT NOT ALLOWED WITHOUT NET.OD.APPL|T24001694"
    - "Input not allowed Input not allowed|T24001649"
    - "Input not allowed, limit not managed by AA Input not allowed, limit not managed
    by AA|T24001695"
    - "Input not allowed, when CONSOLIDATE.ENTRIES is Yes Input not allowed, when CONSOLIDATE.ENTRIES
    is Yes|T24001696"
    - "INPUT NOT NULL INPUT NOT NULL|T24001697"
    - "Input Only Allowed at Arrangement Level Input Only Allowed at Arrangement Level|T24001698"
    - "Input only allowed for an Automatic selection Input only allowed for an Automatic
    selection|T24001699"
    - "Input only allowed for BEST selection method Input only allowed for BEST selection
    method|T24001700"
    - "INPUT ONLY ALLOWED FOR SERIAL CONTROL TYPE INPUT ONLY ALLOWED FOR SERIAL
    CONTROL TYPE|T24001701"
    - "Input Only allowed When property type is Rebate Unamort Input Only allowed When
    property type is Rebate Unamort|T24001702"
    - "Input only for ICA accounts Input only for ICA accounts|T24001703"
    - "INPUT ONLY FOR LD INPUT ONLY FOR LD|T24001704"
    - "INPUT ONLY IF CR.ZERO.IN.BAL SET INPUT ONLY IF CR.ZERO.IN.BAL SET|T24001705"
    - "INPUT ONLY IF CR2.ZERO.IN.BAL SET INPUT ONLY IF CR2.ZERO.IN.BAL SET|T24001706"
    - "Input only tolerance percent or amount/currency Input only tolerance percent
    or amount/currency|T24001707"
    - "INSUFFICIENT.FUNDS INSUFFICIENT.FUNDS|T24003817"
    - "INT CCY MKT & not set up for & INT CCY MKT & not set up for &|T24001726"
    - "Int Rate and Int Liq Account not allowed for collateral Int Rate and Int Liq
    Account not allowed for collateral|T24001727"
    - "Int Suspension not allowed for this product type Int Suspension not allowed
    for this product type|T24001728"
    - "INT.CHARGE.CCY MISSING INT.CHARGE.CCY MISSING|T24001729"
    - "INT.CHRG.BAL.TYPE MISSING INT.CHRG.BAL.TYPE MISSING|T24001730"
    - "INT.METHOD differs from first Disbursement INT.METHOD differs from first Disbursement|T24001731"
    - "INT.NO.BOOKING is changed TODAY in ACCOUNT INT.NO.BOOKING is changed TODAY in
    ACCOUNT|T24001732"
    - "ID MUST BE A VALID LIMIT REFERENCE ID MUST BE A VALID LIMIT REFERENCE|T24001481"
    - "ID MUST BE COMPANY CUST OR ACCT NO ID MUST BE COMPANY CUST OR ACCT NO|T24001482"
    - "ID NOT ALLOWED WITH ADDRESS ID NOT ALLOWED WITH ADDRESS|T24001483"
    - "ID SHOULD BE A VALID CURRENCY ID SHOULD BE A VALID CURRENCY|T24001484"
    - "ID SHOULD BE ONLY SYSTEM OR LEAD COMPANY ID SHOULD BE ONLY SYSTEM OR LEAD COMPANY|T24001485"
    - "Id should be prefixed with & Id should be prefixed with &|T24001486"
    - "ID Should begin with Valid Class Type Record like PROPERTY.CLASS , FORMLET.CLASS
    etc ID Should begin with Valid Class Type Record like PROPERTY.CLASS , FORMLET.CLASS
    etc|T24001487"
    - "ID USED IN ANOTHER COMPANY ID USED IN ANOTHER COMPANY|T24001489"
    - "INDEX NOT FOUND IN SYSTEM RECORD INDEX NOT FOUND IN SYSTEM RECORD|T24001510"
    - "Info Info|T24001511"
    - "INPUT < TRANS.CODE.FROM INPUT < TRANS.CODE.FROM|T24001512"
    - "INPUT ALLOWED FOR 101 MESSAGE ONLY INPUT ALLOWED FOR 101 MESSAGE ONLY|T24001513"
    - "Input allowed for tax suspended account Input allowed for tax suspended account|T24001514"
    - "INPUT ALLOWED ONLY FOR PROXY CUSTOMER INPUT ALLOWED ONLY FOR PROXY CUSTOMER|T24001535"
    - "Input allowed only for rule break charge Input allowed only for rule break charge|T24001536"
    - "Input allowed only for Settlement Input allowed only for Settlement|T24001537"
    - "Input allowed only for SUSPENSE account Input allowed only for SUSPENSE account|T24001538"
    - "Input allowed only for SUSPENSE accounts Input allowed only for SUSPENSE accounts|T24001539"
    - "INPUT ALLOWED ONLY IF IX PRODUCT IS INSTALLED INPUT ALLOWED ONLY IF IX
    PRODUCT IS INSTALLED|T24001540"
    - "INVALID ID - ENTER COMPANY . MESSAGE TYPE INVALID ID - ENTER COMPANY . MESSAGE
    TYPE|T24001929"
    - "INVALID ID - MUST BE SYSTEM INVALID ID - MUST BE SYSTEM|T24001930"
    - "INTERCO PARAMETER REQD FOR INPUT INTERCO PARAMETER REQD FOR INPUT|T24001736"
    - "Interest amt greater than payment amt Interest amt greater than payment amt|T24001737"
    - "Interest and account property only allowed for fixed interest Interest and account
    property only allowed for fixed interest|T24001738"
    - "INTEREST BASIS NE A INTEREST BASIS NE A|T24001739"
    - "INTEREST BASIS NE G INTEREST BASIS NE G|T24001740"
    - "INTEREST CANNOT BE COMPOUNDING FOR ANNUITY PAYMENTS INTEREST CANNOT BE COMPOUNDING
    FOR ANNUITY PAYMENTS|T24001741"
    - "INTEREST DAY BASIS REQUIRED INTEREST DAY BASIS REQUIRED|T24001742"
    - "Interest Details should not be null Interest Details should not be null|T24001743"
    - "Interest is not null if frequency is defined Interest is not null if frequency
    is defined|T24001744"
    - "Interest mode fixed is not allowed when accounting mode is Advanced Interest
    mode fixed is not allowed when accounting mode is Advanced|T24001745"
    - "Interest mode must be FIXED for UPFRONT PROFIT Interest mode must be FIXED for
    UPFRONT PROFIT|T24001746"
    - "Interest not allowed for FLAT.AMOUNT Interest not allowed for FLAT.AMOUNT|T24001747"
    - "INTEREST ONLY FOR VARIABLE TYPE INTEREST ONLY FOR VARIABLE TYPE|T24001748"
    - "Interest property class only allowed as mandatory Interest property class only
    allowed as mandatory|T24001749"
    - "Interest rate allowed only for Fixed contracts Interest rate allowed only for
    Fixed contracts|T24001750"
    - "Interest Rate and Int Liq Account are mandatory Interest Rate and Int Liq Account
    are mandatory|T24001751"
    - "Interest Rate Mandatory For Constant Payment Type Interest Rate Mandatory For
    Constant Payment Type|T24001752"
    - "INTERFACE NOT ALLOWED IF CARRIER IS NOT GENERIC INTERFACE NOT ALLOWED IF CARRIER
    IS NOT GENERIC|T24001753"
    - "INTERNAL ACCOUNT MISSING FOR ACCOUNT CLASS & INTERNAL ACCOUNT MISSING FOR ACCOUNT
    CLASS &|T24001755"
    - "INTERNAL ACCOUNT INTERNAL ACCOUNT|T24001754"
    - "INTRA.DAY MUST BE SINGLE INTRA.DAY MUST BE SINGLE|T24001757"
    - "INVALID - ID MUST BE SYSTEM OR COVER INVALID - ID MUST BE SYSTEM OR COVER|T24001758"
    - "INVALID - TRAILER NOT YET DEFINED INVALID - TRAILER NOT YET DEFINED|T24001759"
    - "INVALID & ACTIVITY INVALID & ACTIVITY|T24001760"
    - "Invalid Account Category - Invalid ID Invalid Account Category - Invalid ID|T24001761"
    - "INVALID ACCOUNT CURRENCY INVALID ACCOUNT CURRENCY|T24001762"
    - "Internal booking not set because Internal category not set in ACCOUNTING condition
    Internal booking not set because Internal category not set in ACCOUNTING condition|T24001756"
    - "Invalid account error. Please contact bank (E-135650) Invalid account error.
    Please contact bank (E-135650)|T24001766"
    - "Invalid Account error. Please contact bank (E-135659) Invalid Account error.
    Please contact bank (E-135659)|T24001767"
    - "Invalid account error. Please contact bank Invalid account error. Please contact
    bank|T24001763"
    - "Invalid account error. Please contact bank. Invalid account error. Please contact
    bank.|T24001768"
    - "Invalid Account Number & Invalid Account Number &|T24001771"
    - "INVALID ACCOUNT NUMBER FORMAT INVALID ACCOUNT NUMBER FORMAT|T24001772"
    - "INVALID ACCOUNT NUMBER INVALID ACCOUNT NUMBER|T24001769"
    - "INVALID ACCOUNT INVALID ACCOUNT|T24003796"
    - "Invalid Account. Please Contact Bank (E-109350) Invalid Account. Please Contact
    Bank (E-109350)|T24001773"
    - "Invalid account.different category code Invalid account.different category code|T24001774"
    - "Invalid Account.Please contact Bank (E-105883) Invalid Account.Please contact
    Bank (E-105883)|T24001775"
    - "Invalid account.Please contact bank for further details Invalid account.Please
    contact bank for further details|T24001776"
    - "Invalid action for class type Invalid action for class type|T24001783"
    - "INVALID ACTION FOR PROPERTY CLASS & INVALID ACTION FOR PROPERTY CLASS &|T24001785"
    - "INVALID ACTION FOR PROPERTY CLASS INVALID ACTION FOR PROPERTY CLASS|T24001784"
    - "Invalid activity for the Arrangement status Invalid activity for the Arrangement
    status|T24001786"
    - "Invalid Activity on or before Last Payment Date Invalid Activity on or before
    Last Payment Date|T24001787"
    - "Invalid Activity Type in Activity.Class Invalid Activity Type in Activity.Class|T24001788"
    - "Invalid activity, activity type should be closure type Invalid activity, activity
    type should be closure type|T24001789"
    - "INVALID ACTIVITY.CLASS ID INVALID ACTIVITY.CLASS ID|T24001790"
    - "Invalid Activity/Freq for Event Name Invalid Activity/Freq for Event Name|T24001791"
    - "INVALID ALPHA CHARACTER INVALID ALPHA CHARACTER|T24001794"
    - "Invalid amortization period for & property Invalid amortization period for &
    property|T24001795"
    - "INVALID APPLICATION FOR ! INVALID APPLICATION FOR !|T24001798"
    - "INVALID APPLICATION FORMAT INVALID APPLICATION FORMAT|T24001799"
    - "INVALID APPLICATION OR MSG.TYPE INVALID APPLICATION OR MSG.TYPE|T24001800"
    - "INVALID APPLICATION INVALID APPLICATION|T24001796"
    - "Invalid Arrangement Customer Invalid Arrangement Customer|T24001801"
    - "INVALID ARRANGEMENT ROUTINE INVALID ARRANGEMENT ROUTINE|T24001802"
    - "INVALID ASCII CODE INVALID ASCII CODE|T24001803"
    - "INVALID ATTRIBUTE KEYWORD INVALID ATTRIBUTE KEYWORD|T24001804"
    - "INVALID AUTHENTICATOR INVALID AUTHENTICATOR|T24001805"
    - "INVALID BACKGROUND USE INVALID BACKGROUND USE|T24001806"
    - "Invalid Balance Type definition Invalid Balance Type definition|T24001807"
    - "Invalid Balance type for this Property class Invalid Balance type for this Property
    class|T24001808"
    - "Invalid Balance type for this Property. Invalid Balance type for this Property.|T24001809"
    - "Invalid BALANCE.TYPE for negative interest rate Invalid BALANCE.TYPE for negative
    interest rate|T24001810"
    - "INVALID BASE DATE OPTION INVALID BASE DATE OPTION|T24001811"
    - "INVALID BATCH NO. INVALID BATCH NO.|T24001812"
    - "INVALID BAUD USE 1200, 2400, 4800 OR 9600 ONLY INVALID BAUD USE 1200, 2400,4800 OR 9600 ONLY|T24001813"
    - "Invalid BIC Length Invalid BIC Length|T24001814"
    - "Invalid bill type for Advance Invalid bill type for Advance|T24001815"
    - "INVALID BLOCK LENGTH INVALID BLOCK LENGTH|T24001816"
    - "INVALID BRANCH CODE FOR COMPANY:&|T24001817"
    - "INVALID CALCUATION INVALID CALCUATION|T24001818"
    - "INVALID CALCULATION PARAMETER INVALID CALCULATION PARAMETER|T24001820"
    - "INVALID CALCULATION INVALID CALCULATION|T24001819"
    - "INVALID CARRIER NO. INVALID CARRIER NO.|T24001822"
    - "INVALID CARRIER INVALID CARRIER|T24001821"
    - "Invalid CATEGORY for given POSITION TYPE Invalid CATEGORY for given POSITION
    TYPE|T24001823"
    - "INVALID CHARACTER FOR SC.INDUSTRY INVALID CHARACTER FOR SC.INDUSTRY|T24001825"
    - "INVALID CHARACTER FOR SUB.ASSET.TYPE INVALID CHARACTER FOR SUB.ASSET.TYPE|T24001826"
    - "Input is mandatory when RC type is defined Input is mandatory when RC type is
    defined|T24001588"
    - "Input is not a CUSTOMER company Input is not a CUSTOMER company|T24001589"
    - "Input is not allowed for Automatic Pricing Input is not allowed for Automatic
    Pricing|T24001591"
    - "Input is not allowed for Manual/No.Pricing Input is not allowed for Manual/No.Pricing|T24001592"
    - "INPUT MISSING AS BEGINNING OF RANGE INPUT MISSING AS BEGINNING OF RANGE|T24001632"
    - "INPUT MISSING AS END OF RANGE INPUT MISSING AS END OF RANGE|T24001633"
    - "Input missing for Bond Cap Input missing for Bond Cap|T24001634"
    - "Input Missing for Bond Ranking Input Missing for Bond Ranking|T24001635"
    - "INPUT MISSING OR WRONG OPERAND INPUT MISSING OR WRONG OPERAND|T24001636"
    - "INPUT MSG TYPE FOR FT APPL INPUT MSG TYPE FOR FT APPL|T24001637"
    - "INPUT MUST BE A BANK , PLEASE RETYPE INPUT MUST BE A BANK , PLEASE RETYPE|T24001638"
    - "INPUT MUST BE IN RANGE 90 - 99 INPUT MUST BE IN RANGE 90 - 99|T24001640"
    - "INPUT MUST BE MADE TO LOC.REF.POS INPUT MUST BE MADE TO LOC.REF.POS|T24001641"
    - "INPUT MUST BE NO INPUT MUST BE NO|T24001642"
    - "INPUT MUST BE OPPORTUNITY CHANNELS INPUT MUST BE OPPORTUNITY CHANNELS|T24001643"
    - "Input must not be same as EXP Input must not be same as EXP|T24001644"
    - "Input N or U only Input N or U only|T24001645"
    - "Input Name,Charge,linked prop and frequency Input Name,Charge,linked prop and
    frequency|T24001646"
    - "INVALID PROPERTY CLASS INVALID PROPERTY CLASS|T24002027"
    - "INVALID SORT CODE INVALID SORT CODE|T24002061"
    - "INVALID SORT.CODE INVALID SORT.CODE|T24002062"
    - "Invalid source balance defined for adv interest & Invalid source balance defined
    for adv interest &|T24002063"
    - "Invalid source balance for fixed type of interest Invalid source balance for
    fixed type of interest|T24002064"
    - "INVALID ADDRESS NO. INVALID ADDRESS NO.|T24001792"
    - "INVALID ALL-OPTION INVALID ALL-OPTION|T24001793"
    - "INVALID CHARGE & IN PERIODIC RULE OF & INVALID CHARGE & IN PERIODIC RULE OF
    &|T24001827"
    - "INVALID CURRENCY INVALID CURRENCY|T24001848"
    - "INVALID CUST ENTERED OR WORNG ACCT.WITH.TYPE SELECTED INVALID CUST ENTERED OR
    WORNG ACCT.WITH.TYPE SELECTED|T24001852"
    - "Invalid Customer error. Please contact bank Invalid Customer error. Please contact
    bank|T24001856"
    - "INVALID CUSTOMER INVALID CUSTOMER|T24001853"
    - "Invalid Customer. Plz. contact Bank for further details Invalid Customer. Plz.
    contact Bank for further details|T24001857"
    - "INVALID DATA BITS USE 7 OR 8 ONLY INVALID DATA BITS USE 7 OR 8 ONLY|T24001858"
    - "Invalid customer entered or wrong ORD.INST.TYPE selected Invalid customer entered
    or wrong ORD.INST.TYPE selected|T24001855"
    - "INVALID DATA TYPE INVALID DATA TYPE|T24001859"
    - "INVALID DATE (YYYYMMDD) INVALID DATE (YYYYMMDD)|T24001862"
    - "INVALID DATE LENGTH INVALID DATE LENGTH|T24003808"
    - "INVALID DATE INVALID DATE|T24001860"
    - "INVALID DDI STATUS INVALID DDI STATUS|T24001863"
    - "Invalid Decision for DECISION.FIELD Invalid Decision for DECISION.FIELD|T24001864"
    - "Invalid Definition Id Invalid Definition Id|T24001865"
    - "INVALID DELIVERY ID INVALID DELIVERY ID|T24001866"
    - "INVALID DEPARTMENT CODE INVALID DEPARTMENT CODE|T24001868"
    - "INVALID DEPARTMENT INVALID DEPARTMENT|T24001867"
    - "INVALID DICTIONARY TYPE INVALID DICTIONARY TYPE|T24001869"
    - "INVALID DOC.GROUP FOR APPLN INVALID DOC.GROUP FOR APPLN|T24001870"
    - "Invalid Donate Type Specified Invalid Donate Type Specified|T24001871"
    - "INVALID EB ERROR ID INVALID EB ERROR ID|T24001872"
    - "Invalid EB.COMPARISON.TYPE record Invalid EB.COMPARISON.TYPE record|T24001873"
    - "Invalid EB.PRODUCT ID Invalid EB.PRODUCT ID|T24001874"
    - "INVALID ENQUIRY INVALID ENQUIRY|T24001875"
    - "Invalid equivalent document of & exists Invalid equivalent document of & exists|T24001876"
    - "Invalid Exclusion definition Invalid Exclusion definition|T24001877"
    - "INVALID EXEC VALUE . MUST BE FROM 0 - 100N INVALID EXEC VALUE . MUST BE FROM
    0 - 100%N|T24001878"
    - "INVALID FIELD FOR MESSAGE INVALID FIELD FOR MESSAGE|T24001880"
    - "Invalid field for selected application Invalid field for selected application|T24001881"
    - "Invalid field name & in property class & Invalid field name & in property class
    &|T24001886"
    - "Invalid field name & Invalid field name &|T24001885"
    - "Invalid Field Name in Additional Field Invalid Field Name in Additional Field|T24001887"
    - "Invalid Field name Invalid Field name|T24001882"
    - "INVALID FIELD NUMBER (1-9) INVALID FIELD NUMBER (1-9)|T24001889"
    - "INVALID FIELD NUMBER INVALID FIELD NUMBER|T24001888"
    - "INVALID FIELD SPEC INVALID FIELD SPEC|T24001890"
    - "INVALID FIELD TAG INVALID FIELD TAG|T24001891"
    - "INVALID FIELD TYPE INVALID FIELD TYPE|T24001892"
    - "INVALID FIELD INVALID FIELD|T24001879"
    - "INVALID FILE ID INVALID FILE ID|T24001893"
    - "INVALID FILE NAME INVALID FILE NAME|T24001894"
    - "INVALID FOR CALC TYPE INVALID FOR CALC TYPE|T24001896"
    - "INVALID FOR FIXED TYPE OPTION INVALID FOR FIXED TYPE OPTION|T24001899"
    - "INVALID FOR INTERNAL ACCOUNT INVALID FOR INTERNAL ACCOUNT|T24001900"
    - "Invalid for MT292 Invalid for MT292|T24001901"
    - "INVALID FOR RAISE.ACTIVITY INVALID FOR RAISE.ACTIVITY|T24001902"
    - "INVALID FOR THIS GRP & CCY INVALID FOR THIS GRP & CCY|T24001903"
    - "INVALID FORMAT FOR CONVERSION INVALID FORMAT FOR CONVERSION|T24001908"
    - "INVALID FORMAT FOR COUNTRY.CODE INVALID FORMAT FOR COUNTRY.CODE|T24001909"
    - "INVALID FORMAT FOR ID INVALID FORMAT FOR ID|T24001910"
    - "INVALID FORMAT FOR SECTOR.CODE INVALID FORMAT FOR SECTOR.CODE|T24001911"
    - "Invalid format must be 1-3ND Invalid format must be 1-3ND|T24001912"
    - "INVALID FORMAT OF ID SPECIFIED INVALID FORMAT OF ID SPECIFIED|T24001913"
    - "Invalid Format to be followed by C or W Invalid Format to be followed by C or
    W|T24001914"
    - "INVALID FORMAT TYPE INVALID FORMAT TYPE|T24001915"
    - "INVALID FORMAT INVALID FORMAT|T24001904"
    - "INVALID FREQUENCY - 1 OR 2 INVALID FREQUENCY - 1 OR 2|T24001917"
    - "INVALID FREQUENCY (ONLY 1,1C,2,2C,3) INVALID FREQUENCY (ONLY 1,1C,2,2C,3)|T24001918"
    - "INVALID FREQUENCY INVALID FREQUENCY|T24001916"
    - "INVALID FT.COMMISSION.TYPE CODE INVALID COMMISSION|T24003806"
    - "INVALID FUNCTION FOR AUTHORISED RECORD INVALID FUNCTION FOR AUTHORISED RECORD|T24001919"
    - "INVALID FUNCTION FOR BAL-USE INVALID FUNCTION FOR BAL-USE|T24001920"
    - "INVALID FUNCTION FOR END.OF.DAY INVALID FUNCTION FOR END.OF.DAY|T24001921"
    - "INVALID FUNCTION FOR TAP-USE INVALID FUNCTION FOR TAP-USE|T24001922"
    - "INVALID FUNCTION FOR THIS PGM. INVALID FUNCTION FOR THIS PGM.|T24001923"
    - "Invalid function when Status is & Invalid function when Status is &|T24001925"
    - "INVALID GROUP.CONDITION ID - & INVALID GROUP.CONDITION ID - &|T24001926"
    - "Invalid Id Character(s) Invalid Id Character(s)|T24001931"
    - "Invalid Id for Buyer-Seller Limit Invalid Id for Buyer-Seller Limit|T24001932"
    - "INVALID ID FOR COMPANY INVALID ID FOR COMPANY|T24001933"
    - "INVALID ID FORMAT INVALID ID FORMAT|T24001934"
    - "INPUT OR LINE-DELETION MISSING INPUT OR LINE-DELETION MISSING|T24001708"
    - "INPUT POSITION REQUIRED INPUT POSITION REQUIRED|T24001709"
    - "INPUT POSSIBLE ONLY IF SYSTEM.ID IS FT INPUT POSSIBLE ONLY IF SYSTEM.ID IS FT|T24001710"
    - "Input Property Field Name to validate Input Property Field Name to validate|T24001711"
    - "INPUT REC NUMBER MANDATORY INPUT REC NUMBER MANDATORY|T24001712"
    - "Input reqd. when RECONCILIATION is YES Input reqd. when RECONCILIATION is YES|T24001713"
    - "Input Should be either PL Category or Account Class Input Should be either PL
    Category or Account Class|T24001714"
    - "INPUT SYSTEM RECORD IN INT.MOVEMENT.UPDATE INPUT SYSTEM RECORD IN INT.MOVEMENT.UPDATE|T24001715"
    - "INPUT TO THIS FIELD IS MANDATORY INPUT TO THIS FIELD IS MANDATORY|T24001716"
    - "INPUT TRANS.CODE.FROM MISSING INPUT TRANS.CODE.FROM MISSING|T24001717"
    - "INPUT VALID SETTLEMENT ACCOUNT INPUT VALID SETTLEMENT ACCOUNT|T24001718"
    - "Input values are similar to existing parameter record Input values are similar
    to existing parameter record|T24001719"
    - "Input values when MANDATORY is YES Input values when MANDATORY is YES|T24001720"
    - "Inputs Property or Floating Index are missing Inputs Property or Floating Index
    are missing|T24001721"
    - "Installment should be checked for any one payment type Installment should be
    checked for any one payment type|T24001722"
    - "Installment Should be flagged for Account Property Installment Should be flagged
    for Account Property|T24001723"
    - "Instance & and Class & Cannot be Definned Together Instance & and Class & Cannot
    be Definned Together|T24001724"
    - "Instance not allowed for the class type & Instance not allowed for the class
    type &|T24001725"
    - "INT.NO.BOOKING must be SUSPENSEfor CONTINGENT.INT O INT.NO.BOOKING must be SUSPENSEfor
    CONTINGENT.INT O|T24001733"
    - "INT.NO.BOOKING should be Y for CONTINGENT.INT C INT.NO.BOOKING should be Y for
    CONTINGENT.INT C|T24001734"
    - "INTEGER, NULL OR NONE INTEGER, NULL OR NONE|T24001735"
    - "INVALID FOR CHEQUE TXN INVALID FOR CHEQUE TXN|T24001897"
    - "INVALID FOR DEBIT TXN INVALID FOR DEBIT TXN|T24001898"
    - "Invalid ID Invalid ID|T24001927"
    - "INVALID IF BASE DATE IS ENTERED INVALID IF BASE DATE IS ENTERED|T24001935"
    - "INVALID INCREASE AMOUNT & INVALID INCREASE AMOUNT &|T24001936"
    - "Invalid Input ! Only Interest Property is allowed Invalid Input ! Only Interest
    Property is allowed|T24001939"
    - "INVALID INPUT FOR ACTIVITY SOURCE TYPE INVALID INPUT FOR ACTIVITY SOURCE TYPE|T24001940"
    - "INVALID INPUT FOR APPLICATION.TYPE INVALID INPUT FOR APPLICATION.TYPE|T24001941"
    - "INVALID INPUT FOR BALANCE SOURCE TYPE INVALID INPUT FOR BALANCE SOURCE TYPE|T24001942"
    - "Invalid Input for Org.Date Invalid Input for Org.Date|T24001943"
    - "Invalid Input for Org.Msg.Type Invalid Input for Org.Msg.Type|T24001944"
    - "INVALID INPUT FOR PROPERTY SOURCE TYPE INVALID INPUT FOR PROPERTY SOURCE TYPE|T24001945"
    - "Invalid input for top account Invalid input for top account|T24001946"
    - "INVALID LENGTH FOR SUB.ASSET.TYPE INVALID LENGTH FOR SUB.ASSET.TYPE|T24001967"
    - "INVALID LENGTH INVALID LENGTH|T24001964"
    - "INVALID LIABILITY INVALID LIABILITY|T24001968"
    - "INVALID LINE NUMBER INVALID LINE NUMBER|T24001969"
    - "INVALID LINECOMM SPECIFICATION INVALID LINECOMM SPECIFICATION|T24001970"
    - "Invalid Loan Type for the Category Invalid Loan Type for the Category|T24001971"
    - "INVALID LOCAL TABLE INVALID LOCAL TABLE|T24001972"
    - "INVALID MASK LENGTH INVALID MASK LENGTH|T24001973"
    - "INVALID MASK.LENGTH INVALID MASK.LENGTH|T24001974"
    - "INVALID MASK-CHARACTER (&) INVALID MASK-CHARACTER (&)|T24001975"
    - "Invalid Mass Block Record Invalid Mass Block Record|T24001976"
    - "Invalid Master Card Number Invalid Master Card Number|T24001977"
    - "Invalid match pattern for LIKE Invalid match pattern for LIKE|T24001978"
    - "INVALID MATCHING RECORD STATUS INVALID MATCHING RECORD STATUS|T24001979"
    - "INVALID MESSAGE CODE INVALID MESSAGE CODE|T24001980"
    - "INVALID MESSAGE TYPE INVALID MESSAGE TYPE|T24001981"
    - "Invalid MINUS when COLLATERAL.TYPE is PLUS Invalid MINUS when COLLATERAL.TYPE
    is PLUS|T24001983"
    - "INVALID MINUS INVALID MINUS|T24001982"
    - "INVALID MMDD FORMAT INVALID MMDD FORMAT|T24001984"
    - "INVALID MOVEMENT TYPE INVALID MOVEMENT TYPE|T24001985"
    - "Invalid Msg.Type Invalid Msg.Type|T24001986"
    - "INVALID MULTI FIELD DEFINEITION INVALID MULTI FIELD DEFINEITION|T24001987"
    - "Invalid Multipe Instance of & Invalid Multipe Instance of &|T24001988"
    - "INVALID MULTIPLE LINES COMBINATION INVALID MULTIPLE LINES COMBINATION|T24001989"
    - "INVALID MV OR SV DEFINITION INVALID MV OR SV DEFINITION|T24001990"
    - "Invalid Netting Agreement Invalid Netting Agreement|T24001991"
    - "INVALID NETWORK NUMBER INVALID NETWORK NUMBER|T24001992"
    - "Invalid New Amount for the Product Line Invalid New Amount for the Product Line|T24001993"
    - "INVALID NOSTRO CLASS SECTOR FOR CUSTOMER INVALID NOSTRO CLASS SECTOR FOR CUSTOMER|T24001994"
    - "INVALID NUMBER OF DELIMITERS INVALID NUMBER OF DELIMITERS|T24001995"
    - "INVALID OPERAND FOR FIELD.NAME INVALID OPERAND FOR FIELD.NAME|T24001996"
    - "INVALID OPERAND FOR FIELDNAME INVALID OPERAND FOR FIELDNAME|T24001997"
    - "INVALID OPTION WHEN ATTRIBUTE VALUE IS FIXED INVALID OPTION WHEN ATTRIBUTE VALUE
    IS FIXED|T24001999"
    - "INVALID OPTION WHEN NON-NEGOTIABLE IS SET INVALID OPTION WHEN NON-NEGOTIABLE
    IS SET|T24002000"
    - "INVALID OPTION INVALID OPTION|T24001998"
    - "Invalid or Incorrect Selection Invalid or Incorrect Selection|T24002001"
    - "INVALID OVERRIDE ID INVALID OVERRIDE ID|T24002002"
    - "INVALID PAGE REQUEST INVALID PAGE REQUEST|T24002003"
    - "Invalid path Invalid path|T24002004"
    - "Invalid payment amount Invalid payment amount|T24002005"
    - "INVALID PAYMENT METHOD FOR DISBURSEMENT BILL TYPE INVALID PAYMENT METHOD FOR
    DISBURSEMENT BILL TYPE|T24002006"
    - "INVALID PAYMENT METHOD FOR LENDING PRODUCT LINE INVALID PAYMENT METHOD FOR LENDING
    PRODUCT LINE|T24002007"
    - "Invalid Payment Rules Activity Invalid Payment Rules Activity|T24002008"
    - "Invalid Payment type for Advance type of Interest Invalid Payment type for Advance
    type of Interest|T24002009"
    - "Invalid payment type for this property Invalid payment type for this property|T24002010"
    - "INVALID PERIOD - nnnD or nnM or nnW are valid formats INVALID PERIOD
    - nnnD or nnM or nnW are valid formats|T24002012"
    - "INVALID PERIOD = nnA, nnC, nnW valid formats INVALID PERIOD = nnA,
    nnC, nnW valid formats|T24002013"
    - "INVALID PERIOD INVALID PERIOD|T24002011"
    - "Invalid Period. NNC,NNW are Valid Formats Invalid Period. NNC,NNW are Valid
    Formats|T24002014"
    - "Invalid Periodic Attribute record Invalid Periodic Attribute record|T24002015"
    - "Invalid PLUS when COLLATERAL.TYPE is MINUS Invalid PLUS when COLLATERAL.TYPE
    is MINUS|T24002016"
    - "INVALID POSITION TYPE INVALID POSITION TYPE|T24002017"
    - "INVALID PRODUCT CODE INVALID PRODUCT CODE|T24002019"
    - "Invalid Product for Class Definition Invalid Product for Class Definition|T24002020"
    - "Invalid Product Type Invalid Product Type|T24002021"
    - "INVALID PROPERTY & DEFINED IN ACTIVITY.API INVALID PROPERTY & DEFINED IN ACTIVITY.API|T24002022"
    - "INVALID PROPERTY & DEFINED IN ACTIVITY.CHARGE INVALID PROPERTY & DEFINED IN
    ACTIVITY.CHARGE|T24002023"
    - "INVALID PROPERTY & DEFINED IN ACTIVITY.PRESENTATION INVALID PROPERTY & DEFINED
    IN ACTIVITY.PRESENTATION|T24002024"
    - "Invalid Property & Defined in SCHED.CHARGE Invalid Property & Defined in SCHED.CHARGE|T24002025"
    - "INVALID PROPERTY & FOR PRODUCT & INVALID PROPERTY & FOR PRODUCT &|T24002026"
    - "INVALID PROPERTY CLASS FOR ACTIVITY INVALID PROPERTY CLASS FOR ACTIVITY|T24002028"
    - "INVALID PROPERTY CLASS FOR CALC TYPE INVALID PROPERTY CLASS FOR CALC TYPE|T24002029"
    - "INVALID PROPERTY CLASS FOR PAYMENT INVALID PROPERTY CLASS FOR PAYMENT|T24002030"
    - "Invalid charge type for Classic mapping Invalid charge type for Classic mapping|T24001828"
    - "INVALID CHARGE.APPLICATION INVALID CHARGE.APPLICATION|T24001829"
    - "INVALID CHEQUE CURRENCY FOR ACCOUNT INVALID CHEQUE CURRENCY FOR ACCOUNT|T24001830"
    - "Invalid Cheque Sequence No. Invalid Cheque Sequence No.|T24001831"
    - "INVALID CLASS & FOR PRODUCT LINE & INVALID CLASS & FOR PRODUCT LINE &|T24001832"
    - "Invalid class type for & Invalid class type for &|T24001833"
    - "INVALID CLASSIC PRODUCT ID INVALID CLASSIC PRODUCT ID|T24001834"
    - "INVALID CLEARING SYSTEM CODE INVALID CLEARING SYSTEM CODE|T24001835"
    - "INVALID CODE FOR EVENT - RETURNED.ITEM INVALID CODE FOR EVENT - RETURNED.ITEM|T24001836"
    - "INVALID COLLATERAL TYPE INVALID COLLATERAL TYPE|T24001837"
    - "INVALID COLLATERAL.TYPE ID ATTACHED TO CUSTOMER INVALID COLLATERAL.TYPE ID ATTACHED
    TO CUSTOMER|T24001838"
    - "INVALID COMBINATION OF MASK CHARACTERS INVALID COMBINATION OF MASK CHARACTERS|T24001839"
    - "INVALID COMPANY CODE INVALID COMPANY CODE|T24001840"
    - "Invalid Company code/Customer ID. Invalid Company code/Customer ID.|T24001841"
    - "INVALID COMPANY NAME INVALID COMPANY NAME|T24001842"
    - "Invalid Condition Id Invalid Condition Id|T24001843"
    - "INVALID CONVERSION INVALID CONVERSION|T24001844"
    - "INVALID CONVERSION, FILES NOT RESIDENT INVALID CONVERSION, FILES NOT RESIDENT|T24001845"
    - "INVALID COORDINATES - ENTER XXX,YYY INVALID COORDINATES - ENTER XXX,YYY|T24001846"
    - "INVALID COUNTRY/REGION INVALID COUNTRY/REGION|T24001847"
    - "Invalid currency for the product Invalid currency for the product|T24001849"
    - "INVALID CURRENCY LENGTH INVALID CREDIT CURRENCY|T24003802"
    - "INVALID CURRENCY MNEMONIC INVALID CURRENCY MNEMONIC|T24001850"
    - "INVALID CURRENCY PAIR INVALID CURRENCY PAIR|T24001851"
    - "INVALID RULE VALUE INVALID RULE VALUE|T24002047"
    - "INVALID SC.TRANS.NAME ID INVALID SC.TRANS.NAME ID|T24002049"
    - "Invalid Seller exposure defined Invalid Seller exposure defined|T24002050"
    - "INVALID SEQUENCE NO INVALID SEQUENCE NO|T24002051"
    - "INVALID SEQUENCE NUMBER INVALID SEQUENCE NUMBER|T24002052"
    - "INVALID SERIAL NUMBER INVALID SERIAL NUMBER|T24002054"
    - "INVALID SHOULD BE <S>HHMM INVALID SHOULD BE <S>HHMM|T24002055"
    - "INVALID SIC FIELD INVALID SIC FIELD|T24002056"
    - "Invalid sign for Product Line and Balance type Invalid sign for Product Line
    and Balance type|T24002057"
    - "Invalid simulation runner Id Invalid simulation runner Id|T24002058"
    - "INVALID SIMULATON CAPTURE REFERENCE INVALID SIMULATON CAPTURE REFERENCE|T24002059"
    - "Invalid sms address Invalid sms address|T24002060"
    - "Invalid Source Type for Charge Property Class Invalid Source Type for Charge
    Property Class|T24002065"
    - "INVALID SOURCE TYPE FOR INTEREST PROPERTY CLASS INVALID SOURCE TYPE FOR INTEREST
    PROPERTY CLASS|T24002066"
    - "INVALID SOURCE-CALC TYPE FOR CHARGE PROPERTY CLASS INVALID SOURCE-CALC TYPE
    FOR CHARGE PROPERTY CLASS|T24002067"
    - "INVALID SOURCE-CALC TYPE FOR INTEREST PROPERTY CLASS INVALID SOURCE-CALC TYPE
    FOR INTEREST PROPERTY CLASS|T24002068"
    - "INVALID STAGE OR PROPERTY.CLASS INVALID STAGE OR PROPERTY.CLASS|T24002070"
    - "INVALID STAGE INVALID STAGE|T24002069"
    - "INVALID STANDALONE LINK TO MG INVALID STANDALONE LINK TO MG|T24002071"
    - "INVALID STATUS CHANGE INVALID STATUS CHANGE|T24002074"
    - "INVALID STATUS INVALID STATUS|T24002072"
    - "INVALID STOCK PARAMETER ID INVALID STOCK PARAMETER ID|T24002075"
    - "INVALID STOCK.PARAMETER ID INVALID STOCK.PARAMETER ID|T24002076"
    - "INVALID STOP BITS USE 0,1,2 ONLY INVALID STOP BITS USE 0,1,2 ONLY|T24002077"
    - "INVALID SUB FIELD DEFINTION INVALID SUB FIELD DEFINTION|T24002078"
    - "INVALID SUB TYPE INVALID SUB TYPE|T24002079"
    - "INVALID SWIFT CODE INVALID SWIFT CODE|T24002080"
    - "Invalid Swift Type Sent Invalid Swift Type Sent|T24002081"
    - "Invalid TARGET.TYPE Invalid TARGET.TYPE|T24002082"
    - "INVALID TEXT FORMAT INVALID TEXT FORMAT|T24002083"
    - "Invalid transaction amount Invalid transaction amount|T24002088"
    - "Invalid Transaction Code & Invalid Transaction Code &|T24002090"
    - "INVALID TRANSACTION CODE INVALID TRANSACTION CODE|T24002089"
    - "INVALID TXN CODE FOR APPLICATION INVALID TXN CODE FOR APPLICATION|T24002091"
    - "INVALID TYPE INVALID TYPE|T24002092"
    - "Invalid Upload Reference Invalid Upload Reference|T24002093"
    - "Invalid Usage - LIMIT.REFERENCE is not stated in LIMIT. Invalid Usage - LIMIT.REFERENCE
    is not stated in LIMIT.|T24002094"
    - "Invalid Value Source for Additional Details Invalid Value Source for Additional
    Details|T24002096"
    - "Invalid Value Invalid Value|T24002095"
    - "Invalid Version entered Invalid Version entered|T24002097"
    - "Invalid version for Quotation Type & Invalid version for Quotation Type &|T24002098"
    - "Invalid Visa Card Number Invalid Visa Card Number|T24002099"
    - "INVALID WHEN CUSTOMER HAS BEEN DEFINED INVALID WHEN CUSTOMER HAS BEEN DEFINED|T24002100"
    - "INVALID WHEN FIRST LIMIT.AMT IS ZERO INVALID WHEN FIRST LIMIT.AMT IS ZERO|T24002101"
    - "INVALID WHEN WALK.IN.CUSTOMER FIELD IS SET TO NONE INVALID WHEN WALK.IN.CUSTOMER
    FIELD IS SET TO NONE|T24002102"
    - "INVALID WITH IND CHARGE LEVEL INVALID WITH IND CHARGE LEVEL|T24002103"
    - "INVALID WITH LOCAL CCY INVALID WITH LOCAL CCY|T24002104"
    - "Invalid without APPLICATION.TYPE or APPLICATION.ORDER Invalid without APPLICATION.TYPE
    or APPLICATION.ORDER|T24002105"
    - "INVALID WITHOUT PARTY ID INVALID WITHOUT PARTY ID|T24002106"
    - "INVALID/CUSTOMER RECORD DOES NOT EXISTS INVALID/CUSTOMER RECORD DOES NOT EXISTS|T24002107"
    - "INVALID/NEITHER PERSON.ENTITY NOR CUSTOMER.PROSPECT RECORD EXISTS INVALID/NEITHER
    PERSON.ENTITY NOR CUSTOMER.PROSPECT RECORD EXISTS|T24002108"
    - "Invlaid ID Format Invlaid ID Format|T24002109"
    - "Inwards account is different from mandate Inwards account is different from
    mandate|T24002110"
    - "Issue bill should be set to NO for Online Capitalise Issue bill should be set
    to NO for Online Capitalise|T24002111"
    - "Issue of Cheque Book is Restricted to the Customer Issue of Cheque Book is Restricted
    to the Customer|T24002112"
    - "ITEM & WITH RECORD.STATUS & ITEM & WITH RECORD.STATUS &|T24002118"
    - "ITEM ALREADY PROCESSED ITEM ALREADY PROCESSED|T24002119"
    - "ITEM=0 ITEM=0|T24002120"
    - "Joint Holder Already Exist Joint Holder Already Exist|T24002121"
    - "JOINT NOTES CANNOT BE ENTERED JOINT NOTES CANNOT BE ENTERED|T24002122"
    - "It is not allowed to add new posting restriction back dated It is not allowed
    to add new posting restriction back dated|T24002113"
    - "It is not allowed to remove posting restriction back dated It is not allowed
    to remove posting restriction back dated|T24002114"
    - "It is not allowed to reverse a backdated activity if posting restriction related
    fields has been changed It is not allowed to reverse a backdated activity if posting
    restriction related fields has been changed|T24002115"
    - "It is not allowed to update posting restriction back dated It is not allowed
    to update posting restriction back dated|T24002116"
    - "It is not allowed to update start date when restriction is already active It
    is not allowed to update start date when restriction is already active|*********"
    - "Liability and Info limit parties cannot be same Liability and Info limit parties
    cannot be same|*********"
    - "Liability customers should be different Liability customers should be different|*********"
    - "Liability party should be none for Product Collection Liability party should
    be none for Product Collection|*********"
    - "License code not installed License code not installed|*********"
    - "Limit Allocation and GL Allocation must be for same Customer Limit Allocation
    and GL Allocation must be for same Customer|*********"
    - "Limit Allocation Percentage is not inputted for any customer Limit Allocation
    Percentage is not inputted for any customer|*********"
    - "Limit Amount cannot be lesser than Oustanding Amount Limit Amount cannot be
    lesser than Oustanding Amount|*********"
    - "LIMIT CANT BE ATTACHED AS PERIOD PRCNT MENTIONED LIMIT CANT BE ATTACHED AS PERIOD
    PRCNT MENTIONED|*********"
    - "Limit Customer Should set to Yes Limit Customer Should set to Yes|*********"
    - "Limit error. Please contact bank for further details Limit error. Please contact
    bank for further details|*********"
    - "Limit id is null during AA link check Limit id is null during AA link check|*********"
    - "Limit is not secured Limit is not secured|*********"
    - "Limit managed by AA - Limit amount cannot be null Limit managed by AA - Limit
    amount cannot be null|T24002145"
    - "Limit managed by AA - should be a product level limit Limit managed by AA -
    should be a product level limit|T24002146"
    - "Limit managed by AA - Should be a single limit Limit managed by AA - Should
    be a single limit|T24002147"
    - "LIMIT MANDATORY FOR COMMITMENT ARRANGEMENT LIMIT MANDATORY FOR COMMITMENT ARRANGEMENT|T24002148"
    - "INVALID INPUT FOR TYPE INVALID INPUT FOR TYPE|T24001947"
    - "INVALID INPUT HERE INVALID INPUT HERE|T24001948"
    - "Invalid input when decisn field holds routine Invalid input when decisn field
    holds routine|T24001949"
    - "Invalid input when NEXT.STATUS is null Invalid input when NEXT.STATUS is null|T24001950"
    - "Invalid input without LAST.UPD.APPLN Invalid input without LAST.UPD.APPLN|T24001951"
    - "Invalid Input! Company must belong to current financial group Invalid Input!
    Company must belong to current financial group|T24001952"
    - "Invalid Input! Only Interest and Charge properties are allowed Invalid Input!
    Only Interest and Charge properties are allowed|T24001953"
    - "INVALID INPUT INVALID INPUT|T24001937"
    - "Invalid interest Margin Operator Invalid interest Margin Operator|T24001954"
    - "INVALID INTERNET ADDRESS INVALID INTERNET ADDRESS|T24001955"
    - "Invalid KEY Format Invalid KEY Format|T24001958"
    - "INVALID KEY LENGTH INVALID KEY LENGTH|T24001959"
    - "INVALID KEY INVALID KEY|T24001956"
    - "INVALID KEYWORD CONSTRUCTION INVALID KEYWORD CONSTRUCTION|T24001961"
    - "INVALID KEYWORD INVALID KEYWORD|T24001960"
    - "INVALID LANGUAGE CODE INVALID LANGUAGE CODE|T24001963"
    - "INVALID LANGUAGE INVALID LANGUAGE|T24001962"
    - "INVALID LENGTH FOR CURRENCY INVALID LENGTH FOR CURRENCY|T24001965"
    - "INVALID LENGTH FOR SECTOR.CODE INVALID LENGTH FOR SECTOR.CODE|T24001966"
    - "JULIAN DATE CAN ONLY BE TODAY FOR NEW TRANSACTIONS JULIAN DATE CAN ONLY BE TODAY
    FOR NEW TRANSACTIONS|T24002123"
    - "KEY NOT DEFINED ON PI TABLE KEY NOT DEFINED ON PI TABLE|T24002124"
    - "LANGUAGE CODE MUST BE ALPHA LANGUAGE CODE MUST BE ALPHA|T24002125"
    - "LAST CHARACTER SHOULD BE C OR W LAST CHARACTER SHOULD BE C OR W|T24002126"
    - "LAST CHEQUE < FIRST CHEQUE LAST CHEQUE < FIRST CHEQUE|T24002127"
    - "LAST PAGE DISPLAYED LAST PAGE DISPLAYED|T24002128"
    - "LEFTMOST SCREEN DISPLAYED LEFTMOST SCREEN DISPLAYED|T24002129"
    - "LESS THAN ARRANGEMENT START DATE LESS THAN ARRANGEMENT START DATE|T24002130"
    - "LESS THAN MINIMUM RATE LESS THAN MINIMUM RATE|T24002131"
    - "Liability and Info limit customers cannot be same Liability and Info limit customers
    cannot be same|T24002132"
    - "Linked arrangement should same as in collateral Linked arrangement should same
    as in collateral|T24002168"
    - "Linked arrangements currency cannot be a different currency Linked arrangements
    currency cannot be a different currency|T24002169"
    - "Linked Property and Interest Property are same Linked Property and Interest
    Property are same|T24002170"
    - "Linked property should be inputted Linked property should be inputted|T24002171"
    - "Linked to portfolio, customer cant be changed Linked to portfolio, customer
    cant be changed|T24002172"
    - "Linked to this account at a lower level Linked to this account at a lower level|T24002173"
    - "LIQU ACCOUNT TO BE CLOSED LIQU ACCOUNT TO BE CLOSED|T24002174"
    - "LIQU.ACCOUNT HAS ANOTHER CURRENCY LIQU.ACCOUNT HAS ANOTHER CURRENCY|T24002175"
    - "LIQU.CCY.MARKET Value is Only 1 or Between 11 And 19 LIQU.CCY.MARKET Value is
    Only 1 or Between 11 And 19|T24002176"
    - "List not found List not found|T24002177"
    - "LOCAL AMT AFTER BALANCING = NEGATIV LOCAL AMT AFTER BALANCING = NEGATIV|T24002178"
    - "Local API Name should be specified Local API Name should be specified|T24002179"
    - "Local Application should be specified Local Application should be specified|T24002180"
    - "LOCAL CCY MKT suspended & LOCAL CCY MKT suspended &|T24002181"
    - "Local currency record missing Local currency record missing|T24002182"
    - "Local fixed rate missing Local fixed rate missing|T24002183"
    - "LOCAL TABLE NOT AUTH. LOCAL TABLE NOT AUTH.|T24002184"
    - "LOCAL.REF is not present in LOCAL.REF.TABLE LOCAL.REF is not present in LOCAL.REF.TABLE|T24002185"
    - "LOCAL.REF.TABLE NOT AUTH. LOCAL.REF.TABLE NOT AUTH.|T24002186"
    - "LOCATION MUST BE INPUT LOCATION MUST BE INPUT|T24002187"
    - "LOCK.INC.MVMT Either Debit or None LOCK.INC.MVMT Either Debit or None|T24002188"
    - "LOCK.INC.THIS.MVMT CAN BE BOTH OR CREDITS OR DEBITS LOCK.INC.THIS.MVMT CAN BE
    BOTH OR CREDITS OR DEBITS|T24002189"
    - "LOCK.INC.THIS.MVMT SHOULD BE EITHER CREDITS OR NONE LOCK.INC.THIS.MVMT SHOULD
    BE EITHER CREDITS OR NONE|T24002190"
    - "LOCK.INC.THIS.MVMT SHOULD BE EITHER DEBITS OR NONE LOCK.INC.THIS.MVMT SHOULD
    BE EITHER DEBITS OR NONE|T24002191"
    - "LOCK.INC.THIS.MVMT SHOULD BE EITHER NULL OR NONE LOCK.INC.THIS.MVMT SHOULD BE
    EITHER NULL OR NONE|T24002192"
    - "Main account is INTEREST method Main account is INTEREST method|T24002193"
    - "MAIN MASTER LINKS ALREADY SET UP MAIN MASTER LINKS ALREADY SET UP|T24002194"
    - "MAIN MESSAGE & NOT SET UP MAIN MESSAGE & NOT SET UP|T24002195"
    - "Make bill due should be set to NO for Advance Make bill due should be set to
    NO for Advance|T24002196"
    - "Mandate Not reached (E-156530) Mandate Not reached (E-156530)|T24002198"
    - "Mandate not reached Mandate not reached|T24002197"
    - "Mandate Reference already present for Creditor Id Mandate Reference already
    present for Creditor Id|T24002199"
    - "MANDATE.REF SHOULD BE INWARD MANDATE.REF SHOULD BE INWARD|T24002200"
    - "MANDATORY - REPORT REQUIRED MANDATORY - REPORT REQUIRED|T24002201"
    - "Mandatory Argument Class Name Input Missing Mandatory Argument Class Name Input
    Missing|T24002202"
    - "Mandatory Argument Class Type Input Missing Mandatory Argument Class Type Input
    Missing|T24002203"
    - "Mandatory argument File name input missing Mandatory argument File name input
    missing|T24002204"
    - "Mandatory Argument Formlet Class Input Missing Mandatory Argument Formlet Class
    Input Missing|T24002205"
    - "Mandatory Class type missing Mandatory Class type missing|T24002206"
    - "MANDATORY DEFINITIONS SHOULD BE INPUT MANDATORY DEFINITIONS SHOULD BE INPUT|T24002207"
    - "MANDATORY FIELD & MISSING IN CONDITION & MANDATORY FIELD & MISSING IN CONDITION
    &|T24002210"
    - "Mandatory Field Deposit Period Mandatory Field Deposit Period|T24002211"
    - "MANDATORY FIELD FOR CORRESPONDING DEPOSIT PERIOD MANDATORY FIELD FOR CORRESPONDING
    DEPOSIT PERIOD|T24002212"
    - "Mandatory field for this request type Mandatory field for this request type|T24002213"
    - "Mandatory field Mandatory field|T24002208"
    - "MANDATORY FOR ACTIVITY TYPE MANDATORY FOR ACTIVITY TYPE|T24002214"
    - "MANDATORY FOR APPLICATION.TYPE MANDATORY FOR APPLICATION.TYPE|T24002215"
    - "MANDATORY FOR CALC TYPE MANDATORY FOR CALC TYPE|T24002216"
    - "Mandatory for contingent category Mandatory for contingent category|T24002218"
    - "MANDATORY FOR COVER MANDATORY FOR COVER|T24002219"
    - "MANDATORY FOR INWARD MANDATORY FOR INWARD|T24002220"
    - "MANDATORY FOR LANGUAGE MANDATORY FOR LANGUAGE|T24002221"
    - "MANDATORY FOR MARGIN DEFINITION MANDATORY FOR MARGIN DEFINITION|T24002222"
    - "MANDATORY FOR NUM PAYMENTS MANDATORY FOR NUM PAYMENTS|T24002223"
    - "MANDATORY FOR OUTWARD MANDATORY FOR OUTWARD|T24002224"
    - "Mandatory for Partial Recourse Mandatory for Partial Recourse|T24002225"
    - "Mandatory for Payment Type Mandatory for Payment Type|T24002226"
    - "MANDATORY FOR SERIAL CONTROL TYPE MANDATORY FOR SERIAL CONTROL TYPE|T24002227"
    - "MANDATORY FOR SUB GROUP MANDATORY FOR SUB GROUP|T24002228"
    - "MANDATORY FOR THIS ACTIVITY MANDATORY FOR THIS ACTIVITY|T24002229"
    - "MANDATORY FOR TYPE MANDATORY FOR TYPE|T24002230"
    - "MANDATORY HEADER FIELD (&) MISSING MANDATORY HEADER FIELD (&) MISSING|T24002231"
    - "Mandatory header fields (&) missing# Mandatory header fields (&) missing#|T24002232"
    - "MANDATORY IF ATTRIBUTE IS ENTERED MANDATORY IF ATTRIBUTE IS ENTERED|T24002233"
    - "Mandatory if BALANCED.INTRNL = Y Mandatory if BALANCED.INTRNL = Y|T24002234"
    - "MANDATORY IF BASE DATE IS SPECIFIED MANDATORY IF BASE DATE IS SPECIFIED|T24002235"
    - "Mandatory if CHANGE.ACTIVITY equal to CHANGE.PRODUCT Mandatory if CHANGE.ACTIVITY
    equal to CHANGE.PRODUCT|T24002236"
    - "MANDATORY INPUT MANDATORY INPUT|T24002251"
    - "MANDATORY MESSAGE FIELD (&) MISSING MANDATORY MESSAGE FIELD (&) MISSING|T24002263"
    - "Mandatory message fields ( & ) missing# & Mandatory message fields ( & ) missing#
    &|T24002264"
    - "INVALID PROPERTY FOR ACTIVITY INVALID PROPERTY FOR ACTIVITY|T24002031"
    - "Invalid Property for the Activity Invalid Property for the Activity|T24002032"
    - "Invalid Property for this Arrangement. Invalid Property for this Arrangement.|T24002033"
    - "Invalid Property Type Accrual by Bill for scheduling Invalid Property Type Accrual
    by Bill for scheduling|T24002034"
    - "Invalid Property Type for Application Type Invalid Property Type for Application
    Type|T24002036"
    - "Invalid Property, Only Account,interest,Charges allowed Invalid Property, Only
    Account,interest,Charges allowed|T24002037"
    - "INVALID PROPERTY, ONLY CHARGE PROPERTY CLASS ALLOWED INVALID PROPERTY, ONLY
    CHARGE PROPERTY CLASS ALLOWED|T24002038"
    - "Invalid Propery type for & Defined in SCHED.CHARGE Invalid Propery type for
    & Defined in SCHED.CHARGE|T24002039"
    - "INVALID PUBLISH ID INVALID PUBLISH ID|T24002040"
    - "INVALID REF. INVALID REF.|T24002041"
    - "INVALID REFERENCE INVALID REFERENCE|T24002042"
    - "Invalid Rel.Ref Invalid Rel.Ref|T24002043"
    - "INVALID REPAYMENT TYPE INVALID REPAYMENT TYPE|T24002044"
    - "Invalid request type entered Invalid request type entered|T24002045"
    - "INVALID ROUTINE - MUST BE PGM.FILE TYPE S INVALID ROUTINE - MUST BE PGM.FILE
    TYPE S|T24002046"
    - "Invalid tier type Invalid tier type|T24002084"
    - "INVALID TIME INVALID TIME|T24002085"
    - "INVALID TOTAL FORMAT INVALID TOTAL FORMAT|T24002086"
    - "INVALID TRAINING FLAG INVALID TRAINING FLAG|T24002087"
    - "MANDATORY INPUT WHEN CHQ.NO IS INPUT MANDATORY INPUT WHEN CHQ.NO IS INPUT|T24002259"
    - "Mandatory input when Console property is defined Mandatory input when Console
    property is defined|T24002260"
    - "Mandatory input when Customer is defined Mandatory input when Customer is defined|T24002261"
    - "Mandatory Input When the corresponding & is provided Mandatory Input When the
    corresponding & is provided|T24002262"
    - "MANDATORY PROPERTY & MISSING MANDATORY PROPERTY & MISSING|T24002265"
    - "Mandatory property Balance Maintenance missing Mandatory property Balance Maintenance
    missing|T24002266"
    - "Mandatory property Charge-off missing Mandatory property Charge-off missing|T24002267"
    - "MANDATORY WHEN A NEW STMT.ENTRY.ID HAS BEEN INPUT MANDATORY WHEN A NEW STMT.ENTRY.ID
    HAS BEEN INPUT|T24002268"
    - "Mandatory when ACTION = BATCH Mandatory when ACTION = BATCH|T24002269"
    - "Mandatory when associated mapping definition is input Mandatory when associated
    mapping definition is input|T24002270"
    - "MANDATORY WHEN CHEQUE TYPE IS INPUT MANDATORY WHEN CHEQUE TYPE IS INPUT|T24002271"
    - "MANDATORY WHEN CYCLE.NO IS DEFINED MANDATORY WHEN CYCLE.NO IS DEFINED|T24002272"
    - "MANDATORY WHEN IFRS REVALUE IS YES MANDATORY WHEN IFRS REVALUE IS YES|T24002273"
    - "Mandatory when Info limit party is defined Mandatory when Info limit party is
    defined|T24002274"
    - "Mandatory when Info Limit Reference is defined Mandatory when Info Limit Reference
    is defined|T24002275"
    - "Mandatory when INPUT is defined and MAP.TO.AAA.FIELD is not defined Mandatory
    when INPUT is defined and MAP.TO.AAA.FIELD is not defined|T24002276"
    - "Mandatory When LIQU.CCY.MARKET is Entered Mandatory When LIQU.CCY.MARKET is
    Entered|T24002277"
    - "Mandatory When LIQU.FCY.THRESHOLD Entered Mandatory When LIQU.FCY.THRESHOLD
    Entered|T24002278"
    - "Mandatory When LIQU.SUSP.CATEG Is Entered Mandatory When LIQU.SUSP.CATEG Is
    Entered|T24002279"
    - "Mandatory When Mandate Ref is given Mandatory When Mandate Ref is given|T24002280"
    - "Mandatory when MAP.TO.PROPERTY is defined Mandatory when MAP.TO.PROPERTY is
    defined|T24002281"
    - "Mandatory when MAP.TO.PROPERTY.FIELD is defined Mandatory when MAP.TO.PROPERTY.FIELD
    is defined|T24002282"
    - "Mandatory when next status is valid Mandatory when next status is valid|T24002283"
    - "Mandatory when OUTPUT.TYPE is defined Mandatory when OUTPUT.TYPE is defined|T24002284"
    - "Mandatory when OUTPUT.TYPE is Property Mandatory when OUTPUT.TYPE is Property|T24002286"
    - "Mandatory when OUTPUT.TYPE is either Table or Property or Routine Mandatory
    when OUTPUT.TYPE is either Table or Property or Routine|T24002285"
    - "Mandatory when OUTPUT.TYPE is Routine Mandatory when OUTPUT.TYPE is Routine|T24002287"
    - "Mandatory when OUTPUT.TYPE is Table Mandatory when OUTPUT.TYPE is Table|T24002288"
    - "MANDATORY WHEN PERIODIC VALUE GIVEN MANDATORY WHEN PERIODIC VALUE GIVEN|T24002289"
    - "Mandatory when POST.SIM.DATA is defined Mandatory when POST.SIM.DATA is defined|T24002290"
    - "Mandatory when POST.SIM.ROUTINE is defined Mandatory when POST.SIM.ROUTINE is
    defined|T24002291"
    - "Mandatory when SCENARIO is defined Mandatory when SCENARIO is defined|T24002292"
    - "Mandatory when SCENARIO.SIM.TYPE is defined Mandatory when SCENARIO.SIM.TYPE
    is defined|T24002295"
    - "Mandatory when SIG.REQD is YES Mandatory when SIG.REQD is YES|T24002296"
    - "Mandatory when SIM.TYPE is defined Mandatory when SIM.TYPE is defined|T24002297"
    - "Mandatory when SIM.TYPE.NAME is defined Mandatory when SIM.TYPE.NAME is defined|T24002298"
    - "MANDATORY WHEN TXN.STATUS HAS BEEN CHANGED MANDATORY WHEN TXN.STATUS HAS BEEN
    CHANGED|T24002299"
    - "Mandatory with Advance Disbursement Mandatory with Advance Disbursement|T24002300"
    - "MANDATORY WITH FLD MM.OFS.VERSION MANDATORY WITH FLD MM.OFS.VERSION|T24002301"
    - "MANDATORY WITH FLD OFS.SOURCE MANDATORY WITH FLD OFS.SOURCE|T24002302"
    - "Mandatory with Product Type Mandatory with Product Type|T24002303"
    - "Mandtory When LIQU.FT.TYPE is Entered Mandtory When LIQU.FT.TYPE is Entered|T24002304"
    - "MANUAL INPUT NOT POSSIBLE FOR ERR OR RR TYPE MANUAL INPUT NOT POSSIBLE FOR ERR
    OR RR TYPE|T24002305"
    - "Mapping table (&) not present# Mapping table (&) not present#|T24002306"
    - "Margin is mandatory if operand is specified Margin is mandatory if operand is
    specified|T24002307"
    - "Margin not allowed when Fixed rate is defined Margin not allowed when Fixed
    rate is defined|T24002308"
    - "Margin Rate is Numeric Value only Margin Rate is Numeric Value only|T24002309"
    - "MARGIN RATE MUST BE BETWEEN -100 TO 100 MARGIN RATE MUST BE BETWEEN -100 TO
    100|T24002310"
    - "Marked for closure with SINGLE in ACCOUNT.PARAMETER. Marked for closure with
    SINGLE in ACCOUNT.PARAMETER.|T24002311"
    - "Market Key is not present in PI Table. Market Key is not present in PI Table.|T24002312"
    - "MARKET.KEY is mandatory for market Margin MARKET.KEY is mandatory for market
    Margin|T24002313"
    - "MARKET.MARGIN is Mandatory for Margin operand MARKET.MARGIN is Mandatory for
    Margin operand|T24002314"
    - "MASK CAN ONLY DEFINED WITH THE Ns MASK CAN ONLY DEFINED WITH THE Ns|T24002315"
    - "MASK CHARACTERS DUPLICATED MASK CHARACTERS DUPLICATED|T24002316"
    - "MASK MUST CONTAIN & #-CHAR. MASK MUST CONTAIN & #-CHAR.|T24002317"
    - "MASKING INVALID FOR TEXT MASKING INVALID FOR TEXT|T24002318"
    - "Master Can be Set for single Condition Master Can be Set for single Condition|T24002323"
    - "Master can be set only for mandatory  Master can be set only for mandatory
    |T24002324"
    - "MATCH FIELD MUST BE BLANK MATCH FIELD MUST BE BLANK|T24002325"
    - "MATCH FIELD NOT DEFINITED MATCH FIELD NOT DEFINITED|T24002326"
    - "MATCH IS MISSING MATCH IS MISSING|T24002327"
    - "MATCH RECORD ID IS MISSING MATCH RECORD ID IS MISSING|T24002328"
    - "MATCHED IN UNAUTHORISED FILE MATCHED IN UNAUTHORISED FILE|T24002329"
    - "Matching message direction should be opposite Matching message direction should
    be opposite|T24002330"
    - "MATCHING RECORD CURRENCY DIFFERENT MATCHING RECORD CURRENCY DIFFERENT|T24002331"
    - "MATCHING RECORD NOT FOUND MATCHING RECORD NOT FOUND|T24002332"
    - "Maturity date cannot be less than arr start date Maturity date cannot be less
    than arr start date|T24002333"
    - "Maturity Date cannot be less than Effective date Maturity Date cannot be less
    than Effective date|T24002334"
    - "Maturity date cannot be less than original start date Maturity date cannot be
    less than original start date|T24002335"
    - "MAX CANNOT BE LESS THAN MIN MAX CANNOT BE LESS THAN MIN|T24002336"
    - "MAX CAP AMOUNT CANNOT BE EMPTY MAX CAP AMOUNT CANNOT BE EMPTY|T24002337"
    - "Max ID Length is 15 for this class Max ID Length is 15 for this class|T24002338"
    - "Max of 98 allowed on Position Accounts Max of 98 allowed on Position Accounts|T24002339"
    - "MAX. OF 4 MULTI-VALUES MAX. OF 4 MULTI-VALUES|T24002340"
    - "MAX. OF 6 MULTI-VALUES MAX. OF 6 MULTI-VALUES|T24002342"
    - "MAX.CHG.AMT should be greater than MIN.CHG.AMT MAX.CHG.AMT should be greater
    than MIN.CHG.AMT|T24002344"
    - "MAX.RATE CONTRADICTS MARGIN.RATE MAX.RATE CONTRADICTS MARGIN.RATE|T24002345"
    - "MAXIMUM 4 LINES ALLOWED MAXIMUM 4 LINES ALLOWED|T24002346"
    - "MAXIMUM ACCT NO LENGTH & MAXIMUM ACCT NO LENGTH &|T24002347"
    - "Maximum allowed length is 16 & Maximum allowed length is 16 &|T24002348"
    - "MAXIMUM CUSTOMER LENGTH IS 10 MAXIMUM CUSTOMER LENGTH IS 10|T24002349"
    - "Limit needs to be removed before changing customer Limit needs to be removed
    before changing customer|T24002149"
    - "Limit not managed by AA Limit not managed by AA|T24002150"
    - "LIMIT REF SHOULD NOT SPECIFIED FOR CONTINGENT ACCOUNT LIMIT REF SHOULD NOT SPECIFIED
    FOR CONTINGENT ACCOUNT|T24002151"
    - "LIMIT REF. NOT ALLOWED FOR NOSTRO LIMIT REF. NOT ALLOWED FOR NOSTRO|T24002152"
    - "LIMIT REFERENCE AND ID DO NOT MATCH LIMIT REFERENCE AND ID DO NOT MATCH|T24002153"
    - "Limit reference cannot be NULL Limit reference cannot be NULL|T24002154"
    - "LIMIT REFERENCE is not specified. LIMIT REFERENCE is not specified.|T24002155"
    - "LIMIT.CANT BE ATTACHED AS COLLATERAL PERCENT MENTIONED LIMIT.CANT BE ATTACHED
    AS COLLATERAL PERCENT MENTIONED|T24002156"
    - "LINE NO. MUST NOT BE > & LINE NO. MUST NOT BE > &|T24002157"
    - "LINE.NO MUST BE > 0 LINE.NO MUST BE > 0|T24002158"
    - "LINE.NO MUST NOT BE > & LINE.NO MUST NOT BE > &|T24002159"
    - "LINES NOT IN SEQUENCE LINES NOT IN SEQUENCE|T24002160"
    - "Link Enquiry Not Input Link Enquiry Not Input|T24002161"
    - "Link type should be NON.TRACKING Link type should be NON.TRACKING|T24002162"
    - "Linked Arrangement cannot be defined as CHILD Linked Arrangement cannot be defined
    as CHILD|T24002163"
    - "Linked Arrangement cannot be defined as PARENT Linked Arrangement cannot be
    defined as PARENT|T24002164"
    - "Linked arrangement cant be anything other than AR,AD Linked arrangement cant
    be anything other than AR,AD|T24002165"
    - "Linked Arrangement is not available Linked Arrangement is not available|T24002166"
    - "Linked Arrangement should be inputted Linked Arrangement should be inputted|T24002167"
    - "MUST BE NINE UNITS MUST BE NINE UNITS|T24002568"
    - "MUST BE NUMERIC OR ALL MUST BE NUMERIC OR ALL|T24002570"
    - "MUST BE NUMERIC MUST BE NUMERIC|T24002569"
    - "MUST BE OF TYPE ACCOUNT MUST BE OF TYPE ACCOUNT|T24002571"
    - "MASKING INVALID WITH WORDS CONV. MASKING INVALID WITH WORDS CONV.|T24002319"
    - "MASKING INVALID WITH WORDSCCY CONV. MASKING INVALID WITH WORDSCCY CONV.|T24002320"
    - "MASKING INVALID WITH WORDSCCY*xxx CONV. MASKING INVALID WITH WORDSCCY*xxx CONV.|T24002321"
    - "MESSAGE TYPE MUST BE ENTERED MESSAGE TYPE MUST BE ENTERED|T24002372"
    - "Message Type value to be specified Message Type value to be specified|T24002373"
    - "MESSAGE.TYPE FIELD NOT SET IN ACCT.STMT MESSAGE.TYPE FIELD NOT SET IN ACCT.STMT|T24002374"
    - "MESSAGES AWAITING FORMATTING MESSAGES AWAITING FORMATTING|T24002375"
    - "MESSAGES AWAITING MAPPING MESSAGES AWAITING MAPPING|T24002376"
    - "MIN CANNOT BE GREATER THAN MAX MIN CANNOT BE GREATER THAN MAX|T24002377"
    - "MIN.RATE CONTRADICTS MARGIN.RATE MIN.RATE CONTRADICTS MARGIN.RATE|T24002378"
    - "MINIMUM = 10 SECS MINIMUM = 10 SECS|T24002379"
    - "Minimum Amount Allowed only Bill type having a value Minimum Amount Allowed
    only Bill type having a value|T24002380"
    - "MINIMUM BALANCE CANNOT BE LESS THAN MAXIMUM BALANCE MINIMUM BALANCE CANNOT BE
    LESS THAN MAXIMUM BALANCE|T24002381"
    - "Minimum Balance Cant Greater Than Maximum Balance Minimum Balance Cant Greater
    Than Maximum Balance|T24002382"
    - "MINIMUM LENGTH GREATER THAN MAXIMUM MINIMUM LENGTH GREATER THAN MAXIMUM|T24002383"
    - "MINIMUM.AMT MUST BE LESS THAN CORRESPONDING MAXIMUM.AMT MINIMUM.AMT MUST BE
    LESS THAN CORRESPONDING MAXIMUM.AMT|T24002384"
    - "MISSING & - REASON CODE MISSING & - REASON CODE|T24002385"
    - "MISSING & - RECORD MISSING & - RECORD|T24002386"
    - "MISSING & RECORD & MISSING & RECORD &|T24002387"
    - "MISSING & RECORD -& MISSING & RECORD -&|T24002388"
    - "MISSING AC.BALANCE.TYPE & FOR PROPERTY & MISSING AC.BALANCE.TYPE & FOR PROPERTY
    &|T24002389"
    - "Missing Account balance type in Payout rules Missing Account balance type in
    Payout rules|T24002390"
    - "MISSING ACCOUNT FILE RECORD - & MISSING ACCOUNT FILE RECORD - &|T24002391"
    - "MISSING ACCOUNT RECORD MISSING ACCOUNT RECORD|T24002392"
    - "MISSING ACCT.GEN.CONDITION RECORD MISSING ACCT.GEN.CONDITION RECORD|T24002393"
    - "MISSING ACTIVITY & MISSING ACTIVITY &|T24002394"
    - "MISSING ACTIVITY DATE MISSING ACTIVITY DATE|T24002395"
    - "MISSING ACTIVITY PRESENTATION RECORD MISSING ACTIVITY PRESENTATION RECORD|T24002396"
    - "MISSING ACTIVITY TYPE MISSING ACTIVITY TYPE|T24002397"
    - "MISSING ADDRESS FOR CUSTOMER : &|T24002398"
    - "MISSING API RECORD MISSING API RECORD|T24002399"
    - "MISSING APPLICATION LEVEL MISSING APPLICATION LEVEL|T24002400"
    - "Missing Application Name Missing Application Name|T24002401"
    - "Missing Arrangement & Missing Arrangement &|T24002402"
    - "MISSING ARRANGEMENT ID MISSING ARRANGEMENT ID|T24002404"
    - "MISSING ARRAY FOR & - ID = & MISSING ARRAY FOR & - ID = &|T24002405"
    - "Missing Arrear Account Missing Arrear Account|T24002406"
    - "MISSING BATCH JOB MISSING BATCH JOB|T24002407"
    - "MISSING BILL REFERENCE MISSING BILL REFERENCE|T24002408"
    - "MISSING CARRIER ON ADDRESS FILE MISSING CARRIER ON ADDRESS FILE|T24002409"
    - "MISSING CATEGORY - RECORD MISSING CATEGORY|T24002410"
    - "MISSING CHARGE CATEGORY FOR THIS AC MISSING CHARGE CATEGORY FOR THIS AC|T24002411"
    - "MISSING CHARGE CATEGORY FOR THIS ACCOUNT MISSING CHARGE CATEGORY FOR THIS ACCOUNT|T24002412"
    - "MISSING CLEARING SYSTEM MISSING CLEARING SYSTEM|T24002413"
    - "MISSING COMPANY CODE IN ENTRY MISSING COMPANY CODE IN ENTRY|T24002414"
    - "Missing Comparison Value for Rule Missing Comparison Value for Rule|T24002415"
    - "MISSING CONDITION FOR DEPENDANCY MISSING CONDITION FOR DEPENDANCY|T24002416"
    - "MISSING CONDITION RECORD MISSING CONDITION RECORD|T24002417"
    - "MISSING CR CATEGORY FOR THIS AC MISSING CR CATEGORY FOR THIS AC|T24002418"
    - "Missing Currency rule Missing Currency rule|T24002420"
    - "Missing currency Missing currency|T24002419"
    - "MISSING CUSTOMER - RECORD Invalid MISSING CUSTOMER - RECORD|T24003792"
    - "Missing Customer Number Missing Customer Number|T24002421"
    - "Missing customer or product Missing customer or product|T24002422"
    - "MISSING DE.MESSAGE/REPORT.MNEMONIC RECORD MISSING DE.MESSAGE/REPORT.MNEMONIC
    RECORD|T24002423"
    - "MISSING DEBIT CATEGORY FOR THIS ACCOUNT MISSING DEBIT CATEGORY FOR THIS ACCOUNT|T24002424"
    - "Missing definitions for EVENT.TYPE|T24002425"
    - "MISSING DEPT.ACCT.OFFICER RECORD MISSING DEPT.ACCT.OFFICER RECORD|T24002426"
    - "MISSING DR CATEGORY FOR THIS AC MISSING DR CATEGORY FOR THIS AC|T24002427"
    - "MISSING DR2 CATEG FOR THIS AC MISSING DR2 CATEG FOR THIS AC|T24002428"
    - "MISSING EFFECTIVE DATE MISSING EFFECTIVE DATE|T24002429"
    - "Missing Failure Action Missing Failure Action|T24002430"
    - "Missing Failure type Missing Failure type|T24002431"
    - "MISSING FILE = F.ACCOUNT.ACCRUAL ID = & MISSING FILE = F.ACCOUNT.ACCRUAL ID
    = &|T24002432"
    - "MISSING FILE = F.CUSTOMER ID = & MISSING FILE = F.CUSTOMER ID = &|T24002433"
    - "MISSING FILE= & MISSING FILE= &|T24002434"
    - "MISSING FILE=& ID=& F.ACCOUNT MISSING FILE=& ID=& F.ACCOUNT|T24002437"
    - "MISSING FILE=& ID=& F.CUSTOMER MISSING FILE=& ID=& F.CUSTOMER|T24002439"
    - "MISSING FILE=& ID=& MISSING FILE=& ID=&|T24002435"
    - "MISSING FILE=F.ACCOUNT ID = & MISSING FILE=F.ACCOUNT ID = &|T24002440"
    - "MISSING FILE=F.ACCOUNT ID= & MISSING FILE=F.ACCOUNT ID= &|T24002441"
    - "MISSING FILE=F.ACCOUNT.ACCRUAL ID= & MISSING FILE=F.ACCOUNT.ACCRUAL ID= &|T24002442"
    - "MISSING IN FILE & & MISSING IN FILE & &|T24002443"
    - "MISSING IN STOCK.REGISTER MISSING IN STOCK.REGISTER|T24002444"
    - "MISSING LIMIT AMOUNT MISSING LIMIT AMOUNT|T24002445"
    - "MM.MONEY.MARKET - PRODUCT NOT INSTALLED MM.MONEY.MARKET - PRODUCT NOT INSTALLED|T24002480"
    - "Mandatory if CHANGE.DATE.TYPE equal to Date Mandatory if CHANGE.DATE.TYPE equal
    to Date|T24002237"
    - "Mandatory if CHANGE.DATE.TYPE equal to null Mandatory if CHANGE.DATE.TYPE equal
    to null|T24002238"
    - "Mandatory if CHANGE.DATE.TYPE equal to PERIOD Mandatory if CHANGE.DATE.TYPE
    equal to PERIOD|T24002239"
    - "Mandatory if CHANGE.DATE.TYPE is SET Mandatory if CHANGE.DATE.TYPE is SET|T24002240"
    - "Mandatory if Charge is input Mandatory if Charge is input|T24002241"
    - "Mandatory if CHARGEOFF.ORDER is defined Mandatory if CHARGEOFF.ORDER is defined|T24002242"
    - "Mandatory If INITIATION TYPE is Set Mandatory If INITIATION TYPE is Set|T24002243"
    - "Mandatory if INITIATION.TYPE is AUTO Mandatory if INITIATION.TYPE is AUTO|T24002244"
    - "Mandatory if INITIATION.TYPE is MANUAL Mandatory if INITIATION.TYPE is MANUAL|T24002245"
    - "MANDATORY IF INTERMEDIARY BANK IS SET MANDATORY IF INTERMEDIARY BANK IS SET|T24002246"
    - "MANDATORY IF NOT LINK TO CUSTOMER FILE MANDATORY IF NOT LINK TO CUSTOMER FILE|T24002247"
    - "MANDATORY IF PERIOD IS SPECIFIED MANDATORY IF PERIOD IS SPECIFIED|T24002248"
    - "Mandatory if Property is stated Mandatory if Property is stated|T24002249"
    - "MANDATORY IF TYPE IS ENTERED MANDATORY IF TYPE IS ENTERED|T24002250"
    - "Mandatory input for contingent accounts Mandatory input for contingent accounts|T24002254"
    - "Mandatory input for CONTINGENT.INT type B Mandatory input for CONTINGENT.INT
    type B|T24002255"
    - "MANDATORY INPUT FOR GENERIC CARRIER MANDATORY INPUT FOR GENERIC CARRIER|T24002256"
    - "MANDATORY INPUT FOR NEW ARRANGEMENT MANDATORY INPUT FOR NEW ARRANGEMENT|T24002257"
    - "MANDATORY INPUT WHEN CHQ.NO AND CHQ.TYP IS INPUT MANDATORY INPUT WHEN CHQ.NO
    AND CHQ.TYP IS INPUT|T24002258"
    - "MISSING npipes LOCATION MISSING npipes LOCATION|T24002449"
    - "MISSING NUMERIC.CURRENCY - RECORD MISSING NUMERIC.CURRENCY - RECORD|T24003812"
    - "MISSING PARAMETER RECORD MISSING PARAMETER RECORD|T24002450"
    - "MISSING PARENT PRODUCT & FOR & MISSING PARENT PRODUCT & FOR &|T24002451"
    - "MISSING PM PARAMETER RECORD MISSING PM PARAMETER RECORD|T24002452"
    - "MISSING PROCESS TYPE MISSING PROCESS TYPE|T24002453"
    - "MISSING PRODUCT LINE MISSING PRODUCT LINE|T24002454"
    - "Missing product or arrangement Missing product or arrangement|T24002455"
    - "MISSING PROPERTY CLASS MISSING PROPERTY CLASS|T24002458"
    - "MISSING PROPERTY MISSING PROPERTY|T24002456"
    - "MISSING QUANTITY OF SECURITIES MISSING QUANTITY OF SECURITIES|T24002459"
    - "MISSING REC IN FILE & & MISSING REC IN FILE & &|T24002460"
    - "Missing RECIPIENT Property Missing RECIPIENT Property|T24002461"
    - "MISSING RECORD FILE=F.ACCOUNT, ID= & MISSING RECORD FILE=F.ACCOUNT, ID= &|T24002462"
    - "MISSING REFERAL: &|T24002463"
    - "MISSING REPORT CONTROL & MISSING REPORT CONTROL &|T24002464"
    - "Missing Review frequency Missing Review frequency|T24002465"
    - "MISSING RULE MISSING RULE|T24002466"
    - "Missing SMS adresss Missing SMS adresss|T24002468"
    - "MOVEMENT.TYPE MISSING MOVEMENT.TYPE MISSING|T24002486"
    - "MSG TYPE LENGTH INCORRECT MSG TYPE LENGTH INCORRECT|T24002487"
    - "MSG TYPE MISSING FOR FT MSG TYPE MISSING FOR FT|T24002488"
    - "MSG TYPE MUST BE IN THE RANGE (9000-9999) MSG TYPE MUST BE IN THE RANGE (9000-9999)|T24002489"
    - "MSG TYPE MUST NOT BE 0 MSG TYPE MUST NOT BE 0|T24002490"
    - "MSG WILL NOT BE PROCESSED THRU STP MSG WILL NOT BE PROCESSED THRU STP|T24002491"
    - "MSG.TYPE required MSG.TYPE required|T24002492"
    - "MULTI FLOOR LIMIT CCY NOT ALLOWED MULTI FLOOR LIMIT CCY NOT ALLOWED|T24002493"
    - "MULTI MUST BE S MULTI MUST BE S|T24002494"
    - "Multi Value not allowed for Context Type Multi Value not allowed for Context
    Type|T24002495"
    - "MULTI VALUE NOT ALLOWED WITH CUSTOMER NUMBER MULTI VALUE NOT ALLOWED WITH CUSTOMER
    NUMBER|T24002496"
    - "MULTI VALUE NOT ALLOWED WITH SWIFT ADDRESS MULTI VALUE NOT ALLOWED WITH SWIFT
    ADDRESS|T24002497"
    - "MULTI.CUST HAS TO CO-EXIST WITH OVR.DEL.ADD MULTI.CUST HAS TO CO-EXIST WITH
    OVR.DEL.ADD|T24002498"
    - "MULTIPLE ACTIONS NOT ALLOWED MULTIPLE ACTIONS NOT ALLOWED|T24002499"
    - "MULTIPLE CLASSES NOT ALLOWED FOR CALC TYPE MULTIPLE CLASSES NOT ALLOWED FOR
    CALC TYPE|T24002500"
    - "Multiple collateral under the same collateral right Multiple collateral under
    the same collateral right|T24002501"
    - "Multiple Customers defined! Relative Option BIRTH not allowed Multiple Customers
    defined! Relative Option BIRTH not allowed|T24002503"
    - "MULTIPLE DATES NOT ALLOWED FOR CALC TYPE MULTIPLE DATES NOT ALLOWED FOR CALC
    TYPE|T24002504"
    - "Multiple fixed interest property not allowed Multiple fixed interest property
    not allowed|T24002505"
    - "Multiple margin rates are not allowed Multiple margin rates are not allowed|T24002506"
    - "MULTIPLE NULL FIELDS MULTIPLE NULL FIELDS|T24002507"
    - "Multiple PAYIN account not allowed when RC enabled Multiple PAYIN account not
    allowed when RC enabled|T24002508"
    - "Multiple payment types found in overlapping periods Multiple payment types found
    in overlapping periods|T24002509"
    - "Multiple Properties not allowed for APPL.TYPE Multiple Properties not allowed
    for APPL.TYPE|T24002511"
    - "MULTIPLE PROPERTIES NOT ALLOWED FOR CALC TYPE MULTIPLE PROPERTIES NOT ALLOWED
    FOR CALC TYPE|T24002512"
    - "Multiple Properties not allowed for PIA APPL.TYPE Multiple Properties not allowed
    for PIA APPL.TYPE|T24002513"
    - "MULTIPLE PROPERTIES NOT ALLOWED MULTIPLE PROPERTIES NOT ALLOWED|T24002510"
    - "Multivalue set mandatory Multivalue set mandatory|T24002514"
    - "MUST BE 0 MUST BE 0|T24002515"
    - "MUST BE 1 ALPHA AFTER 1ST 3 CHARS MUST BE 1 ALPHA AFTER 1ST 3 CHARS|T24002516"
    - "MUST BE 1 OR BLANK FOR CARRIER MUST BE 1 OR BLANK FOR CARRIER|T24002517"
    - "MUST BE 1 TO 3 DIGITS FOLLOWED BY W MUST BE 1 TO 3 DIGITS FOLLOWED BY W|T24002518"
    - "MUST BE 6 OR 8 CHARACTERS FOR SWIFT MUST BE 6 OR 8 CHARACTERS FOR SWIFT|T24002519"
    - "MUST BE 8 OR 11 CHARACTERS MUST BE 8 OR 11 CHARACTERS|T24002520"
    - "MUST BE 8 OR 11 CHARS MUST BE 8 OR 11 CHARS|T24002521"
    - "MUST BE 9 OR 12 CHARS MUST BE 9 OR 12 CHARS|T24002522"
    - "MUST BE A DIRECTORY MUST BE A DIRECTORY|T24002523"
    - "Must be a Globus-Acct for F.CCY Must be a Globus-Acct for F.CCY|T24002525"
    - "Must be a specified multiple (&) Must be a specified multiple (&)|T24002526"
    - "MUST BE A VALID CARRIER MUST BE A VALID CARRIER|T24002527"
    - "Must be a valid id on DE.MESSAGE file Must be a valid id on DE.MESSAGE file|T24002529"
    - "MUST BE A VALID ID ON DE.MESSAGE MUST BE A VALID ID ON DE.MESSAGE|T24002528"
    - "MUST BE BLANK FOR ACCOUNT & WITH CATEGORY & MUST BE BLANK FOR ACCOUNT & WITH
    CATEGORY &|T24002531"
    - "MUST BE BLANK FOR LOCAL CCY MUST BE BLANK FOR LOCAL CCY|T24002532"
    - "MUST BE BLANK FOR RECEIPTS MUST BE BLANK FOR RECEIPTS|T24002533"
    - "MUST BE BLANK FOR RESIDENT LOCAL CCY MUST BE BLANK FOR RESIDENT LOCAL CCY|T24002534"
    - "MUST BE BLANK IF DR.INT.RATE EXISTS MUST BE BLANK IF DR.INT.RATE EXISTS|T24002535"
    - "MUST BE BLANK IF DR2.INT.RATE EXISTS MUST BE BLANK IF DR2.INT.RATE EXISTS|T24002536"
    - "MUST BE BLANK IF INT.RATE EXISTS MUST BE BLANK IF INT.RATE EXISTS|T24002537"
    - "MUST BE BLANK MUST BE BLANK|T24002530"
    - "MUST BE CCY*currency location MUST BE CCY*currency location|T24002538"
    - "MUST BE CH RESIDENT FOR SIC MUST BE CH RESIDENT FOR SIC|T24002539"
    - "Must be contingent account for MINUS or BOTH Must be contingent account for
    MINUS or BOTH|T24002540"
    - "MUST BE LESS THAN CR.MINIMUM.BAL MUST BE LESS THAN CR.MINIMUM.BAL|T24002563"
    - "MUST BE LESS THAN CR2.MINIMUM.BAL MUST BE LESS THAN CR2.MINIMUM.BAL|T24002564"
    - "MUST BE LESS THAN TO.CATEGORY MUST BE LESS THAN TO.CATEGORY|T24002565"
    - "MUST BE LT CLAIM DATE IN ITEM MUST BE LT CLAIM DATE IN ITEM|T24002566"
    - "Must be LT previous Future set Must be LT previous Future set|T24002567"
    - "Mass Block Already Expired Mass Block Already Expired|T24002322"
    - "MAXIMUM DISPLAY MASK LENGTH & MAXIMUM DISPLAY MASK LENGTH &|T24002350"
    - "Maximum forward date exceeds Maximum forward date exceeds|T24002351"
    - "MAXIMUM INDENTATION (1-&) MAXIMUM INDENTATION (1-&)|T24002352"
    - "MAXIMUM MUST BE > MINIMUM MAXIMUM MUST BE > MINIMUM|T24002353"
    - "MAXIMUM NO OF LINES EXCEEDED MAXIMUM NO OF LINES EXCEEDED|T24002354"
    - "Maximum of 999 sub accounts allowed Maximum of 999 sub accounts allowed|T24002355"
    - "Maximum tax percentage should not exceed 100 Maximum tax percentage should not
    exceed 100|T24002356"
    - "Maximum three Simulations can be compared Maximum three Simulations can be compared|T24002357"
    - "Maximum Value Exceeded (&) Maximum Value Exceeded (&)|T24002358"
    - "MAXIMUM.AMT MUST BE GREATER THAN CORRESPONDING MINIMUM.AMT MAXIMUM.AMT MUST
    BE GREATER THAN CORRESPONDING MINIMUM.AMT|T24002359"
    - "MESG NOT REMOVED FROM INWARD FILE MESG NOT REMOVED FROM INWARD FILE|T24002360"
    - "MESSAGE ALREADY MATCHED MESSAGE ALREADY MATCHED|T24002361"
    - "Message Class defined not equal default Message Class Message Class defined
    not equal default Message Class|T24002362"
    - "MESSAGE DETAILS CANNOT BE AMENDED MESSAGE DETAILS CANNOT BE AMENDED|T24002363"
    - "MESSAGE MUST BE RESUBMITTED MESSAGE MUST BE RESUBMITTED|T24002364"
    - "MESSAGE RECORD DOES NOT EXIST MESSAGE RECORD DOES NOT EXIST|T24002365"
    - "Message table (&) not present# Message table (&) not present#|T24002366"
    - "MESSAGE TYPE ALREADY DEFINED MESSAGE TYPE ALREADY DEFINED|T24002367"
    - "Message Type belongs to CAMT, only ISOREPORT is allowed Message Type belongs
    to CAMT, only ISOREPORT is allowed|T24002368"
    - "Message Type doesnt belong to CAMT Message Type doesnt belong to CAMT|T24002369"
    - "MESSAGE TYPE LENGTH INCORRECT MESSAGE TYPE LENGTH INCORRECT|T24002370"
    - "MESSAGE TYPE MISSING MESSAGE TYPE MISSING|T24002371"
    - "MUST BE IN ASCENDING ORDER MUST BE IN ASCENDING ORDER|T24002557"
    - "Must be in Range 90 to 99 Must be in Range 90 to 99|T24002558"
    - "MUST BE IN THE RANGE 1 - 9 MUST BE IN THE RANGE 1 - 9|T24002559"
    - "MUST BE INPUT FOR THIS TXN CODE MUST BE INPUT FOR THIS TXN CODE|T24002560"
    - "MUST BE INPUT IF DATA CAPTURE IS Y MUST BE INPUT IF DATA CAPTURE IS Y|T24002561"
    - "MUST BE LESS THAN CORRESPONDING MAXIMUM.AMT MUST BE LESS THAN CORRESPONDING
    MAXIMUM.AMT|T24002562"
    - "MUST BE OF TYPE I OR D MUST BE OF TYPE I OR D|T24002572"
    - "MUST BE POSITION ACCOUNT MUST BE POSITION ACCOUNT|T24002573"
    - "MUST BE REROUTE OR RESUBMIT MUST BE REROUTE OR RESUBMIT|T24002574"
    - "MUST BE SAME GROUP AS RECORD ID MUST BE SAME GROUP AS RECORD ID|T24002575"
    - "MUST BE SAME GROUP AS RECORD KEY MUST BE SAME GROUP AS RECORD KEY|T24002576"
    - "MUST BE SEPARATED BY - MUST BE SEPARATED BY -|T24002577"
    - "MUST BE SET TO YES IN PARAMETER MUST BE SET TO YES IN PARAMETER|T24002579"
    - "MUST BE SET TO YES MUST BE SET TO YES|T24002578"
    - "MUST BE SPECFIED FOR AMOUNT CONVERSION MUST BE SPECFIED FOR AMOUNT CONVERSION|T24002580"
    - "MUST BE TWO UNITS MUST BE TWO UNITS|T24002581"
    - "MUST BE WORDSCCY*currency location MUST BE WORDSCCY*currency location|T24002582"
    - "MUST BE WORKING OR VALUE.DATED FOR SHARED BALANCES MUST BE WORKING OR VALUE.DATED
    FOR SHARED BALANCES|T24002583"
    - "MUST BE X99 OR X99-999 FORMAT MUST BE X99 OR X99-999 FORMAT|T24002584"
    - "Must be Y for ICA.POST.INTEREST INFO Must be Y for ICA.POST.INTEREST
    INFO|T24002585"
    - "MUST BE Y WHEN NOSTRO-ACCOUNT MUST BE Y WHEN NOSTRO-ACCOUNT|T24002586"
    - "Must be YES for main account Must be YES for main account|T24002587"
    - "MUST BEGIN WITH & MUST BEGIN WITH &|T24002588"
    - "Must contain a status Must contain a status|T24002590"
    - "Must enter 1 amount Must enter 1 amount|T24002591"
    - "MUST ENTER A TELEX LINE NUMBER MUST ENTER A TELEX LINE NUMBER|T24002592"
    - "MUST ENTER BENEF.CUST.1 OR BEN.ADDRESS MUST ENTER BENEF.CUST.1 OR BEN.ADDRESS|T24002593"
    - "MUST ENTER THE RETURN SUSPENSE CATEGORY MUST ENTER THE RETURN SUSPENSE CATEGORY|T24002598"
    - "MUST HAVE ENTRY CATEGORY ENTERED MUST HAVE ENTRY CATEGORY ENTERED|T24002599"
    - "MUST HAVE PGM ENTRY MUST HAVE PGM ENTRY|T24002600"
    - "Must input a category Must input a category|T24002601"
    - "must input main account must input main account|T24002602"
    - "Must Input NEW.MAIN.ACC with date Must Input NEW.MAIN.ACC with date|T24002603"
    - "MUST INPUT ROUTINE WITH VERSION MUST INPUT ROUTINE WITH VERSION|T24002604"
    - "MUST INPUT VERSION WITH ROUTINE MUST INPUT VERSION WITH ROUTINE|T24002605"
    - "MUST NOT BE > & MUST NOT BE > &|T24002606"
    - "MUST NOT BE A CARRIER NAME MUST NOT BE A CARRIER NAME|T24002607"
    - "MUST NOT BE BEFORE TODAY MUST NOT BE BEFORE TODAY|T24002608"
    - "MUST NOT BE BLANK AS CONDITION IS ENTERED MUST NOT BE BLANK AS CONDITION IS
    ENTERED|T24002609"
    - "MUST NOT BE NEGATIVE MUST NOT BE NEGATIVE|T24002610"
    - "MUST NOT BE SAME AS FORMAT.MODULE MUST NOT BE SAME AS FORMAT.MODULE|T24002611"
    - "MUST NOT BE THE SAME AS CARRIER MODULE MUST NOT BE THE SAME AS CARRIER MODULE|T24002612"
    - "MUST NOT EXCEED 4 LINES MUST NOT EXCEED 4 LINES|T24002613"
    - "MUST SAME AS CUSTOMER OF ID ACCOUNT MUST SAME AS CUSTOMER OF ID ACCOUNT|T24002614"
    - "MUST SPECIFY EITHER INCLUDE OR EXCLUDE CURRENCY(S) MUST SPECIFY EITHER INCLUDE
    OR EXCLUDE CURRENCY(S)|T24002615"
    - "Must tally to inbound channels for this opportunity Must tally to inbound channels
    for this opportunity|T24002616"
    - "NEXT CONDITION MISSING NEXT CONDITION MISSING|T24002653"
    - "NEXT FIELDS MISSING NEXT FIELDS MISSING|T24002654"
    - "Mutually Exclusive Fields. Only one of them can be specified Mutually Exclusive
    Fields. Only one of them can be specified|T24002617"
    - "MX Product not installed MX Product not installed|T24002618"
    - "NARR.FORMAT FIELD IS MANDATORY NARR.FORMAT FIELD IS MANDATORY|T24002619"
    - "Negative amount allowed only for Charge Off Activities Negative amount allowed
    only for Charge Off Activities|T24002620"
    - "Negative Days not allowed Negative Days not allowed|T24002621"
    - "Negative Interest Not Allowed For Constant Payment type Negative Interest Not
    Allowed For Constant Payment type|T24002622"
    - "Negative or zero amt not allowed in delinquent amount Negative or zero amt not
    allowed in delinquent amount|T24002623"
    - "Negative rate is not allowed for Advance interest Negative rate is not allowed
    for Advance interest|T24002624"
    - "NEGATIVE RATE NOT ALLOWED NEGATIVE RATE NOT ALLOWED|T24002625"
    - "NEGATIVE SIGN NOT ALLOWED NEGATIVE SIGN NOT ALLOWED|T24002626"
    - "NEGATIVE.RATE SHOULD BE YES NEGATIVE.RATE SHOULD BE YES|T24002627"
    - "NEGOTIATION RULES DEFINED, NEGOTIABLE OPTION MANDATORY NEGOTIATION RULES DEFINED,
    NEGOTIABLE OPTION MANDATORY|T24002628"
    - "Net Total must be zero Net Total must be zero|T24002629"
    - "Netting Agreement has expired Netting Agreement has expired|T24002630"
    - "Netting Agreement set for FT only Netting Agreement set for FT only|T24002631"
    - "Netting Agreement set for FX only Netting Agreement set for FX only|T24002632"
    - "NEW FIELDS MUST BE APPENDED, CANNOT REMOVE OLD FIELDS NEW FIELDS MUST BE APPENDED,
    CANNOT REMOVE OLD FIELDS|T24002643"
    - "New Live Arrangement not allowed in Simulation mode New Live Arrangement not
    allowed in Simulation mode|T24002644"
    - "New messages cannot be entered New messages cannot be entered|T24002645"
    - "New product type and Existing Product type are same New product type and Existing
    Product type are same|T24002646"
    - "New property amount cannot be less than zero New property amount cannot be less
    than zero|T24002647"
    - "New record name must be SYSTEM New record name must be SYSTEM|T24002648"
    - "New record not allowed to input New record not allowed to input|T24002649"
    - "New suspend amount will become negative New suspend amount will become negative|T24002650"
    - "NEW NEW|T24002651"
    - "NEXT CAP DATE CANNOT BE IN THE PAST NEXT CAP DATE CANNOT BE IN THE PAST|T24002652"
    - "NEXT.EFF.DATE must be greater than TODAY NEXT.EFF.DATE must be greater than
    TODAY|T24002655"
    - "NO - FOR FUNDS NO - FOR FUNDS|T24002656"
    - "No & Instance Exists for Class of Type & No & Instance Exists for Class of Type
    &|T24002657"
    - "NO & REC IN ER.PARAMETER NO & REC IN ER.PARAMETER|T24002658"
    - "NO : &|T24002659"
    - "NO ACCOUNT ACTIVITY ON RECORD NO ACCOUNT ACTIVITY ON RECORD|T24002660"
    - "NO ACCOUNT RECORD NO ACCOUNT RECORD|T24002661"
    - "Missing Mandatory Property Class Missing Mandatory Property Class|T24002446"
    - "MISSING MANDATORY PROPERTY FOR CLASS MISSING MANDATORY PROPERTY FOR CLASS|T24002447"
    - "Missing message type for breaking of Negotiation Rule. Missing message type
    for breaking of Negotiation Rule.|T24002448"
    - "Missing Social Security Details Missing Social Security Details|T24002469"
    - "Missing Standard Comparison Rule Missing Standard Comparison Rule|T24002470"
    - "MISSING STMT.ENTRY ID & MISSING STMT.ENTRY ID &|T24002471"
    - "MISSING SUBROUTINE NAME MISSING SUBROUTINE NAME|T24002472"
    - "Missing Tax Details Records Missing Tax Details Records|T24002473"
    - "MISSING TRANSACTION CHARGE RECORD MISSING TRANSACTION CHARGE RECORD|T24002474"
    - "MISSING TRANSACTION CODE MISSING TRANSACTION CODE|T24002475"
    - "MISSING TXN.AMOUNT in ARRANGEMENT ACTIVITY MISSING TXN.AMOUNT in ARRANGEMENT
    ACTIVITY|T24002476"
    - "MISSING UPDATE TYPE MISSING UPDATE TYPE|T24002477"
    - "MISSING VARIATION MISSING VARIATION|T24002478"
    - "MISSING XREF FILE MISSING XREF FILE|T24002479"
    - "MNEMONIC ALREADY EXISTS MNEMONIC ALREADY EXISTS|T24002481"
    - "MNEMONIC IS A NOSTRO MNEMONIC MNEMONIC IS A NOSTRO MNEMONIC|T24002482"
    - "MNEMONIC USED IN ANOTHER COMPANY MNEMONIC USED IN ANOTHER COMPANY|T24002483"
    - "Modification of customer(s) is not Allowed Modification of customer(s) is not
    Allowed|T24002484"
    - "MORE THAN 3 DECIMALS MORE THAN 3 DECIMALS|T24002485"
    - "NOT NUMERIC NOT NUMERIC|T24002845"
    - "NO CHANGE OF ADJUSTED AMOUNT NO CHANGE OF ADJUSTED AMOUNT|T24002672"
    - "No changes allowed for FIXED type classes No changes allowed for FIXED type
    classes|T24002673"
    - "NO CHARGE KEY FOR 51000 NO CHARGE KEY FOR 51000|T24002674"
    - "NO CHARGE KEY FOR 5100O NO CHARGE KEY FOR 5100O|T24002675"
    - "NO CONSTANT OR LINEAR TYPE ON CALL CONTRACT NO CONSTANT OR LINEAR TYPE ON CALL
    CONTRACT|T24002676"
    - "No Countries, Cap Not allowed No Countries, Cap Not allowed|T24002677"
    - "No credit balance for the given exposure date No credit balance for the given
    exposure date|T24002678"
    - "NO CREDIT TRANSACTION CODE NO CREDIT TRANSACTION CODE|T24002679"
    - "NO DAY.BASIS NO DAY.BASIS|T24002680"
    - "NO DEBIT TRANSACTION CODE NO DEBIT TRANSACTION CODE|T24002681"
    - "NO DEPOSITORY NO DEPOSITORY|T24002682"
    - "No Deposits Found INVALID SEARCH FIELD OR VALUE|T24003815"
    - "NO DUPLICATE ALLOWED FOR VARIATION NO DUPLICATE ALLOWED FOR VARIATION|T24002683"
    - "No early repayments are allowed. No early repayments are allowed.|T24002684"
    - "NO ENTRIES IN BATCH & NO ENTRIES IN BATCH &|T24002687"
    - "NO ENTRIES TO REVERSE NO ENTRIES TO REVERSE|T24002688"
    - "NO ENTRIES NO ENTRIES|T24002686"
    - "NO EX DATE NO EX DATE|T24002689"
    - "NO FT - SETTLEMENT ACCOUNT NOT ALLOWED NO FT - SETTLEMENT ACCOUNT NOT ALLOWED|T24002690"
    - "NO FUNCTION FOR THIS APPLICATION NO FUNCTION FOR THIS APPLICATION|T24002691"
    - "NO FUNCTIONS ALLOWED FOR THIS FILE NO FUNCTIONS ALLOWED FOR THIS FILE|T24002698"
    - "NO FUNCTIONS ALLOWED ON THIS FILE NO FUNCTIONS ALLOWED ON THIS FILE|T24002699"
    - "No further disbursement allowed. No further disbursement allowed.|T24002700"
    - "No Group capitalisation record for main acct No Group capitalisation record
    for main acct|T24002701"
    - "No Group capitalisation record for this acct No Group capitalisation record
    for this acct|T24002702"
    - "No Hist Restore & exists in DM.APPLN.GRP No Hist Restore & exists in DM.APPLN.GRP|T24002703"
    - "NO HISTORY EXTRACTION ALLOWED NO HISTORY EXTRACTION ALLOWED|T24002704"
    - "NO HOLIDAY RECORD FOR THIS CCY NO HOLIDAY RECORD FOR THIS CCY|T24002705"
    - "NO ID OR COMPONENTS DETAILS GIVEN NO ID OR COMPONENTS DETAILS GIVEN|T24002706"
    - "NO INPUT - UNAUTH RECORD EXISTS NO INPUT - UNAUTH RECORD EXISTS|T24002707"
    - "NO INPUT ALLOWED FOR THIS COMBINATION NO INPUT ALLOWED FOR THIS COMBINATION|T24002708"
    - "NO INPUT ALLOWED WHEN REBUILD.ALL = YES NO INPUT ALLOWED WHEN REBUILD.ALL
    = YES|T24002709"
    - "NO INPUT FIELD CANNOT BE NEGOTIATED NO INPUT FIELD CANNOT BE NEGOTIATED|T24002710"
    - "No input fields are not allowed No input fields are not allowed|T24002711"
    - "No input for an AA account No input for an AA account|T24002712"
    - "No input for final multi value No input for final multi value|T24002713"
    - "NO INPUT FOR OSC TYPE NO INPUT FOR OSC TYPE|T24002714"
    - "No input if no POSTING.DETAIL No input if no POSTING.DETAIL|T24002715"
    - "NO INPUT WHEN NO BALANCE.TYPE NO INPUT WHEN NO BALANCE.TYPE|T24002716"
    - "NO INPUT WITHOUT DEPENDANT FIELD NO INPUT WITHOUT DEPENDANT FIELD|T24002717"
    - "NO LEADING MINUS WITH SWIFT NO LEADING MINUS WITH SWIFT|T24002718"
    - "NO LEADING SLASH WITH SWIFT NO LEADING SLASH WITH SWIFT|T24002719"
    - "NO LIMIT TO LINK TO!!!! NO LIMIT TO LINK TO!!!!|T24002720"
    - "NO LIST NO LIST|T24002721"
    - "No MID RATE for BUY currency No MID RATE for BUY currency|T24002722"
    - "No MID RATE for SELL currency No MID RATE for SELL currency|T24002723"
    - "NO MORE RECORDS NO MORE RECORDS|T24002724"
    - "NO MULTIPLE INPUT OF & NO MULTIPLE INPUT OF &|T24002725"
    - "No need to change the SEQ no No need to change the SEQ no|T24002726"
    - "NO NETTING ACCOUNT.CLASS LOADED NO NETTING ACCOUNT.CLASS LOADED|T24002727"
    - "NO NETTING AGREEMENT FOR OPERATION OCDE NO NETTING AGREEMENT FOR OPERATION OCDE|T24002729"
    - "No Netting Agreement No Netting Agreement|T24002728"
    - "NO OF DAYS MUST BE NUMERIC NO OF DAYS MUST BE NUMERIC|T24002730"
    - "No RATE for SELL currency No RATE for SELL currency|T24002736"
    - "NO RECEIVER ADDRESS NO RECEIVER ADDRESS|T24002737"
    - "NO RECORD(S) NO RECORD(S)|T24002738"
    - "NO RECORDS TO DISPLAY NO RECORDS TO DISPLAY|T24002739"
    - "NO RECORDS TO PUBLISH NO RECORDS TO PUBLISH|T24002740"
    - "No records were found that matched the selection criteria No records were found
    that matched the selection criteria|T24003790"
    - "No reversal of PRINT.1 No reversal of PRINT.1|T24002741"
    - "NO ROUNDING RULE RECORD NO ROUNDING RULE RECORD|T24002743"
    - "No Rounding Rule No Rounding Rule|T24002742"
    - "NO ROUTINE NAME SUPPLIED NO ROUTINE NAME SUPPLIED|T24002744"
    - "No separate Routing Details for Cover message No separate Routing Details for
    Cover message|T24002745"
    - "No special characters other than . is Allowed No special characters other
    than . is Allowed|T24002746"
    - "NO SPECIFIED VALUE INVALID ChargesFor|T24003805"
    - "NO STANDARD SELECTION RECORD FOR & NO STANDARD SELECTION RECORD FOR &|T24002748"
    - "NO STANDARD SELECTION RECORD NO STANDARD SELECTION RECORD|T24002747"
    - "NO SUSPENSE CATEGORY LOADED NO SUSPENSE CATEGORY LOADED|T24002749"
    - "NO TRAILER DEFINED NO TRAILER DEFINED|T24002750"
    - "NO UNAUTH. ITEM FOR ADJUSTMENT NO UNAUTH. ITEM FOR ADJUSTMENT|T24002751"
    - "NO UNAUTHORISED RECORDS IN BATCH NO UNAUTHORISED RECORDS IN BATCH|T24002752"
    - "No. & Address may not both be input No. & Address may not both be input|T24002753"
    - "No. of payment should be null for matured contract No. of payment should be
    null for matured contract|T24002754"
    - "NOINPUT FOR FINAL MV NOINPUT FOR FINAL MV|T24002755"
    - "NOLOG activity cannot be defined NOLOG activity cannot be defined|T24002756"
    - "Non Comparison Field Non Comparison Field|T24002758"
    - "Non Contingent Account not allowed for this Batch Non Contingent Account not
    allowed for this Batch|T24002759"
    - "NON NEGOTIABLE FIELD NON NEGOTIABLE FIELD|T24002760"
    - "Non OSC type Collateral exists Non OSC type Collateral exists|T24002761"
    - "MUST BE DEFAULT MUST BE DEFAULT|T24002541"
    - "MUST BE DEFINED AFTER THE CURRENT FIELD MUST BE DEFINED AFTER THE CURRENT FIELD|T24002542"
    - "Must be defined when PI key used for contracts without term Must be defined
    when PI key used for contracts without term|T24002543"
    - "MUST BE EITHER BEN.BANK OR BEN.CUSTOMER MUST BE EITHER BEN.BANK OR BEN.CUSTOMER|T24002544"
    - "MUST BE EITHER MSG.NARR OR MSG.FIELD MUST BE EITHER MSG.NARR OR MSG.FIELD|T24002545"
    - "MUST BE ENTERED IF CARRIER IS GENERIC MUST BE ENTERED IF CARRIER IS GENERIC|T24002548"
    - "Must be entered in value dated system Must be entered in value dated system|T24002549"
    - "MUST BE ENTERED MUST BE ENTERED|T24002546"
    - "MUST BE FIXaaa*currency location MUST BE FIXaaa*currency location|T24002550"
    - "MUST BE GE CR.MIN.BAL.ST.DTE MUST BE GE CR.MIN.BAL.ST.DTE|T24002551"
    - "MUST BE GE CR2.MIN.BAL.ST.DTE MUST BE GE CR2.MIN.BAL.ST.DTE|T24002552"
    - "Must be greater than & Must be greater than &|T24002553"
    - "MUST BE GREATER THAN CORRESPONDING MINIMUM.AMT MUST BE GREATER THAN CORRESPONDING
    MINIMUM.AMT|T24002554"
    - "MUST BE GREATER THAN TODAY MUST BE GREATER THAN TODAY|T24002555"
    - "Must be GT Term date and Cycle freq Must be GT Term date and Cycle freq|T24002556"
    - "MUST ENTER ONE FIELD MUST ENTER ONE FIELD|T24002594"
    - "MUST ENTER PENDING CATEGORY CODE MUST ENTER PENDING CATEGORY CODE|T24002595"
    - "MUST ENTER TELEX HARDWARE TYPE MUST ENTER TELEX HARDWARE TYPE|T24002596"
    - "MUST ENTER THE COLLECTION SUSPENSE CATEGORY MUST ENTER THE COLLECTION SUSPENSE
    CATEGORY|T24002597"
    - "No RATE for BUY currency No RATE for BUY currency|T24002735"
    - "NOLOG activity cannot be defined in rule based evaluation NOLOG activity cannot
    be defined in rule based evaluation|T24002757"
    - "NON-CONTINGENT TO CONT NOT ALLOWED NON-CONTINGENT TO CONT NOT ALLOWED|T24002762"
    - "NOSTRO ACCOUNT NOT PERMISSIBLE NOSTRO ACCOUNT NOT PERMISSIBLE|T24002763"
    - "NOSTRO.NUM.MNE MNEMONIC RECORD - MISSING NOSTRO.NUM.MNE MNEMONIC RECORD
    - MISSING|T24002764"
    - "NOSTRO.NUM.MNE MNEMONIC RECORD CORRUPT NOSTRO.NUM.MNE MNEMONIC RECORD
    CORRUPT|T24002765"
    - "NOSTRO.NUM.MNE NUMBER RECORD - MISSING NOSTRO.NUM.MNE NUMBER RECORD
    - MISSING|T24002766"
    - "NOT A CHEQUE TYPE OF INSTRUCTED STOPPED CHEQUE NOT A CHEQUE TYPE OF INSTRUCTED
    STOPPED CHEQUE|T24002767"
    - "Not a common class type Not a common class type|T24002768"
    - "Not a corresponding BILL.TYPE & Not a corresponding BILL.TYPE &|T24002769"
    - "NOT A CREDIT TXN CODE NOT A CREDIT TXN CODE|T24002770"
    - "Not a Customer Company Not a Customer Company|T24002771"
    - "NOT A DEBIT TXN CODE NOT A DEBIT TXN CODE|T24002772"
    - "NOT A FOREIGN CURRENCY TXN NOT A FOREIGN CURRENCY TXN|T24002773"
    - "NOT A LIVE COMPANY NOT A LIVE COMPANY|T24002774"
    - "Not a valid T24 File Name Not a valid T24 File Name|T24002790"
    - "NOT A VALID TRANSACTION CODE NOT A VALID TRANSACTION CODE|T24002791"
    - "Not allowed - Product already defined Not allowed - Product already defined|T24002792"
    - "Not allowed for ACCOUNTS product Not allowed for ACCOUNTS product|T24002793"
    - "Not Allowed for Action - BATCH Not Allowed for Action - BATCH|T24002794"
    - "Not Allowed for Action - PRODUCT/CHANGE.PRODUCT Not Allowed for Action - PRODUCT/CHANGE.PRODUCT|T24002795"
    - "Not Allowed for change product Not Allowed for change product|T24002796"
    - "Not Allowed for INWARD DD.DDI Not Allowed for INWARD DD.DDI|T24002797"
    - "NOT ALLOWED FOR MULTI VALUE FIELD NOT ALLOWED FOR MULTI VALUE FIELD|T24002798"
    - "NOT ALLOWED FOR NOCHANGE OPTION NOT ALLOWED FOR NOCHANGE OPTION|T24002799"
    - "Not allowed for Product Type Collection Not allowed for Product Type Collection|T24002800"
    - "Not allowed for Rate Type Not allowed for Rate Type|T24002801"
    - "Not Allowed for Safe Deposit Box Product Line Not Allowed for Safe Deposit Box
    Product Line|T24002802"
    - "NOT ALLOWED FOR SHARED BALANCES NOT ALLOWED FOR SHARED BALANCES|T24002803"
    - "NOT ALLOWED FOR THE COMPOUND TYPE NOT ALLOWED FOR THE COMPOUND TYPE|T24002804"
    - "NOT ALLOWED FOR THIS DATE CONVENTION NOT ALLOWED FOR THIS DATE CONVENTION|T24002805"
    - "NOT ALLOWED IF BALANCE TYPE NOT MINIMUM NOT ALLOWED IF BALANCE TYPE NOT MINIMUM|T24002806"
    - "Not Allowed if CHANGE.DATE.TYPE is MATURITY Not Allowed if CHANGE.DATE.TYPE
    is MATURITY|T24002807"
    - "Not Allowed if CHANGE.DATE.TYPE is PAYMENT.END.DATE Not Allowed if CHANGE.DATE.TYPE
    is PAYMENT.END.DATE|T24002808"
    - "NOT ALLOWED IF MAX.LEGAL.RATE DEFINED NOT ALLOWED IF MAX.LEGAL.RATE DEFINED|T24002809"
    - "Not allowed more than two liability party Not allowed more than two liability
    party|T24002810"
    - "Not allowed to change Line Attribute Not allowed to change Line Attribute|T24002811"
    - "Not allowed to change property class order Not allowed to change property class
    order|T24002812"
    - "Not allowed to input if the Evaluation result is APPLY Not allowed to input
    if the Evaluation result is APPLY|T24002814"
    - "Not allowed to input Not allowed to input|T24002813"
    - "NOT ALLOWED TO MODIFY COUNTRY NOT ALLOWED TO MODIFY COUNTRY|T24002816"
    - "Not Allowed to modify from Active to Provisional Not Allowed to modify
    from Active to Provisional|T24002817"
    - "Not Allowed to modify from Disputed to Provisional Not Allowed to modify
    from Disputed to Provisional|T24002818"
    - "NOT ALLOWED TO MODIFY NOT ALLOWED TO MODIFY|T24002815"
    - "Not allowed when inc all def chgs set Not allowed when inc all def chgs set|T24002819"
    - "Not allowed when Interest Method is Fixed Not allowed when Interest Method is
    Fixed|T24002820"
    - "Not allowed with BILL.PRODUCED greater than zero Not allowed with BILL.PRODUCED
    greater than zero|T24002821"
    - "Not allowed with Buyer-Seller Info limit Not allowed with Buyer-Seller Info
    limit|T24002822"
    - "Not Allowed! Activity belongs to the service group &. Not Allowed! Activity
    belongs to the service group &.|T24002823"
    - "Not allowed, Bill Date Less Than Suspend Date Not allowed, Bill Date Less Than
    Suspend Date|T24002824"
    - "Not applicable - Invoice already cleared / returned Not applicable - Invoice
    already cleared / returned|T24002825"
    - "NOT APPLICABLE FOR APPLICATION.TYPE CURRENT NOT APPLICABLE FOR APPLICATION.TYPE
    CURRENT|T24002826"
    - "Not applicable for same currency Not applicable for same currency|T24002827"
    - "Not Applicable for this PERIOD.TYPE Not Applicable for this PERIOD.TYPE|T24002828"
    - "NOT AVAILABLE NOT AVAILABLE|T24002829"
    - "NOT DEFINED AS A SUB GROUP NOT DEFINED AS A SUB GROUP|T24002830"
    - "Not defined as main account Not defined as main account|T24002831"
    - "NOT DEFINED FOR & MESSAGE NOT DEFINED FOR & MESSAGE|T24002833"
    - "NOT DEFINED FOR & NOT DEFINED FOR &|T24002832"
    - "NOT DEFINED IN DE.MESSAGE & NOT DEFINED IN DE.MESSAGE &|T24002834"
    - "NOT DEFINED IN LOCAL.REF.TABLE NOT DEFINED IN LOCAL.REF.TABLE|T24002835"
    - "Not defined in SIM.TYPE.NAME Not defined in SIM.TYPE.NAME|T24002836"
    - "NOT DEFINED IN VETTING TABLE INVALID WrittenRequest|T24003804"
    - "NOT DEFINED ON COMPANY RECORD NOT DEFINED ON COMPANY RECORD|T24002837"
    - "NOT DEFINED UNDER THIS CLASS NOT DEFINED UNDER THIS CLASS|T24002838"
    - "NOT EVERY ACCRUAL FIELD EMPTY NOT EVERY ACCRUAL FIELD EMPTY|T24002839"
    - "NOT EVERY BALANCE FIELD EMPTY NOT EVERY BALANCE FIELD EMPTY|T24002840"
    - "NOT IN LINE WITH MULTIPLES DEFINED NOT IN LINE WITH MULTIPLES DEFINED|T24002841"
    - "NOT INPUTTABLE FOR INWARD DD NOT INPUTTABLE FOR INWARD DD|T24002842"
    - "Not inputtable for Outward DD Not inputtable for Outward DD|T24002843"
    - "Not more than 4 lines permitted Not more than 4 lines permitted|T24002844"
    - "NOT ON INPUT TABLE NOT ON INPUT TABLE|T24002846"
    - "Number of authoriser should be greater than zero Number of authoriser should
    be greater than zero|T24002869"
    - "Number of days does not fit in date range Number of days does not fit in date
    range|T24002870"
    - "NUMBER OF INDEX FIELDS NE TO NUMBER OF PARTS IN ID NUMBER OF INDEX FIELDS NE
    TO NUMBER OF PARTS IN ID|T24002871"
    - "NUMBER.OF.CREDIT ALREADY DEFINED NUMBER.OF.CREDIT ALREADY DEFINED|T24002872"
    - "NUMBER.OF.DEBIT ALREADY DEFINED NUMBER.OF.DEBIT ALREADY DEFINED|T24002873"
    - "NUMERIC EXPECTED NUMERIC EXPECTED|T24002874"
    - "NUMERIC PART IS MANDATORY NUMERIC PART IS MANDATORY|T24002875"
    - "Offset activities are mandatory Offset activities are mandatory|T24002876"
    - "NETTING ALLOWED FOR 202 & 203 MESSAGES ONLY NETTING ALLOWED FOR 202 & 203 MESSAGES
    ONLY|T24002633"
    - "NETTING NOT ALLOWED FOR REDUCING LIMIT NETTING NOT ALLOWED FOR REDUCING LIMIT|T24002635"
    - "NETTING NOT ALLOWED NETTING NOT ALLOWED|T24002634"
    - "NETTING STATUS NOT SET IN DE.PARM NETTING STATUS NOT SET IN DE.PARM|T24002636"
    - "New Activity Not Allowed to Input New Activity Not Allowed to Input|T24002637"
    - "New API is introduced New API is introduced|T24002638"
    - "New balance amount should not be zero New balance amount should not be zero|T24002639"
    - "New bill type and existing bill type are same. New bill type and existing bill
    type are same.|T24002640"
    - "New Bill Type not allowed New Bill Type not allowed|T24002641"
    - "New Class Type Not Allowed New Class Type Not Allowed|T24002642"
    - "NO ADDRESS RECORD FOR & NO ADDRESS RECORD FOR &|T24002662"
    - "NO ADJUSTMENT ALLOWED FOR THE BATCH NO ADJUSTMENT ALLOWED FOR THE BATCH|T24002663"
    - "NO AMORTISATION ON CALL CONTRACT NO AMORTISATION ON CALL CONTRACT|T24002664"
    - "NO AMOUNT FOR LAST TIER NO AMOUNT FOR LAST TIER|T24002665"
    - "No arr activity defined for A/C & Arr. & No arr activity defined for A/C & Arr.
    &|T24002666"
    - "NO AUTH., INVALID GROUP NO AUTH., INVALID GROUP|T24002667"
    - "NO BILATERAL AGREEMENT FOR THE CUSTOMER NO BILATERAL AGREEMENT FOR THE CUSTOMER|T24002668"
    - "NO BILATERAL AGREEMENT FOR THE MSG TYPE NO BILATERAL AGREEMENT FOR THE MSG TYPE|T24002669"
    - "No block found for given account or cusotmer No block found for given account
    or cusotmer|T24002670"
    - "NO CHANGE ALLOWED - STOCK EXISTS NO CHANGE ALLOWED - STOCK EXISTS|T24002671"
    - "Periodic Charge property missing Periodic Charge property missing|T24003055"
    - "Periodic Commission type not allowed for Scheduled Charge Periodic Commission
    type not allowed for Scheduled Charge|T24003056"
    - "Periodic Review not set Periodic Review not set|T24003057"
    - "Permission type is different from user type Permission type is different from
    user type|T24003058"
    - "PGM FILE IS NOT OF TYPE S PGM FILE IS NOT OF TYPE S|T24003059"
    - "OFS.SOURCE SHOULD BE GLOBUS OFS.SOURCE SHOULD BE GLOBUS|T24002879"
    - "OFS.VERSION SHOULD BE REAL VERSION OFS.VERSION SHOULD BE REAL VERSION|T24002880"
    - "ONE A MUST BE NEXT TO THE Ns ONE A MUST BE NEXT TO THE Ns|T24002881"
    - "Online capitalise not allowed for Periodic charge property Online capitalise
    not allowed for Periodic charge property|T24002882"
    - "Online closure method allowed only for ACCOUNTS product line Online closure
    method allowed only for ACCOUNTS product line|T24002883"
    - "Only 1 amount can be enteres Only 1 amount can be enteres|T24002884"
    - "ONLY 1 COPY FOR THIS TYPE ONLY 1 COPY FOR THIS TYPE|T24002885"
    - "ONLY 1 MESSAGE CAN BE RAISED ONLY 1 MESSAGE CAN BE RAISED|T24002886"
    - "Only 20 Lines Permitted Only 20 Lines Permitted|T24002887"
    - "ONLY 3 LINES ALLOWED ONLY 3 LINES ALLOWED|T24002888"
    - "Only 35 Lines Permitted Only 35 Lines Permitted|T24002889"
    - "Only 6 Lines Permitted Only 6 Lines Permitted|T24002890"
    - "ONLY AC APPLICATION ALLOWED ONLY AC APPLICATION ALLOWED|T24002891"
    - "Only Account and Interest properties can be captured Only Account and Interest
    properties can be captured|T24002892"
    - "Only ACCOUNT or INTEREST property allowed Only ACCOUNT or INTEREST property
    allowed|T24002893"
    - "ONLY ALLOW COMBINATION OF A,N,X,. ONLY ALLOW COMBINATION OF
    A,N,X,.|T24002894"
    - "ONLY ALLOW WITH SWIFT ADDRESS ONLY ALLOW WITH SWIFT ADDRESS|T24002895"
    - "ONLY ALLOW WITH TELEXT ADDRESS ONLY ALLOW WITH TELEXT ADDRESS|T24002896"
    - "Only allowed for Accounts, Deposits and Lending product line Only allowed for
    Accounts, Deposits and Lending product line|T24002897"
    - "Only allowed for PAYMENT Type Activities Only allowed for PAYMENT Type Activities|T24002898"
    - "ONLY ALLOWED IN SUB GROUPS ONLY ALLOWED IN SUB GROUPS|T24002899"
    - "Only Allowed is of type & Only Allowed is of type &|T24002900"
    - "ONLY ALLOWED WITH PAYMENT FREQUENCY ONLY ALLOWED WITH PAYMENT FREQUENCY|T24002901"
    - "Only applies to a main account Only applies to a main account|T24002902"
    - "ONLY AVAILABLE FOR BALANCE TYPE OF MINIMUM ONLY AVAILABLE FOR BALANCE TYPE OF
    MINIMUM|T24002903"
    - "ONLY AVAILABLE IF BALANCE TYPE IS SET TO MINIMUM ONLY AVAILABLE IF BALANCE TYPE
    IS SET TO MINIMUM|T24002904"
    - "Only CHANGE.PRODUCT activities are allowed Only CHANGE.PRODUCT activities are
    allowed|T24002905"
    - "Only Charge and Tax Property/Property Class allowed Only Charge and Tax Property/Property
    Class allowed|T24002906"
    - "ONLY CLEARED IS ALLOWED WHEN ID IS AUTO.CLEAR ONLY CLEARED IS ALLOWED WHEN ID
    IS AUTO.CLEAR|T24002907"
    - "Only CLOSE Allowed for Relationship Pricing Products Only CLOSE Allowed for
    Relationship Pricing Products|T24002908"
    - "Only Current balances - CUR, RES and ACC allowed Only Current balances - CUR,
    RES and ACC allowed|T24002909"
    - "ONLY CUSTOMER ACCOUNT ALLOWED ONLY CUSTOMER ACCOUNT ALLOWED|T24002910"
    - "Only DUE Only DUE|T24002911"
    - "Only EB-0192,EB-0195,EB-0196 valid Only EB-0192,EB-0195,EB-0196 valid|T24002912"
    - "Only EB-0292,EB-0295,EB-0296 valid Only EB-0292,EB-0295,EB-0296 valid|T24002913"
    - "Only EB-0392,EB-0395,EB-0396 valid Only EB-0392,EB-0395,EB-0396 valid|T24002914"
    - "Only EB-0492,EB-0495,EB-0496 valid Only EB-0492,EB-0495,EB-0496 valid|T24002915"
    - "Only EB-0592,EB-0595,EB-0596 valid Only EB-0592,EB-0595,EB-0596 valid|T24002916"
    - "Only EB-0692,EB-0695,EB-0696 valid Only EB-0692,EB-0695,EB-0696 valid|T24002917"
    - "Only EB-0792,EB-0795,EB-0796 valid Only EB-0792,EB-0795,EB-0796 valid|T24002918"
    - "Only EB-0892,EB-0895,EB-0896 valid Only EB-0892,EB-0895,EB-0896 valid|T24002919"
    - "Only EB-0992,EB-0995,EB-0996 valid Only EB-0992,EB-0995,EB-0996 valid|T24002920"
    - "ONLY ENTER ACCT NO FOR NEXT ID ONLY ENTER ACCT NO FOR NEXT ID|T24002921"
    - "Only Equal Operator is Supported Only Equal Operator is Supported|T24002922"
    - "ONLY FIVE SORT FIELDS ALLOWED ONLY FIVE SORT FIELDS ALLOWED|T24002923"
    - "Only for lending products property can be SUSPEND Only for lending products
    property can be SUSPEND|T24002924"
    - "Only H or U type programs allowed Only H or U type programs
    allowed|T24002925"
    - "Only internal accounts and PL category allowed Only internal accounts and PL
    category allowed|T24002926"
    - "Only LEVEL type allowed when TierSource is set Only LEVEL type allowed when
    TierSource is set|T24002927"
    - "Only LEVEL type allowed when TierTerm is set Only LEVEL type allowed when TierTerm
    is set|T24002928"
    - "ONLY M-ONTHS ALLOWED ONLY M-ONTHS ALLOWED|T24002929"
    - "Only MSG.TYPE n92,n95 or n96 allowed Only MSG.TYPE n92,n95 or n96 allowed|T24002930"
    - "Only one Bill Batch Accepted Only one Bill Batch Accepted|T24002934"
    - "Only one Bill Register Accpeted Only one Bill Register Accpeted|T24002935"
    - "Only one customer allowed for ACCT.WITH.BANK Only one customer allowed for ACCT.WITH.BANK|T24002936"
    - "Only one customer allowed for ORDERING.INST Only one customer allowed for ORDERING.INST|T24002937"
    - "Only one CUSTOMER field allowed Only one CUSTOMER field allowed|T24002938"
    - "Only one customer parameter allowed Only one customer parameter allowed|T24002939"
    - "ONLY ONE GROUP OF N IS ALLOWED ONLY ONE GROUP OF N IS ALLOWED|T24002940"
    - "Only one input allowed.Either Tier Amount or Tier Count or Tier Term Only one
    input allowed.Either Tier Amount or Tier Count or Tier Term|T24002941"
    - "Only one LinkAcNumber value is allowed. Only one LinkAcNumber value is allowed.|T24002942"
    - "Only one multi-value allowed if DB.RULE is FULL. Only one multi-value allowed
    if DB.RULE is FULL.|T24002943"
    - "Only one of the upto fields to be input throught the set Only one of the upto
    fields to be input throught the set|T24002944"
    - "Only PAYMENT or EXPECTED types allowed Only PAYMENT or EXPECTED types allowed|T24002945"
    - "Only PRINT address allowed Only PRINT address allowed|T24002946"
    - "ONLY REDUCTION ALLOWED FOR REVOLVING ONLY REDUCTION ALLOWED FOR REVOLVING|T24002947"
    - "Only Renewal Activities will be allowed. Only Renewal Activities will be allowed.|T24002948"
    - "Only Rollover Activity will be allowed Only Rollover Activity will be allowed|T24002949"
    - "ONLY SCHEDULED ACTIVITY ALLOWED ONLY SCHEDULED ACTIVITY ALLOWED|T24002950"
    - "Only single valued fields allowed Only single valued fields allowed|T24002951"
    - "Only six EXTRA DETAILS are allowed Only six EXTRA DETAILS are allowed|T24002952"
    - "Only six SENDER INFOs are allowed Only six SENDER INFOs are allowed|T24002953"
    - "Only START.DATE is allowed Only START.DATE is allowed|T24002954"
    - "ONLY STP OR NULL ALLOWED FOR 941 ONLY STP OR NULL ALLOWED FOR 941|T24002955"
    - "Only TR pos.type account allowed for & Only TR pos.type account allowed for
    &|T24002956"
    - "ONLY TWO ALLOWED - PLEASE DELETE ONLY TWO ALLOWED - PLEASE DELETE|T24002957"
    - "NO OF DAYS MUST BE SPECIFIED NO OF DAYS MUST BE SPECIFIED|T24002731"
    - "NO OF DAYS MUST NOT BE SIGNED NO OF DAYS MUST NOT BE SIGNED|T24002732"
    - "NO P&L CATEGORY NO P&L CATEGORY|T24002733"
    - "No payment type defined for the settlement activity No payment type defined
    for the settlement activity|T24002734"
    - "Not a Mandate of this arrangement Not a Mandate of this arrangement|T24002775"
    - "NOT A MANUAL MATCH NOT A MANUAL MATCH|T24002776"
    - "NOT A NOSTRO COMPANY NOT A NOSTRO COMPANY|T24002777"
    - "NOT A PASSBOOK ACCOUNT NOT A PASSBOOK ACCOUNT|T24002778"
    - "NOT A PL CATEGORY NOT A PL CATEGORY|T24002779"
    - "Not a valid arrangement &, since it is in & status Not a valid arrangement &,
    since it is in & status|T24002780"
    - "Not a Valid Charge Property Class Not a Valid Charge Property Class|T24002781"
    - "Not a Valid Context Type Not a Valid Context Type|T24002782"
    - "Not a Valid Currency for this Product Not a Valid Currency for this Product|T24002783"
    - "Not a valid customer for Liability Not a valid customer for Liability|T24002784"
    - "Not a valid field for the Property Not a valid field for the Property|T24002785"
    - "Not a Valid Interest Property Class Not a Valid Interest Property Class|T24002786"
    - "Not a valid offset payin activity Not a valid offset payin activity|T24002787"
    - "Not a valid offset payout activity Not a valid offset payout activity|T24002788"
    - "Not a valid SWIFT BIC format Not a valid SWIFT BIC format|T24002789"
    - "Opportunity Definition is Mandatory Opportunity Definition is Mandatory|T24002971"
    - "Opportunity Definition record not Found. Opportunity Definition record not Found.|T24002972"
    - "OPTION & NOT AVAILABLE FOR MULTI-COMPANY OPTION & NOT AVAILABLE FOR MULTI-COMPANY|T24002973"
    - "OPTION INVALID FOR PR.ATTRIBUTE OPTION INVALID FOR PR.ATTRIBUTE|T24002974"
    - "Option Should not be null Option Should not be null|T24002975"
    - "Options can be MATURITY,RENEWAL,EXPECTED.TERM or Period Options can be MATURITY,RENEWAL,EXPECTED.TERM
    or Period|T24002976"
    - "Original amount cant be less than or equal to zero Original amount cant be less
    than or equal to zero|T24002977"
    - "Original and new exposure dates cannot be same Original and new exposure dates
    cannot be same|T24002978"
    - "Original date cannot be greater than effective date Original date cannot be
    greater than effective date|T24002979"
    - "Original date mandatory for takeover activity Original date mandatory for takeover
    activity|T24002980"
    - "Original message mandatory with FRAD narration Original message mandatory with
    FRAD narration|T24002981"
    - "ORIGINAL STATUS MUST BE HOLD ORIGINAL STATUS MUST BE HOLD|T24002982"
    - "OS.PERCENT given only OSC-type allowed OS.PERCENT given only OSC-type allowed|T24002983"
    - "Other Customer Account Not Valid Other Customer Account Not Valid|T24002984"
    - "OUT OF RANGE OF FILE RECORD NUMBER OUT OF RANGE OF FILE RECORD NUMBER|T24002985"
    - "Outbound Opportunities threshold has already reached Outbound Opportunities
    threshold has already reached|T24002986"
    - "Outbound Opportunity cant be created manually Outbound Opportunity cant be created
    manually|T24002987"
    - "OUTSTANDING SUSPENSE AMOUNTS OUTSTANDING SUSPENSE AMOUNTS|T24002988"
    - "Overdraft limit exceeded. Please contact bank Overdraft limit exceeded. Please
    contact bank|T24002989"
    - "Overdue status cannot be blank when period is defined Overdue status cannot
    be blank when period is defined|T24002991"
    - "Overdue To Should be greater than Overdue From Overdue To Should be greater
    than Overdue From|T24002992"
    - "OVERLAPS WITH ITSELF OVERLAPS WITH ITSELF|T24002993"
    - "Previous Cheque is in INAU status|T24003798"
    - "PART OF COLLATERAL & CANNOT CLOSE PART OF COLLATERAL & CANNOT CLOSE|T24002997"
    - "PART OF SEC.ACC.MASTER & - CANNOT CLOSE. PART OF SEC.ACC.MASTER & - CANNOT CLOSE.|T24002999"
    - "PART OF SEC.ACC.MASTER & -CANNOT CLOSE. PART OF SEC.ACC.MASTER & -CANNOT CLOSE.|T24003000"
    - "Partial Disbursement not allowed Partial Disbursement not allowed|T24003001"
    - "Partial payoff is not allowed Partial payoff is not allowed|T24003002"
    - "Partial payout not allowed Partial payout not allowed|T24003003"
    - "Partial Settlement not allowed for this product Partial Settlement not allowed
    for this product|T24003005"
    - "Partial Settlement not allowed Partial Settlement not allowed|T24003004"
    - "Participant & currency & is not the same as Master &(&) Participant & currency
    & is not the same as Master &(&)|T24003006"
    - "Passbook not allowed for HVT accounts Passbook not allowed for HVT accounts|T24003007"
    - "PASSBOOKS ONLY VALID FOR SAVINGS ACCT PASSBOOKS ONLY VALID FOR SAVINGS ACCT|T24003008"
    - "PAY method valid only for CREDIT type property PAY method valid only for CREDIT
    type property|T24003009"
    - "PAY type not allowed PAY type not allowed|T24003010"
    - "PAY PAY|T24003011"
    - "PAYIN account is mandatory when RC setup is enabled PAYIN account is mandatory
    when RC setup is enabled|T24003012"
    - "Payin Activity is mandatory for PAYIN accounts. Payin Activity is mandatory
    for PAYIN accounts.|T24003013"
    - "Payment Amount Input Missing Payment Amount Input Missing|T24003014"
    - "Payment amount not equal to sum of new property amount Payment amount not equal
    to sum of new property amount|T24003015"
    - "Payment amount should not be null Payment amount should not be null|T24003016"
    - "Payment date should not be null Payment date should not be null|T24003017"
    - "PAYMENT DATE(&) GT TERM END DATE(&) PAYMENT DATE(&) GT TERM END DATE(&)|T24003018"
    - "PAYMENT FREQUENCY MANDATORY WHEN TERM IS INPUT PAYMENT FREQUENCY MANDATORY WHEN
    TERM IS INPUT|T24003020"
    - "PAYMENT FREQUENCY MANDATORY PAYMENT FREQUENCY MANDATORY|T24003019"
    - "Payment frequency not allowed for matured contract Payment frequency not allowed
    for matured contract|T24003021"
    - "Payment Frequency should be Weekly or Bi-weekly Payment Frequency should be
    Weekly or Bi-weekly|T24003022"
    - "Payment Method Capitalise Not Allowed For Neg Interests Payment Method Capitalise
    Not Allowed For Neg Interests|T24003023"
    - "Payment method DUE not allowed for payment bill types Payment method DUE not
    allowed for payment bill types|T24003024"
    - "PAYMENT RULE FOR & PROPERTY MISSING PAYMENT RULE FOR & PROPERTY MISSING|T24003025"
    - "Payment type is mandatory for given mandate Payment type is mandatory for given
    mandate|T24003026"
    - "Payment type mandatory for bill Payment type mandatory for bill|T24003027"
    - "Payment type not allowed for fixed type of interest Payment type not allowed
    for fixed type of interest|T24003028"
    - "Payment type not defined in payment schedule Payment type not defined in payment
    schedule|T24003029"
    - "Payment Type Not In Payment Schedule Payment Type Not In Payment Schedule|T24003030"
    - "Payment type should not be null Payment type should not be null|T24003031"
    - "Payment type valid only for Advance type of Interest Payment type valid only
    for Advance type of Interest|T24003032"
    - "PAYMENT.STOP CAN BE REVOKED BY STOP.END.FLAG Y PAYMENT.STOP CAN BE REVOKED
    BY STOP.END.FLAG Y|T24003033"
    - "PaymentSchedule Component Latest Update Available PaymentSchedule Component
    Latest Update Available|T24003034"
    - "Payoff amt greater than repay amt Payoff amt greater than repay amt|T24003035"
    - "PERCENTAGE ALLOWED ONLY FOR ACCOUNT PROPERTY CLASS PERCENTAGE ALLOWED ONLY FOR
    ACCOUNT PROPERTY CLASS|T24003042"
    - "PERCENTAGE INVALID FOR THIS PAYMENT TYPE PERCENTAGE INVALID FOR THIS PAYMENT
    TYPE|T24003043"
    - "Percentage not allowed for MAINTAIN Method Percentage not allowed for MAINTAIN
    Method|T24003044"
    - "PERCENTAGE SHOULD BE > 0 AND <= 100 PERCENTAGE SHOULD BE > 0 AND <= 100|T24003045"
    - "PERCT.FOR.OFFSET MISSING PERCT.FOR.OFFSET MISSING|T24003046"
    - "Period can be specified only if Status is specified Period can be specified
    only if Status is specified|T24003047"
    - "Period format must be NNNNN followed by DWMY Period format must be NNNNN followed
    by DWMY|T24003048"
    - "Period is mandatory when overdue status is defined Period is mandatory when
    overdue status is defined|T24003049"
    - "Period should be in Ascending order Period should be in Ascending order|T24003050"
    - "Period Should Not be more than one year (E-135499) Period Should Not be more
    than one year (E-135499)|T24003052"
    - "Period Should Not be more than one year Period Should Not be more than one year|T24003051"
    - "Period type not allowed Period type not allowed|T24003053"
    - "PERIOD, NUMERIC and AMOUNT are the only allowed types PERIOD, NUMERIC and AMOUNT
    are the only allowed types|T24003054"
    - "PGM TYPE SHOULD BE S PGM TYPE SHOULD BE S|T24003060"
    - "PGM.FILE MUST BE SET UP FOR THE ROUTINE PGM.FILE MUST BE SET UP FOR THE ROUTINE|T24003061"
    - "NOT ON PERIODIC INTEREST TABLE NOT ON PERIODIC INTEREST TABLE|T24002847"
    - "NOT POSSIBLE INPUT NOT POSSIBLE INPUT|T24002848"
    - "NOT PREFIXED WITH SW- NOT PREFIXED WITH SW-|T24002849"
    - "NOT SET IN PARAMETER NOT SET IN PARAMETER|T24002850"
    - "NOT UNDER PRODUCT.GROUP NOT UNDER PRODUCT.GROUP|T24002851"
    - "Not valid charge property Not valid charge property|T24002852"
    - "Not Valid if there is no CHANGE.PRODUCT Property Not Valid if there is no CHANGE.PRODUCT
    Property|T24002853"
    - "Not valid interest property Not valid interest property|T24002854"
    - "NOT VALID UNLESS EXCHANGE RATE INPUT NOT VALID UNLESS EXCHANGE RATE INPUT|T24002855"
    - "NOT VALID WITH ACCOUNT INPUT NOT VALID WITH ACCOUNT INPUT|T24002856"
    - "NOT VALID WITH PL.CATEGORY INPUT NOT VALID WITH PL.CATEGORY INPUT|T24002857"
    - "Nothing Mapped for Any Parameter of & Nothing Mapped for Any Parameter of &|T24002858"
    - "NOTHING TO RERUN NOTHING TO RERUN|T24002859"
    - "Notice Days can be specified only if Status is specified Notice Days can be
    specified only if Status is specified|T24002860"
    - "Notice Frequency can be specified only if Status is specified Notice Frequency
    can be specified only if Status is specified|T24002861"
    - "NP SUB-PRODUCT NOT INSTALLED CORRECTLY NP SUB-PRODUCT NOT INSTALLED CORRECTLY|T24002862"
    - "NR.OPTIONS SHOULD BE SAME NR.OPTIONS SHOULD BE SAME|T24002863"
    - "NULL FIELD NOT ALLOWED NULL FIELD NOT ALLOWED|T24002864"
    - "NULL LINES NOT ALLOWED NULL LINES NOT ALLOWED|T24002865"
    - "NULL NOT EXPECTED NULL NOT EXPECTED|T24002866"
    - "Number issued is less than Minimum Holdings Number issued is less than Minimum
    Holdings|T24002867"
    - "NUMBER NOT NUMERIC NUMBER NOT NUMERIC|T24002868"
    - "OFFSET.BAL.TYPE MISSING OFFSET.BAL.TYPE MISSING|T24002877"
    - "OFFSET.CURRENCY MISSING OFFSET.CURRENCY MISSING|*********"
    - "Pleas Input Valid Period Base date Pleas Input Valid Period Base date|*********"
    - "PLEASE ANSWER Y OR NO PLEASE ANSWER Y OR NO|*********"
    - "Please define a Credit type charge property for BONUS Please define a Credit
    type charge property for BONUS|*********"
    - "Please define a Debit type charge property for CHARGE Please define a Debit
    type charge property for CHARGE|*********"
    - "Please delink this arrangement from bundle facility & Please delink this arrangement
    from bundle facility &|*********"
    - "Please enter a valid SWIFT TAG Please enter a valid SWIFT TAG|*********"
    - "Please enter Joint Holder/Relation Please enter Joint Holder/Relation|*********"
    - "Please enter valid version number Please enter valid version number|*********"
    - "Please include atleast one Date selection Please include atleast one Date selection|*********"
    - "Please include limit condition in the product Please include limit condition
    in the product|*********"
    - "Please input Evidence field name Please input Evidence field name|*********"
    - "Please input Evidence Requirement Please input Evidence Requirement|*********"
    - "Please input Period Please input Period|*********"
    - "Please modify the CURRENCY selection to include two currencies separated by
    a space Please modify the CURRENCY selection to include two currencies separated
    by a space|*********"
    - "Please remove the Unblocking Code as there is no expiry Please remove the Unblocking
    Code as there is no expiry|T24003082"
    - "Please remove the Unblocking Reason as there is no expiry Please remove the
    Unblocking Reason as there is no expiry|T24003083"
    - "Please use Browser for this action Please use Browser for this action|T24003084"
    - "PO PRODUCTS NOT MAINTAINED IN ACCOUNT CLOSURE PARAM APPLICATION PO PRODUCTS
    NOT MAINTAINED IN ACCOUNT CLOSURE PARAM APPLICATION|T24003088"
    - "POSITION OR LENGTH MISSING POSITION OR LENGTH MISSING|T24003094"
    - "POSITION TYPE MUST BE SAME AS FIRST ITEM IN BATCH POSITION TYPE MUST BE SAME
    AS FIRST ITEM IN BATCH|T24003095"
    - "Possible matches cannot be manually set Possible matches cannot be manually
    set|T24003096"
    - "Posting restriction is mandatory when & is input Posting restriction is mandatory
    when & is input|T24003097"
    - "POSTING.DETAIL Not Found POSTING.DETAIL Not Found|T24003098"
    - "POSTING.DETAIL not set &, &. POSTING.DETAIL not set &, &.|T24003099"
    - "PR ATTRIBUTE MISSING PR ATTRIBUTE MISSING|T24003100"
    - "PR PROPERTY CLASS DOES NOT MATCH PR PROPERTY CLASS DOES NOT MATCH|T24003101"
    - "PR.ATTR.ID should not be same as Attribute or Local Ref PR.ATTR.ID should not
    be same as Attribute or Local Ref|T24003102"
    - "PR.ATTRIBUTE defined NON-NEGOTIABLE cannot input value PR.ATTRIBUTE defined
    NON-NEGOTIABLE cannot input value|T24003103"
    - "Pre notice date & is less than activity date & Pre notice date & is less than
    activity date &|T24003104"
    - "PRE OR POST OR RECORD OR VALIDATE ROUTINE IS MANDATORY PRE OR POST OR RECORD
    OR VALIDATE ROUTINE IS MANDATORY|T24003105"
    - "Prefix & is Duplicated in & and & Prefix & is Duplicated in & and &|T24003106"
    - "PREFIX ROUTINE NAME WITH @ PREFIX ROUTINE NAME WITH @|T24003107"
    - "Prefix should not be & in development environment Prefix should not be & in
    development environment|T24003108"
    - "prefix/directory name missing prefix/directory name missing|T24003109"
    - "PREMIUM TYPE CANNOT BE CHANGED - ALREADY CAPITALISED PREMIUM TYPE CANNOT BE
    CHANGED - ALREADY CAPITALISED|T24003110"
    - "Prenotice date & is less than valuedate & of arragement Prenotice date & is
    less than valuedate & of arragement|T24003111"
    - "Pre-select error Pre-select error|T24003112"
    - "Primary owner should be one of other party Primary owner should be one of other
    party|T24003113"
    - "Principal amount not equal to Disburse Amount Principal amount not equal to
    Disburse Amount|T24003114"
    - "Principal amount not equal to Settlement Amount Principal amount not equal to
    Settlement Amount|T24003115"
    - "Principal Balance will go -ve due to & Capitalisation Principal Balance will
    go -ve due to & Capitalisation|T24003116"
    - "PRINCIPAL REQUIRED PRINCIPAL REQUIRED|T24003117"
    - "PRIORITY CANNOT BE ZERO OR LESSTHAN ZERO PRIORITY CANNOT BE ZERO OR LESSTHAN
    ZERO|T24003118"
    - "PRIORITY DUPLICATED PRIORITY DUPLICATED|T24003119"
    - "PRIORITY MUST BE BLANK FOR STATUS PRIORITY MUST BE BLANK FOR STATUS|*********"
    - "Process already shutdown Process already shutdown|*********"
    - "Process records cant be changed Process records cant be changed|*********"
    - "PROCESS.PAYMENTS NOT SET PROCESS.PAYMENTS NOT SET|*********"
    - "Processing message & Processing message &|*********"
    - "Product & does not exist for & Product & does not exist for &|*********"
    - "PRODUCT & EXPIRED ON & PRODUCT & EXPIRED ON &|*********"
    - "Product & not licensed Product & not licensed|*********"
    - "Product & should be Proofed first. Product & should be Proofed first.|*********"
    - "Product already linked in Bundle, Cant change Product Product already linked
    in Bundle, Cant change Product|*********"
    - "Product Category not required for PL Categ 60000-69999 Product Category not
    required for PL Categ 60000-69999|*********"
    - "Product Category not valid Product Category not valid|*********"
    - "Product change allowed only for Collection Product change allowed only for Collection|*********"
    - "Product change not allowed-Linked to BL.BILL Product change not allowed-Linked
    to BL.BILL|*********"
    - "Product Code & is Invalid for Class Type & Product Code & is Invalid for Class
    Type &|*********"
    - "Product does not match with ID Product does not match with ID|*********"
    - "PRODUCT FORMAT IS INVALID PRODUCT FORMAT IS INVALID|*********"
    - "PRODUCT GROUP & MISSING PRODUCT GROUP & MISSING|*********"
    - "Product Group should belong to an eligible Product Line Product Group should
    belong to an eligible Product Line|*********"
    - "Product Group should belong to the Product Line Defined Product Group should
    belong to the Product Line Defined|T24003139"
    - "PRODUCT INPUT IS INVALID. CANNOT INPUT RATES PRODUCT INPUT IS INVALID. CANNOT
    INPUT RATES|T24003140"
    - "Product is define with CCY,but CCY is not supplied Product is define with CCY,but
    CCY is not supplied|T24003141"
    - "Product Type not defined Product Type not defined|T24003154"
    - "Product wrongly configured, new arrangement denied Product wrongly configured,
    new arrangement denied|T24003155"
    - "PRODUCT.ID is mandatory PRODUCT.ID is mandatory|T24003156"
    - "PRODUCT.ONLY PROPERTY SHOULD BE SET TO TRACKING PRODUCT.ONLY PROPERTY SHOULD
    BE SET TO TRACKING|T24003157"
    - "PRODUCT.ONLY should be set for Tracking Only classes PRODUCT.ONLY should be
    set for Tracking Only classes|T24003158"
    - "Profile type should be of Internal Type Profile type should be of Internal Type|T24003159"
    - "Profit amount cannot be negative Profit amount cannot be negative|T24003160"
    - "Profit Amount should not be null Profit Amount should not be null|T24003161"
    - "Profit entry cannot be raised. Profit entry cannot be raised.|T24003162"
    - "Profit recalculation is only applicable when Interest Method is Fixed Profit
    recalculation is only applicable when Interest Method is Fixed|T24003163"
    - "PROG.PAY.PERC IS MANDATORY IF CALC.TYPE IS PROGRESSIVE PROG.PAY.PERC IS MANDATORY
    IF CALC.TYPE IS PROGRESSIVE|T24003164"
    - "PROG.PAY.PERC VALUE REQUIRED PROG.PAY.PERC VALUE REQUIRED|T24003165"
    - "PROGRAM MUST BE OF TYPE S PROGRAM MUST BE OF TYPE S|T24003166"
    - "PROGRAM NOT COMPILED PROGRAM NOT COMPILED|T24003167"
    - "Prop type should be current for current appln type Prop type should be current
    for current appln type|T24003168"
    - "ONLY N PRIORITY ALLOWED ONLY N PRIORITY ALLOWED|T24002931"
    - "ONLY NINE ALLOWED - PLEASE DELETE ONLY NINE ALLOWED - PLEASE DELETE|T24002932"
    - "ONLY NINE VALUES ALLOWED ONLY NINE VALUES ALLOWED|T24002933"
    - "ONLY TWO VALUES ALLOWED ONLY TWO VALUES ALLOWED|T24002958"
    - "ONLY TYPES DEFINED UNDER PROPERTY CAN BE STATED HERE ONLY TYPES DEFINED UNDER
    PROPERTY CAN BE STATED HERE|T24002959"
    - "ONLY USED WHEN STATUS EQUAL HOLD ONLY USED WHEN STATUS EQUAL HOLD|T24002960"
    - "Only valid for Secondary Activities Only valid for Secondary Activities|T24002961"
    - "ONLY VALID LENGTHS OF 8,9,11,12 ARE ALLOWED ONLY VALID LENGTHS OF 8,9,11,12
    ARE ALLOWED|T24002962"
    - "OPEN ORDER� &  OPEN ORDER� &|T24002963"
    - "Operand and Margin are mandatory Operand and Margin are mandatory|T24002964"
    - "Operand is mandatory if margin is sepcified Operand is mandatory if margin is
    sepcified|T24002965"
    - "OPERAND MISSING OPERAND MISSING|T24002966"
    - "Operation Should be DISCOUNT for Disbursement Operation Should be DISCOUNT for
    Disbursement|T24002967"
    - "Oppor Status Value is Missing Oppor Status Value is Missing|T24002968"
    - "Opportunity & for this product already Exists Opportunity & for this product
    already Exists|T24002969"
    - "Opportunity & has already been rejected Opportunity & has already been rejected|T24002970"
    - "OVR.CARRIER IS MISSING OVR.CARRIER IS MISSING|T24002994"
    - "PAGES 1 TO & ONLY PAGES 1 TO & ONLY|T24002995"
    - "PARENT PRODUCT & NOT AVAILABLE ON & PARENT PRODUCT & NOT AVAILABLE ON &|T24002996"
    - "PRODUCT NOT FOR SALE PRODUCT NOT FOR SALE|T24003149"
    - "PRODUCT NOT INSTALLED PRODUCT NOT INSTALLED|T24003150"
    - "Product Only prop type not allowed for Agent Act Charge Product Only prop type
    not allowed for Agent Act Charge|T24003151"
    - "Product should belong to an eligible Product Line Product should belong to an
    eligible Product Line|T24003152"
    - "Product should belong to the specified Product Group Product should belong to
    the specified Product Group|T24003153"
    - "PROPERTIES BALANCE TYPE DIFFER-BILLS CANT BE COMBINED PROPERTIES BALANCE TYPE
    DIFFER-BILLS CANT BE COMBINED|T24003169"
    - "Properties should be in order Charge,Interest & Account Properties should be
    in order Charge,Interest & Account|T24003170"
    - "Property & doesnt belong to & Property Class Property & doesnt belong to
    & Property Class|T24003171"
    - "PROPERTY & IN CONSTRAINT NOT IN PRODUCT PROPERTY & IN CONSTRAINT NOT IN PRODUCT|T24003172"
    - "PROPERTY & IN PAYMENT SCHEDULE NOT IN PRODUCT PROPERTY & IN PAYMENT SCHEDULE
    NOT IN PRODUCT|T24003173"
    - "Property & is Not Belongs to Product Group & Property & is Not Belongs to Product
    Group &|T24003174"
    - "PROPERTY & MISSING PROPERTY & MISSING|T24003175"
    - "Property & not configured for Advance repayment Property & not configured for
    Advance repayment|T24003176"
    - "Property & Not Defined In Product Property & Not Defined In Product|T24003177"
    - "PROPERTY CLASS AND PROPERTY BOTH CANNOT BE ENTERED PROPERTY CLASS AND PROPERTY
    BOTH CANNOT BE ENTERED|T24003191"
    - "PROPERTY CLASS CANNOT BE NULL PROPERTY CLASS CANNOT BE NULL|T24003192"
    - "PROPERTY CLASS MISSING PROPERTY CLASS MISSING|T24003193"
    - "PROPERTY CLASS NOT DEFINED IN PRODUCT LINE PROPERTY CLASS NOT DEFINED IN PRODUCT
    LINE|T24003195"
    - "PROPERTY CLASS NOT DEFINED IN PRODUCT PROPERTY CLASS NOT DEFINED IN PRODUCT|T24003194"
    - "Property Class Should Be Forward Dated Property Class Should Be Forward Dated|T24003196"
    - "Property class with not available Property class with not available|T24003197"
    - "PROPERTY CONDITION FOR & COULD NOT BE RESOLVED PROPERTY CONDITION FOR & COULD
    NOT BE RESOLVED|T24003198"
    - "Property date should not be null Property date should not be null|T24003199"
    - "Property does not belong to fixed interest Property does not belong to fixed
    interest|T24003200"
    - "Property has already been capitalised Property has already been capitalised|T24003201"
    - "Property is mandatory if Exclude is set Property is mandatory if Exclude is
    set|T24003202"
    - "PROPERTY is not allowed if BALANCE.TYPE is entered PROPERTY is not allowed if
    BALANCE.TYPE is entered|T24003203"
    - "Property is not defined in Payment Schedule Condition Property is not defined
    in Payment Schedule Condition|T24003204"
    - "Property key used by another activity. Only deletion allowed Property key used
    by another activity. Only deletion allowed|T24003205"
    - "PROPERTY MANDATORY PROPERTY MANDATORY|T24003206"
    - "Property Missing in Product Group Property Missing in Product Group|T24003207"
    - "PROPERTY NOT ALLOWED FOR VARIATION PROPERTY NOT ALLOWED FOR VARIATION|T24003208"
    - "Property not applicable for advance payments Property not applicable for advance
    payments|T24003209"
    - "Property not defined in payment schedule Property not defined in payment schedule|T24003210"
    - "PROPERTY NOT DEFINED IN PRODUCT PROPERTY NOT DEFINED IN PRODUCT|T24003211"
    - "PROPERTY NOT DEFINED IN THE PRODUCT PROPERTY NOT DEFINED IN THE PRODUCT|T24003212"
    - "Property Not in Product Definition Property Not in Product Definition|T24003214"
    - "Property not in Product Group Property not in Product Group|T24003215"
    - "Property not in product Property not in product|T24003213"
    - "PROPERTY NOT OF THIS PROPERTY CLASS PROPERTY NOT OF THIS PROPERTY CLASS|T24003216"
    - "Property not part of arrangement record Property not part of arrangement record|T24003217"
    - "Property not suspended Property not suspended|T24003218"
    - "Property or Property Class is mandatory Property or Property Class is mandatory|T24003219"
    - "Property should belong to the Charge Property Class Property should belong to
    the Charge Property Class|T24003220"
    - "Property should belong to the Interest Property Class Property should belong
    to the Interest Property Class|T24003221"
    - "Property type of Commission allowed only for AGENT line Property type of Commission
    allowed only for AGENT line|T24003222"
    - "Property type Suspend Not Allowed for Credit Property Property type Suspend
    Not Allowed for Credit Property|T24003223"
    - "Property Valid only for Capture Bill Property Valid only for Capture Bill|T24003224"
    - "PROPERTY.CLASS AND VERSION MANDATORY PROPERTY.CLASS AND VERSION MANDATORY|T24003225"
    - "PropertyClass of SourceProperty and ATTRIBUTE.PROP.CLASS in AA.SOURCE.CALC.TYPE
    doesnot match PropertyClass of SourceProperty and ATTRIBUTE.PROP.CLASS in AA.SOURCE.CALC.TYPE
    doesnot match|T24003226"
    - "PROSPECT CUSTOMER NOT ALLOWED WHEN WALKIN CUSTOMER IS NO PROSPECT CUSTOMER NOT
    ALLOWED WHEN WALKIN CUSTOMER IS NO|T24003227"
    - "Protection Limit Exceeded. Please contact your Bank. Protection Limit Exceeded.
    Please contact your Bank.|T24003228"
    - "PTT ACCOUNT NUMBER MUST BE P AND UP TO 7 NUMERIC PTT ACCOUNT NUMBER MUST
    BE P AND UP TO 7 NUMERIC|T24003229"
    - "PW Workflow Error PW Workflow Error|T24003230"
    - "QTY IN REGISTER < QTY IN ENTRY QTY IN REGISTER < QTY IN ENTRY|T24003231"
    - "QUANTITY NOT AVAILABLE IN STOCK.REGISTER QUANTITY NOT AVAILABLE IN STOCK.REGISTER|T24003232"
    - "Query is Invalid Query is Invalid|T24003233"
    - "Query is Mandatory Query is Mandatory|T24003234"
    - "Query not Allowed Query not Allowed|T24003235"
    - "Quotation request date is greater than definition expiry date Quotation request
    date is greater than definition expiry date|T24003236"
    - "Quotation request date is lesser than definition available date Quotation request
    date is lesser than definition available date|T24003237"
    - "RANGE LIMIT CONFLICTS RANGE LIMIT CONFLICTS|T24003238"
    - "RATE GREATER THAN 10 RATE GREATER THAN 10%|T24003239"
    - "RATE IS MANDATORY IF PRODUCT IS INPUT RATE IS MANDATORY IF PRODUCT IS INPUT|T24003240"
    - "Rate shouldnt set when linked rate ie set to YES Rate shouldnt set when
    linked rate ie set to YES|T24003242"
    - "Rate recalculation is applicable only for non fixed type of Interest Rate recalculation
    is applicable only for non fixed type of Interest|T24003241"
    - "Rate type can be set for Fixed type of interest Rate type can be set for Fixed
    type of interest|*********"
    - "Rate type can not input if interest method is NULL Rate type can not input if
    interest method is NULL|*********"
    - "RATHER STRANGE CANT READ & RATHER STRANGE CANT READ &|*********"
    - "RECEIVER BANK SAME AS ACCT WITH BANK RECEIVER BANK SAME AS ACCT WITH BANK|*********"
    - "RECEIVER CORR BANK MISSING RECEIVER CORR BANK MISSING|********2"
    - "Receiver Not Specified Receiver Not Specified|********3"
    - "Recipient should be the Master defined in Bundle. Recipient should be the Master
    defined in Bundle.|********4"
    - "RECONCILE ACCT NOT SET TO Y RECONCILE ACCT NOT SET TO Y|********5"
    - "RECORD & NOT FOUND IN SECURITY.POSITION RECORD & NOT FOUND IN SECURITY.POSITION|********6"
    - "Record & not found on & Record & not found on &|********7"
    - "RECORD & NOT FOUND ON FILE & RECORD & NOT FOUND ON FILE &|********8"
    - "RECORD ALREADY IN USE RECORD ALREADY IN USE|********9"
    - "RECORD CANNOT BE BLANK RECORD CANNOT BE BLANK|*********"
    - "RECORD CANNOT BE MATCHED TO ITSELF RECORD CANNOT BE MATCHED TO ITSELF|*********"
    - "Record cant be created as cust fields are undefined Record cant be created as
    cust fields are undefined|*********"
    - "Payoff amt less than repay amt Payoff amt less than repay amt|*********"
    - "Payoff bill not found, run payoff in simulation mode Payoff bill not found,
    run payoff in simulation mode|*********"
    - "PAYOFF SETTLE ACTIVITY MANDATORY PAYOFF SETTLE ACTIVITY MANDATORY|*********"
    - "Payout Account is Mandatory. Payout Account is Mandatory.|T24003039"
    - "Payout Activity mandatory for give payout AA account. Payout Activity mandatory
    for give payout AA account.|T24003040"
    - "Payout amount is greater than the available balances Payout amount is greater
    than the available balances|T24003041"
    - "PI TABLE NOT FOUND FOR LOCAL CURRENCY PI TABLE NOT FOUND FOR LOCAL CURRENCY|T24003062"
    - "PL ACCOUNT PL ACCOUNT|T24003063"
    - "PL Category is not allowed for this batch PL Category is not allowed for this
    batch|T24003064"
    - "PL CATEGORY NOT IN RANGE (10000-19999) PL CATEGORY NOT IN RANGE (10000-19999)|T24003066"
    - "PL Category Not In Range PL Category Not In Range|T24003065"
    - "PLEDGE NOT GIVEN TO MAIN CUSTOMER PLEDGE NOT GIVEN TO MAIN CUSTOMER|T24003085"
    - "PO PRODUCT IS NOT ACCOUNT TRANSFER TYPE PO PRODUCT IS NOT ACCOUNT TRANSFER TYPE|T24003086"
    - "PO PRODUCT IS NOT BENEFICIARY TRANSFER TYPE PO PRODUCT IS NOT BENEFICIARY TRANSFER
    TYPE|T24003087"
    - "PO.PRODUCT is mandatory when BENEFICIARY is defined. PO.PRODUCT is mandatory
    when BENEFICIARY is defined.|T24003089"
    - "Portfolio does not belong to Customer & Portfolio does not belong to Customer
    &|T24003090"
    - "Portfolio Id is not valid for Arrangement Customer Portfolio Id is not valid
    for Arrangement Customer|T24003091"
    - "Position Account not allowed Position Account not allowed|T24003092"
    - "Position class input missing Position class input missing|T24003093"
    - "Sorry, arrangement is linked as AUTO.PAY.ACCT to the arrangement account &.
    Please delink the account for closure. Sorry, arrangement is linked as AUTO.PAY.ACCT
    to the arrangement account &. Please delink the account for closure.|T24003462"
    - "RECORD NOT FOUND RECORD NOT FOUND|T24003286"
    - "RECORD NOT ON MAPPING FILE RECORD NOT ON MAPPING FILE|T24003289"
    - "RECORD NUMBER DUPLICATED RECORD NUMBER DUPLICATED|T24003290"
    - "RECORD NUMBER REQUIRED RECORD NUMBER REQUIRED|T24003291"
    - "Rate type should be set when interest method is set to fixed Rate type should
    be set when interest method is set to fixed|T24003245"
    - "RE.TXN.CODE mandatory RE.TXN.CODE mandatory|T24003247"
    - "RECORD LOCKED - CANNOT STOP PROCESS RECORD LOCKED - CANNOT STOP PROCESS|T24003279"
    - "RECORD LOCKED - RETRYING PLEASE WAIT RECORD LOCKED - RETRYING PLEASE WAIT|T24003280"
    - "RECORD LOCKED BY UESR RECORD LOCKED BY UESR|T24003281"
    - "RECORD LOCKED RECORD LOCKED|T24003278"
    - "RECORD MAY ONLY BE DELETED RECORD MAY ONLY BE DELETED|T24003282"
    - "RECORD MISSING & RECORD MISSING &|T24003285"
    - "RECORD MISSING RECORD MISSING|T24003283"
    - "RECORD NOT FOUND ON FWD.ENTRY FILE ID = & RECORD NOT FOUND ON FWD.ENTRY FILE
    ID = &|T24003287"
    - "RECORD PUBLISHED. FUNCTION NOT ALLOWED RECORD PUBLISHED. FUNCTION NOT ALLOWED|T24003292"
    - "RECORD STATUS IS & RECORD STATUS IS &|T24003293"
    - "RECORD SYSTEM.STATUS IS MISSING FROM DE.PARM RECORD SYSTEM.STATUS IS MISSING
    FROM DE.PARM|T24003294"
    - "Record to check latest updates in AC_AccountOpening component. Record to check
    latest updates in AC_AccountOpening component.|T24003295"
    - "Record to check latest updates in AC_Config component. Record to check latest
    updates in AC_Config component.|T24003296"
    - "Redeem Not Allowed, Uncleared Funds exist Redeem Not Allowed, Uncleared Funds
    exist|T24003297"
    - "Redefine exposure date cannot be greater than today Redefine exposure date cannot
    be greater than today|T24003298"
    - "REDISPLAY REQUESTED REDISPLAY REQUESTED|T24003299"
    - "Reducing rate can be defined only for term contracts Reducing rate can be defined
    only for term contracts|T24003300"
    - "REF.NO NOT NUMERIC REF.NO NOT NUMERIC|T24003301"
    - "Refer Limit is applicable only for LEVELS or BANDS Refer Limit is applicable
    only for LEVELS or BANDS|T24003302"
    - "REFER.LIMIT applicable only for TIER.AMOUNT REFER.LIMIT applicable only for
    TIER.AMOUNT|T24003303"
    - "REFER.LIMIT is set hence limit must be managed by AA REFER.LIMIT is set hence
    limit must be managed by AA|T24003304"
    - "Register already attached in bills for settlement Register already attached
    in bills for settlement|T24003305"
    - "Register already attached to batch Register already attached to batch|T24003306"
    - "REGISTER BELONGS TO DIFFERENT BRANCH REGISTER BELONGS TO DIFFERENT BRANCH|T24003307"
    - "Registers with Same Maturity Registers with Same Maturity|T24003308"
    - "RELATION CODE CANNOT BE ENTERED RELATION CODE CANNOT BE ENTERED|T24003309"
    - "RELATION CODE MUST BE ENTERED RELATION CODE MUST BE ENTERED|T24003310"
    - "RELATIONSHIP TO NEXT CONDITION MISSING RELATIONSHIP TO NEXT CONDITION MISSING|T24003311"
    - "RELATIVE END LINE MUST BE > 0 RELATIVE END LINE MUST BE > 0|T24003312"
    - "Remove ACCRUE.AMORT for & .Amortisation Not Allowed for Rewards Product Line
    Remove ACCRUE.AMORT for & .Amortisation Not Allowed for Rewards Product Line|T24003313"
    - "Remove this JOINT.HOLDER since same as CUSTOMER Remove this JOINT.HOLDER since
    same as CUSTOMER|T24003314"
    - "REPAIR NOT ALLOWED REPAIR NOT ALLOWED|T24003315"
    - "Repay Amount Exceeds the Overdue Amount Repay Amount Exceeds the Overdue Amount|T24003316"
    - "Request closure activity exist for today Request closure activity exist for
    today|T24003317"
    - "REQUIRED WHEN LAST.CHEQUE.NO IS GIVEN REQUIRED WHEN LAST.CHEQUE.NO IS GIVEN|T24003318"
    - "Reservation failed Reservation failed|T24003319"
    - "Reservation record missing Reservation record missing|T24003320"
    - "Reset Advance Days allowed only for Single Tier Reset Advance Days allowed only
    for Single Tier|T24003321"
    - "Residual accrual property not allowed for Advance type Residual accrual property
    not allowed for Advance type|T24003322"
    - "RESIDUAL AMOUNT CAN NOT BE SPECIFIED ON CALL RESIDUAL AMOUNT CAN NOT BE SPECIFIED
    ON CALL|T24003323"
    - "RESIDUAL.ACCRUAL not allowed for FIXED type of interest RESIDUAL.ACCRUAL not
    allowed for FIXED type of interest|T24003324"
    - "Restore can done only on closed arrangement Restore can done only on closed
    arrangement|T24003325"
    - "Restrict change between advance and periodic reset Restrict change between advance
    and periodic reset|T24003326"
    - "Restricted Balance type are not allowed. Restricted Balance type are not allowed.|T24003327"
    - "Restricted Property are not allowed. Restricted Property are not allowed.|T24003328"
    - "RESUBMIT DATE SHOULD BE GREATER THAN TODAY RESUBMIT DATE SHOULD BE GREATER THAN
    TODAY|T24003329"
    - "RESUBMIT NOT ALLOWED RESUBMIT NOT ALLOWED|T24003330"
    - "RETURNED.ITEM EVENT NOT PRESENT IN DD.CODES RETURNED.ITEM EVENT NOT PRESENT
    IN DD.CODES|T24003331"
    - "REVALUATION PARANETER FOR AL NOT DEFINED REVALUATION PARANETER FOR AL
    NOT DEFINED|T24003332"
    - "Reversal allowed only for manually created request Reversal allowed only for
    manually created request|T24003333"
    - "Reversal Not Allowed - Arrangement Exist Reversal Not Allowed - Arrangement
    Exist|T24003336"
    - "Reversal Not Allowed - Product Exist for Product Group Reversal Not Allowed
    - Product Exist for Product Group|T24003337"
    - "Reversal Not Allowed - Product group exist for property Reversal Not Allowed
    - Product group exist for property|T24003338"
    - "REVERSAL NOT ALLOWED FOR COVER REVERSAL NOT ALLOWED FOR COVER|T24003339"
    - "REVERSAL NOT ALLOWED REVERSAL NOT ALLOWED|T24003334"
    - "Reversal not allowed, & arrangement is linked Reversal not allowed, & arrangement
    is linked|T24003340"
    - "Reversal of reservation failed Reversal of reservation failed|T24003341"
    - "Reverse user and transaction activities before Reverse user and transaction
    activities before|T24003342"
    - "REVIEW FREQ NOT ALLOWED WITHOUT INFLATION RATE REVIEW FREQ NOT ALLOWED WITHOUT
    INFLATION RATE|T24003343"
    - "Rewards Arrangement Not Authorised Rewards Arrangement Not Authorised|T24003344"
    - "Rewards Arrangement Start Date less than effective date Rewards Arrangement
    Start Date less than effective date|T24003345"
    - "Rewards type only for Agent Commission property Rewards type only for Agent
    Commission property|T24003346"
    - "RIGHTMOST SCREEN DISPLAYED RIGHTMOST SCREEN DISPLAYED|T24003347"
    - "ROLL.APP ID SHOULD NOT BE SAME AS AZ.PRODUCT ID ROLL.APP ID SHOULD NOT BE SAME
    AS AZ.PRODUCT ID|T24003348"
    - "Routine & Is not Catalogued Routine & Is not Catalogued|T24003349"
    - "Routine & is Not Defined in EB.API Routine & is Not Defined in EB.API|T24003350"
    - "Routine allowed only for PIA APPL.TYPE Routine allowed only for PIA APPL.TYPE|T24003351"
    - "ROUTINE DOES NOT EXIST ROUTINE DOES NOT EXIST|T24003352"
    - "ROUTINE IS INCORRECT TYPE ROUTINE IS INCORRECT TYPE|T24003353"
    - "ROUTINE IS NOT CATALOGUED ROUTINE IS NOT CATALOGUED|T24003354"
    - "ROUTINE NAME MUST BE PRECEDED BY @ ROUTINE NAME MUST BE PRECEDED BY @|*********"
    - "Sell fixed rate missing Sell fixed rate missing|*********"
    - "seller id cannot be modified seller id cannot be modified|*********"
    - "Seller ID mandatory for REBATCH/CHANGE.PRODUCT Seller ID mandatory for REBATCH/CHANGE.PRODUCT|*********"
    - "PRODUCT IS MISSING. CANNOT INPUT PRODUCT RATES PRODUCT IS MISSING. CANNOT INPUT
    PRODUCT RATES|*********"
    - "Product IS Product IS|*********"
    - "Product line & not licensed Product line & not licensed|*********"
    - "PRODUCT MISSING PRODUCT MISSING|*********"
    - "Product not available for Sale in this company Product not available for Sale
    in this company|*********"
    - "Product not available on this date Product not available on this date|*********"
    - "Product not defined for agent Product not defined for agent|*********"
    - "Property & Not Defined With Amortisation Property & Not Defined With Amortisation|*********"
    - "Property & should have FORWARD.DATED in TYPE Property & should have FORWARD.DATED
    in TYPE|*********"
    - "Property & should not be RESIDUAL.ACCRUAL in TYPE Property & should not be RESIDUAL.ACCRUAL
    in TYPE|*********"
    - "PROPERTY AND CLASS TYPES INCOMPATIBLE PROPERTY AND CLASS TYPES INCOMPATIBLE|*********"
    - "Property and Type definition already defined before Property and Type definition
    already defined before|*********"
    - "PROPERTY AND VERSION MANDATORY PROPERTY AND VERSION MANDATORY|*********"
    - "Property can not be left blank Property can not be left blank|T24003185"
    - "Property Cannot Be Changed Property Cannot Be Changed|T24003186"
    - "Property cannot be created with keyword ARRANGEMENT Property cannot be created
    with keyword ARRANGEMENT|T24003187"
    - "PROPERTY CANNOT BE EMPTY PROPERTY CANNOT BE EMPTY|T24003188"
    - "PROPERTY CANNOT BE TRACKED PROPERTY CANNOT BE TRACKED|T24003189"
    - "PROPERTY CLASS / PROPERTY MANDATORY PROPERTY CLASS / PROPERTY MANDATORY|T24003190"
    - "Select Concentration cap Level Select Concentration cap Level|T24003373"
    - "Select YES/NO Select YES/NO|T24003374"
    - "Selected Channel not available.Pls contact us E-112154 Selected Channel not
    available.Pls contact us E-112154|T24003375"
    - "SELECTED FIELD IS NOT IN APPLICATION SELECTED FIELD IS NOT IN APPLICATION|T24003376"
    - "Selection must be NO for PROCESS Selection must be NO for PROCESS|T24003377"
    - "Selection must be YES for PROCESS Selection must be YES for PROCESS|T24003378"
    - "Selection required Selection required|T24003379"
    - "Sell amount not numeric Sell amount not numeric|T24003380"
    - "SELL CCY MKT suspended & SELL CCY MKT suspended &|T24003381"
    - "Sell currency record missing Sell currency record missing|T24003382"
    - "Seller Limit exposure exceeds Buyers exposure Seller Limit exposure exceeds
    Buyers exposure|T24003386"
    - "SEND MSG TYPE IS NOT EQUAT TO Y SEND MSG TYPE IS NOT EQUAT TO Y|T24003387"
    - "SEND.MSG.TYPE FIELD SHOULD BE Y OR STP OR MOV SEND.MSG.TYPE FIELD
    SHOULD BE Y OR STP OR MOV|T24003388"
    - "SENDER DETAIL must exist if there is SENDER INFO SENDER DETAIL must exist if
    there is SENDER INFO|T24003389"
    - "SEQ MUST BE POSITIVE SEQ MUST BE POSITIVE|T24003390"
    - "SEQUENCE NO IS MISSING SEQUENCE NO IS MISSING|T24003391"
    - "SERIES ID IS NOT STOCKED IN STOCK REGISTER SERIES ID IS NOT STOCKED IN STOCK
    REGISTER|T24003392"
    - "SERIES SHOULD BE NUMERIC SERIES SHOULD BE NUMERIC|T24003393"
    - "Set CHECK.CUST.LEVEL to track for customer appln Set CHECK.CUST.LEVEL to track
    for customer appln|T24003394"
    - "SET IN ACCOUNT.ACCRUAL SET IN ACCOUNT.ACCRUAL|T24003395"
    - "SETTLE A/C SHOULD BE SAME CCY FOR THIS MODE SETTLE A/C SHOULD BE SAME CCY FOR
    THIS MODE|T24003396"
    - "Settle Method Mandatory When Mandate Ref is Given Settle Method Mandatory When
    Mandate Ref is Given|T24003397"
    - "Settlement account already closed Settlement account already closed|T24003398"
    - "Settlement Account and Arrangement Account cannot be same Settlement Account
    and Arrangement Account cannot be same|T24003399"
    - "SETTLEMENT ACCOUNT IS CLOSED SETTLEMENT ACCOUNT IS CLOSED|T24003400"
    - "SETTLEMENT ACCOUNT IS CLOSING SETTLEMENT ACCOUNT IS CLOSING|T24003401"
    - "Settlement Activity is Mandatory for Payment type. Settlement Activity is Mandatory
    for Payment type.|T24003405"
    - "Settlement activity is mandatory for ppty or ppty class Settlement activity
    is mandatory for ppty or ppty class|T24003406"
    - "Settlement Amount Greater than Bill Register Amount Settlement Amount Greater
    than Bill Register Amount|T24003407"
    - "Settlement Amount must be less than Inv Amt for REBATE Settlement Amount must
    be less than Inv Amt for REBATE|T24003408"
    - "Settlement details not available in & Settlement details not available in &|T24003409"
    - "Settlement instruction should be configured for Account Settlement instruction
    should be configured for Account|T24003410"
    - "SHOULD BE 4 CHAR SHOULD BE 4 CHAR|T24003411"
    - "Should be a contingent account Should be a contingent account|T24003412"
    - "Should be a interest property class Should be a interest property class|T24003413"
    - "Should be a Valid Interest Property Should be a Valid Interest Property|T24003414"
    - "Should be a valid variation defined in product Should be a valid variation defined
    in product|T24003415"
    - "Should be Allowed for Product Discounting Should be Allowed for Product Discounting|T24003416"
    - "Should be an Internal Balance Type Should be an Internal Balance Type|T24003417"
    - "Should be Classified as SERVICE Should be Classified as SERVICE|T24003418"
    - "SHOULD BE COMPANY.ID OR SYSTEM SHOULD BE COMPANY.ID OR SYSTEM|T24003419"
    - "SHOULD BE GREATER THAN REQUEST.ADVICE DAYS SHOULD BE GREATER THAN REQUEST.ADVICE
    DAYS|T24003420"
    - "SHOULD BE GREATER THAN TODAY SHOULD BE GREATER THAN TODAY|T24003421"
    - "Should be in ascending order Should be in ascending order|T24003422"
    - "Should be in Product allowed list Should be in Product allowed list|T24003423"
    - "SHOULD BE IN SAME FORMAT SHOULD BE IN SAME FORMAT|T24003424"
    - "SHOULD BE INBOUND IF ID IS PRODUCT OR PRODUCT GROUP SHOULD BE INBOUND IF ID
    IS PRODUCT OR PRODUCT GROUP|T24003425"
    - "Should be like +2W or +10C Should be like +2W or +10C|T24003426"
    - "SHOULD BE NOT AS THE ABOVE SET SHOULD BE NOT AS THE ABOVE SET|T24003427"
    - "Should be Null for this combination Should be Null for this combination|T24003429"
    - "Should be Null Should be Null|T24003428"
    - "SHOULD BE ONE OF THE OWNERS SHOULD BE ONE OF THE OWNERS|T24003430"
    - "SHOULD BE S TYPE IN PGM.FILE SHOULD BE S TYPE IN PGM.FILE|T24003431"
    - "Should Be Valid Comparison Type From Class File Should Be Valid Comparison Type
    From Class File|T24003432"
    - "Should be valid Info Limit Reference Should be valid Info Limit Reference|T24003433"
    - "Should Be Valid Type From Class File Should Be Valid Type From Class File|T24003434"
    - "SHOULD BE WITHIN 10000-19999 SHOULD BE WITHIN 10000-19999|T24003435"
    - "SHOULD BE WITHIN 3000-9999 SHOULD BE WITHIN 3000-9999|T24003436"
    - "SHOULD BE YES FOR BACK.VALUE.ADJ SHOULD BE YES FOR BACK.VALUE.ADJ|T24003437"
    - "Should be YES for top account Should be YES for top account|T24003438"
    - "SHOULD BE YES FOR VALUE DATED SHOULD BE YES FOR VALUE DATED|T24003439"
    - "SHOULD BE YES ONLY FOR PRIORITY SHOULD BE YES ONLY FOR PRIORITY|T24003440"
    - "SHOULD NOT EXCEED 31 SHOULD NOT EXCEED 31|T24003446"
    - "SHOULD NOT EXCEED 35 LINES SHOULD NOT EXCEED 35 LINES|T24003447"
    - "SHOULD NOT REMOVE THE CURRENCY OF PUBLISHED PRODUCT SHOULD NOT REMOVE THE CURRENCY
    OF PUBLISHED PRODUCT|T24003448"
    - "Should use only INTERPOLATE option for AA Products Should use only INTERPOLATE
    option for AA Products|T24003449"
    - "Shoule be valid PERIOD-PERIOD format Shoule be valid PERIOD-PERIOD format|T24003450"
    - "SIC FIELD MUST BE NUMERIC FOR SWIFT SIC FIELD MUST BE NUMERIC FOR SWIFT|T24003451"
    - "SIGN MISSING SIGN MISSING|T24003452"
    - "SIGN MUST BE CREDIT FOR REVERSAL SIGN MUST BE CREDIT FOR REVERSAL|T24003453"
    - "SIGN MUST BE DEBIT FOR REVERSAL SIGN MUST BE DEBIT FOR REVERSAL|T24003454"
    - "Signature Required should set to Yes if Valid Period Base date is Signature
    Date Signature Required should set to Yes if Valid Period Base date is Signature
    Date|T24003455"
    - "SIGNS MUST BE THE SAME SIGNS MUST BE THE SAME|T24003456"
    - "Sim date should be in ascending order Sim date should be in ascending order|T24003457"
    - "SIM.INPUT.MAPPING is mandatory SIM.INPUT.MAPPING is mandatory|T24003458"
    - "Simulation start date less than run date Simulation start date less than run
    date|T24003459"
    - "SINGLE FCY OUT OF BALANCE SINGLE FCY OUT OF BALANCE|T24003460"
    - "SINGLE LINK LIMIT - INVALID INPUT SINGLE LINK LIMIT - INVALID INPUT|T24003461"
    - "Sorry, but Arrangement is already in & Status Sorry, but Arrangement is already
    in & Status|T24003463"
    - "Sorry, link account & is in & Status Sorry, link account & is in & Status|T24003470"
    - "Record not found on STMT.ENTRY File Record not found on STMT.ENTRY File|T24003288"
    - "RE.TXN.CODE not found RE.TXN.CODE not found|T24003248"
    - "READ FAILED FILE ER.PARAMETER REC SYSTEM READ FAILED FILE ER.PARAMETER REC SYSTEM|T24003249"
    - "REASON CAN ONLY BE USED WITH DELETE REASON CAN ONLY BE USED WITH DELETE|T24003250"
    - "REASON EVAL MANDATORY REASON EVAL MANDATORY|T24003251"
    - "Rebate Amort type cannot be defined for periodic Charge Rebate Amort type cannot
    be defined for periodic Charge|T24003252"
    - "REBUILD = NO AND NO PRODUCT.CATEGORY REBUILD = NO AND NO PRODUCT.CATEGORY|T24003253"
    - "REC balance can not be captured REC balance can not be captured|T24003254"
    - "REC Balance type allowed only for FIXED type interest REC Balance type allowed
    only for FIXED type interest|T24003255"
    - "REC cannot be source balance of any property. It is a Info. entry. REC cannot
    be source balance of any property. It is a Info. entry.|T24003256"
    - "REC CORR BANK CUSTOMER MISSING REC CORR BANK CUSTOMER MISSING|T24003257"
    - "RECALCULATION AS TERM NOT ALLOWED IN CALL CONTRACT RECALCULATION AS TERM NOT
    ALLOWED IN CALL CONTRACT|T24003258"
    - "Recalculation of Term Not Allowed for Forward dated Recalculation of Term Not
    Allowed for Forward dated|T24003259"
    - "RECEIVER BANK CUSTOMER MISSING RECEIVER BANK CUSTOMER MISSING|********0"
    - "RECEIVER BANK REQUIRED RECEIVER BANK REQUIRED|********1"
    - "RECORD DESCRIPTION MANDATORY RECORD DESCRIPTION MANDATORY|T24003273"
    - "RECORD DOES NOT EXIST RECORD DOES NOT EXIST|T24003274"
    - "RECORD EXISTS ON ACCOUNT$NAU FILE. CANNOT AUTHORISE. RECORD EXISTS ON ACCOUNT$NAU
    FILE. CANNOT AUTHORISE.|T24003275"
    - "RECORD IN HOLD-STATUS RECORD IN HOLD-STATUS|T24003276"
    - "RECORD INPUTTABLE ONLY BY & RECORD INPUTTABLE ONLY BY &|T24003277"
    - "Sorry, but this Customer is not eligible for more than 1 Arrangement in this
    Product. Sorry, but this Customer is not eligible for more than 1 Arrangement
    in this Product.|T24003466"
    - "Sorry, but this is not a valid Dormancy status for this Arrangement. Sorry,
    but this is not a valid Dormancy status for this Arrangement.|T24003467"
    - "Sorry, but this is not a valid service group for this arrangement Sorry, but
    this is not a valid service group for this arrangement|T24003468"
    - "Sorry, but this is not a valid transaction initiation type Sorry, but this is
    not a valid transaction initiation type|T24003469"
    - "Sorry, link account & product line & is different from arrangement product line.
    Sorry, link account & product line & is different from arrangement product line.|T24003471"
    - "Source Property type is mandatory to Input Source Property type is mandatory
    to Input|T24003476"
    - "Source Type & Not Allowed For & Source Type & Not Allowed For &|T24003477"
    - "SP.PB.CONS.MAX.CANNOT.BE.BLANK SP.PB.CONS.MAX.CANNOT.BE.BLANK|T24003478"
    - "Special Characters not allowed Special Characters not allowed|T24003479"
    - "Special Income Category allowed only for Interest Special Income Category allowed
    only for Interest|T24003480"
    - "SPECIAL STATEMENT DUE DATE PRIOR TO TODAY SPECIAL STATEMENT DUE DATE PRIOR TO
    TODAY|T24003481"
    - "Specified Security is blocked for access. Contact Bank (E-156440) Specified
    Security is blocked for access. Contact Bank (E-156440)|T24003483"
    - "Specified Security is blocked for access. Contact Bank Specified Security is
    blocked for access. Contact Bank|T24003482"
    - "SPECIFY LCY OR FCY FREQUENCY SPECIFY LCY OR FCY FREQUENCY|T24003484"
    - "Specify the Category Range Specify the Category Range|T24003485"
    - "Start date must be greater than the last capitalisation date Start date must
    be greater than the last capitalisation date|T24003494"
    - "START DATE MUST BE LESS THAN END DATE START DATE MUST BE LESS THAN END DATE|T24003495"
    - "START DATE SHOULD BE GREATER/EQUAL TO TODAY START DATE SHOULD BE GREATER/EQUAL
    TO TODAY|T24003496"
    - "START DATE SHOULD BE LESS THAN PAYMENT END DATE START DATE SHOULD BE LESS THAN
    PAYMENT END DATE|T24003497"
    - "START PERIOD SHOULD BE LESSER THAN END PERIOD START PERIOD SHOULD BE LESSER
    THAN END PERIOD|T24003498"
    - "START WITH PREFIX SW- START WITH PREFIX SW-|T24003499"
    - "START WITH PREFIX TX- START WITH PREFIX TX-|T24003500"
    - "START.DATE > END.DATE START.DATE > END.DATE|T24003501"
    - "START.LINE MUST BE NUMERIC AFTER + START.LINE MUST BE NUMERIC AFTER +|T24003502"
    - "START.RANGE CANNOT EXCEED END.RANGE START.RANGE CANNOT EXCEED END.RANGE|T24003503"
    - "START.RANGE IS NULL, NO INPUT EXPECTED START.RANGE IS NULL, NO INPUT EXPECTED|T24003504"
    - "START.RANGE NOT IN FILE, RANGE EXPECTED START.RANGE NOT IN FILE, RANGE EXPECTED|T24003505"
    - "Statement date < Enquiry Start Date Statement date < Enquiry Start Date|T24003506"
    - "Statement date > Enquiry End Date Statement date > Enquiry End Date|T24003507"
    - "Statement Entry ID is mandatory Statement Entry ID is mandatory|T24003508"
    - "Statement Name can not be left blank Statement Name can not be left blank|T24003509"
    - "STATEMENT.FREQU.2 MISSING STATEMENT.FREQU.2 MISSING|T24003510"
    - "Status can be specified only if Period is specified Status can be specified
    only if Period is specified|T24003511"
    - "Status can only be PAID when the request is a BOOK Status can only be PAID when
    the request is a BOOK|T24003512"
    - "STATUS CANNOT BE CHANGED STATUS CANNOT BE CHANGED|T24003513"
    - "STATUS MUST BE CLAIMED.ITEM STATUS MUST BE CLAIMED.ITEM|T24003514"
    - "STATUS MUST BE RETURNED.ITEM STATUS MUST BE RETURNED.ITEM|T24003515"
    - "STATUS OR PRIORITY MUST BE ENTERED STATUS OR PRIORITY MUST BE ENTERED|T24003516"
    - "Stmt entries must either be all CR or all DR entries Stmt entries must either
    be all CR or all DR entries|T24003517"
    - "Stmt entries must either be all DR or all CR entries Stmt entries must either
    be all DR or all CR entries|T24003518"
    - "STMT ENTRY MUST BE FOR THE SAME ACCOUNT STMT ENTRY MUST BE FOR THE SAME ACCOUNT|T24003519"
    - "STMT NARR FORMAT FILE NAME MUST EQ STMT ENTRY SYS ID EQ STMT NARR FORMAT FILE
    NAME MUST EQ STMT ENTRY SYS ID EQ|T24003520"
    - "STMT.ENTRY FILE, RECORD ID & MISSING STMT.ENTRY FILE, RECORD ID & MISSING|T24003522"
    - "STMT.NARR.FORMAT FILE NAME = & STMT.NARR.FORMAT FILE NAME = &|T24003523"
    - "STO.DAILY.RETRY record missing STO.DAILY.RETRY record missing|T24003524"
    - "STOCK ISSUED - NO REVERSAL ALLOWED STOCK ISSUED - NO REVERSAL ALLOWED|T24003525"
    - "STOCK NUMBER NOT AVAILABLE IN STOCK REGISTER STOCK NUMBER NOT AVAILABLE IN STOCK
    REGISTER|T24003526"
    - "STRUCT.COUNT Should be greater than Zero STRUCT.COUNT Should be greater than
    Zero|T24003527"
    - "Sub Accounts are still linked Sub Accounts are still linked|T24003528"
    - "SUB GROUP CANNOT BE A MAIN GROUP SUB GROUP CANNOT BE A MAIN GROUP|T24003529"
    - "SUB GROUP DEFINED FOR ANOTHER GROUP SUB GROUP DEFINED FOR ANOTHER GROUP|T24003530"
    - "SUB GROUP LIMITS ARE NOT ALLOWED SUB GROUP LIMITS ARE NOT ALLOWED|T24003531"
    - "Sub Product To Should be greater than Sub Product From Sub Product To Should
    be greater than Sub Product From|T24003532"
    - "SUB VALUE NOT ALLOWED WITH CUSTOMER NUMBER SUB VALUE NOT ALLOWED WITH CUSTOMER
    NUMBER|T24003533"
    - "SUB VALUE NOT ALLOWED WITH SWIFT ADDRESS SUB VALUE NOT ALLOWED WITH SWIFT ADDRESS|T24003534"
    - "Sub value set error Sub value set error|T24003535"
    - "Sub value set missing Sub value set missing|T24003536"
    - "SUB.ASSET.TYPE ID IS INVALID SUB.ASSET.TYPE ID IS INVALID|T24003537"
    - "SUBROUTINE NOT COMPILED OR DOES NOT EXIST SUBROUTINE NOT COMPILED OR DOES NOT
    EXIST|T24003538"
    - "Suffix should be identical Suffix should be identical|T24003539"
    - "Sum of defined percentage is GT 100 Sum of defined percentage is GT 100%|T24003540"
    - "suspend amount allowed arrangemetn is suspended suspend amount allowed arrangemetn
    is suspended|T24003548"
    - "Suspend amount greater than bill amount Suspend amount greater than bill amount|T24003549"
    - "Suspend amount greater than property amount Suspend amount greater than property
    amount|T24003550"
    - "Suspend fields only allow when arrangement is suspended Suspend fields only
    allow when arrangement is suspended|*********"
    - "Suspended amount not expected for bill & for property & Suspended amount not
    expected for bill & for property &|*********"
    - "Suspended Balance are not allowed. Suspended Balance are not allowed.|*********"
    - "SWEEP TYPE CASHFLOW NOT ALLOWED SWEEP TYPE CASHFLOW NOT ALLOWED|*********"
    - "SWIFT HARDWARE MANDATORY SWIFT HARDWARE MANDATORY|*********"
    - "Swift License Not Installed Swift License Not Installed|*********"
    - "SWIFT line handler closed OK SWIFT line handler closed OK|*********"
    - "SWIFT LINECOMM MANDATORY SWIFT LINECOMM MANDATORY|*********"
    - "SWIFT NETWORK MANDATORY SWIFT NETWORK MANDATORY|*********"
    - "SWIFT PROTOCOL MANDATORY SWIFT PROTOCOL MANDATORY|*********"
    - "SWIFT Rule Book 2017 changes SWIFT Rule Book 2017 changes|*********"
    - "SWIFT Rule Book 2017 SWIFT Rule Book 2017|*********"
    - "SWIFT Rule Book or Restricted Currency value should be present SWIFT Rule Book
    or Restricted Currency value should be present|*********"
    - "SWIFT Rule Book Value should be present SWIFT Rule Book Value should be present|*********"
    - "SYS.CODE MUST BE PRESENT SYS.CODE MUST BE PRESENT|*********"
    - "System created balance type not allowed System created balance type not allowed|*********"
    - "System defined balance prefix & is not allowed System defined balance prefix
    & is not allowed|*********"
    - "ROUTINE NOT CATALOGED ROUTINE NOT CATALOGED|*********"
    - "ROUTINE NOT CATALOGUED ROUTINE NOT CATALOGUED|T24003357"
    - "ROUTINE SHOULD BE PREFIXED WITH @ ROUTINE SHOULD BE PREFIXED WITH @|T24003359"
    - "Rule Book Already Installed Rule Book Already Installed|T24003360"
    - "Rule currency not available in product currency Rule currency not available
    in product currency|T24003361"
    - "Rule is missing for Customer Role Rule is missing for Customer Role|T24003362"
    - "SAME AS ACCOUNT BEING CLOSED SAME AS ACCOUNT BEING CLOSED|T24003363"
    - "Same customer of Account Same customer of Account|T24003364"
    - "Same day Archive count is mandatory when same day Retain count is present Same
    day Archive count is mandatory when same day Retain count is present|T24003365"
    - "Same day Retain count is mandatory when same day Archive count is present Same
    day Retain count is mandatory when same day Archive count is present|T24003366"
    - "SAME FUNDS TYPE SAME FUNDS TYPE|T24003367"
    - "Same property cannot have different auto settle option Same property cannot
    have different auto settle option|T24003368"
    - "SC NOT DEFINED ON COMPANY RECORD SC NOT DEFINED ON COMPANY RECORD|T24003369"
    - "Schedule Amount on & exceeds outstanding amount by & Schedule Amount on & exceeds
    outstanding amount by &|T24003370"
    - "SECURITY NOT FOUND VIA ISIN CODE SECURITY NOT FOUND VIA ISIN CODE|T24003371"
    - "SECURITY VIOLATION SECURITY VIOLATION|T24003372"
    - "SETTLEMENT ACCOUNT IS MANDATORY SETTLEMENT ACCOUNT IS MANDATORY|T24003402"
    - "SETTLEMENT ACCOUNT MISSING SETTLEMENT ACCOUNT MISSING|T24003403"
    - "settlement account must be master account settlement account must be master
    account|T24003404"
    - "Sum of Tax Percentages Must be 100 Sum of Tax Percentages Must be 100%|T24003544"
    - "Suppress Accrual not allowed for Fixed type of interest Suppress Accrual not
    allowed for Fixed type of interest|T24003545"
    - "SUROUTINE IS NOT OF TYPE S SUROUTINE IS NOT OF TYPE S|T24003546"
    - "SUSP ACCT FOR CLASS OFFSPINT MISSING SUSP ACCT FOR CLASS OFFSPINT MISSING|T24003547"
    - "SYSTEM ID - FT NOT ALLOWED SYSTEM ID - FT NOT ALLOWED|T24003568"
    - "SYSTEM ID - MANDATORY FIELD SYSTEM ID - MANDATORY FIELD|T24003569"
    - "SYSTEM ID is allowed only from MASTER company SYSTEM ID is allowed only from
    MASTER company|T24003570"
    - "System Maintained Should be set System Maintained Should be set|T24003572"
    - "SYSTEM NOT ALLOWED SYSTEM NOT ALLOWED|T24003573"
    - "SYSTEM NOT SET UP FOR POSITION ACCOUNTING SYSTEM NOT SET UP FOR POSITION ACCOUNTING|T24003574"
    - "System Record not allowed System Record not allowed|T24003575"
    - "SYSTEM STATUS RECORD MISSING SYSTEM STATUS RECORD MISSING|T24003576"
    - "TERM mandatory at designer stage TERM mandatory at designer stage|T24003594"
    - "TERM MANDATORY TERM MANDATORY|T24003593"
    - "Term Tolerance not allowed for the CalcType & Term Tolerance not allowed for
    the CalcType &|T24003595"
    - "TEXT MISSING TEXT MISSING|T24003596"
    - "TEXT.DATA INPUT IS IN THE WRONG FORMAT - SEE HELPTEXT TEXT.DATA INPUT IS IN
    THE WRONG FORMAT - SEE HELPTEXT|T24003597"
    - "The Box & is not in AVAILABLE status but in & The Box & is not in AVAILABLE
    status but in &|T24003598"
    - "The cashflow is not defined in the Activity class. The cashflow is not defined
    in the Activity class.|T24003599"
    - "The Class type & is not setup in AA.CLASS.TYPE The Class type & is not setup
    in AA.CLASS.TYPE|T24003600"
    - "The Product Code is not Valid for this Class Type The Product Code is not Valid
    for this Class Type|T24003601"
    - "The Reference is Mandatory for Definition manager The Reference is Mandatory
    for Definition manager|T24003602"
    - "The T24 Product & is not installed. The T24 Product & is not installed.|T24003603"
    - "There is No Balance to Adjust. There is No Balance to Adjust.|T24003604"
    - "There is No Balance to Write Off There is No Balance to Write Off|T24003605"
    - "There is No Bill or Balance to Adjust There is No Bill or Balance to Adjust|T24003606"
    - "There is no Bill or Balance to Write Off There is no Bill or Balance to Write
    Off|T24003607"
    - "There is No Bill to Adjust. There is No Bill to Adjust.|T24003608"
    - "There is No Bill to Write Off There is No Bill to Write Off|T24003609"
    - "THERE IS NO CLEARING CARRIER THERE IS NO CLEARING CARRIER|T24003610"
    - "There must be an Account or a Customer number entered There must be an Account
    or a Customer number entered|T24003611"
    - "This account cannot be used for this transaction This account cannot be used
    for this transaction|T24003612"
    - "This account cannot be used for this transaction(E-168107) This account cannot
    be used for this transaction(E-168107)|T24003613"
    - "THIS ACCOUNT IS ALREADY IN THIS ACCOUNT LINK THIS ACCOUNT IS ALREADY IN THIS
    ACCOUNT LINK|T24003614"
    - "This account is set for Closure This account is set for Closure|T24003615"
    - "THIS CCY HAS ALREADY BEEN DEFINED THIS CCY HAS ALREADY BEEN DEFINED|T24003616"
    - "This field should contain a charge This field should contain a charge|T24003617"
    - "This function must be run under GLOBUS client This function must be run under
    GLOBUS client|T24003618"
    - "This is a Master Account of & This is a Master Account of &|T24003619"
    - "THIS IS A PL CATEGORY THIS IS A PL CATEGORY|T24003620"
    - "This is an AUTO.PAY.ACCT of & This is an AUTO.PAY.ACCT of &|T24003621"
    - "THIS ITEM IS NOT AVAILABLE TO CHANGE THIS ITEM IS NOT AVAILABLE TO CHANGE|T24003622"
    - "THIS MODULE OBSELETE AT RELEASE 11.1.0 THIS MODULE OBSELETE AT RELEASE 11.1.0|T24003623"
    - "This Role cannot have a Tax Liability This Role cannot have a Tax Liability|T24003624"
    - "This statement entry has already been masked This statement entry has already
    been masked|T24003625"
    - "This statement entry has already been unmasked This statement entry has already
    been unmasked|*********"
    - "This stmt id has already been captured This stmt id has already been captured|*********"
    - "This swift tag does not exist in & This swift tag does not exist in &|*********"
    - "Thresholds Must Be In Ascending Order Thresholds Must Be In Ascending Order|*********"
    - "Tier min charge greater than tier max charge Tier min charge greater than tier
    max charge|*********"
    - "TIERS INVALID FOR SINGLE TYPE TIERS INVALID FOR SINGLE TYPE|*********"
    - "To Handle Component Dependency in AC_ExpectedReceipts To Handle Component Dependency
    in AC_ExpectedReceipts|*********"
    - "TO.CATEGORY MUST BE DEFINED TO.CATEGORY MUST BE DEFINED|*********"
    - "TO.CATEGORY NOT NUMERIC TO.CATEGORY NOT NUMERIC|*********"
    - "Today cleared balances were not available Today cleared balances were not available|*********"
    - "Tol Percent/Amt is mandatory when tol action is set Tol Percent/Amt is mandatory
    when tol action is set|*********"
    - "Tolerance Action Mandatory When tol  or Amt is set Tolerance Action Mandatory
    When tol % or Amt is set|*********"
    - "TOO FEW CHARACTERS TOO FEW CHARACTERS|*********"
    - "TOO MANY CHARACTERS TOO MANY CHARACTERS|*********"
    - "TOO MANY DECIMALS TOO MANY DECIMALS|*********"
    - "TOO MANY DIFFERENT CURRENCIES TOO MANY DIFFERENT CURRENCIES|*********"
    - "TOO MANY INTEGERS TOO MANY INTEGERS|*********"
    - "TOO MANY MNEMONIC CHAR. TOO MANY MNEMONIC CHAR.|*********"
    - "TOTAL DISBURSEMENT AMOUNT GREATER TOTAL DISBURSEMENT AMOUNT GREATER|T24003646"
    - "TOTAL DISBURSEMENT PERCENTAGE SHOULD BE 100 TOTAL DISBURSEMENT PERCENTAGE SHOULD
    BE 100|T24003647"
    - "Total group ratio > 100 Total group ratio > 100|T24003648"
    - "TOTAL INVALID FOR TEXT TOTAL INVALID FOR TEXT|T24003649"
    - "TOTAL TIER.PERCENT SHOULD BE 100 TOTAL TIER.PERCENT SHOULD BE 100|T24003650"
    - "Tracking Arr link not allowed for Agent Act charge Tracking Arr link not allowed
    for Agent Act charge|T24003651"
    - "Trade date should not be greater than Effective date. Trade date should not
    be greater than Effective date.|T24003654"
    - "TRAILER ALREADY DEFINED TRAILER ALREADY DEFINED|T24003656"
    - "Transfer not allowed between diff stationaries Transfer not allowed between
    diff stationaries|T24003671"
    - "TRANSLATION NOT ALLOWED TRANSLATION NOT ALLOWED|T24003672"
    - "Treasury rate not numeric Treasury rate not numeric|T24003673"
    - "Txn Code already used in CHQ.COL.TXN Txn Code already used in CHQ.COL.TXN|T24003674"
    - "Txn Code already used in CHQ.DEP.TXN Txn Code already used in CHQ.DEP.TXN|T24003675"
    - "Txn Code already used in CHQ.RET.TXN Txn Code already used in CHQ.RET.TXN|T24003676"
    - "TXN CODE MUST BE CREDIT TXN CODE MUST BE CREDIT|T24003677"
    - "TXN CODE MUST BE DEBIT TXN CODE MUST BE DEBIT|T24003678"
    - "TXN CODES INVALID WHEN ALL TXN CODES INVALID WHEN ALL|T24003679"
    - "Txn does not exist in applicn & Txn does not exist in applicn &|T24003680"
    - "SHOULD BELONG TO INTEREST CLASS SHOULD BELONG TO INTEREST CLASS|T24003441"
    - "Should define either TOL.CCY or PAY.TOLERANCE Should define either TOL.CCY or
    PAY.TOLERANCE|T24003442"
    - "Should hold a Value, when start and end category range is present Should hold
    a Value, when start and end category range is present|T24003443"
    - "Should input any one of the TierAmount ,TierCount and tierTerm fields Should
    input any one of the TierAmount ,TierCount and tierTerm fields|T24003444"
    - "Should match with PRICING.RULES definition Should match with PRICING.RULES definition|T24003445"
    - "Sorry, but the Arrangement is not in any Dormancy status to be Reset Sorry,
    but the Arrangement is not in any Dormancy status to be Reset|T24003464"
    - "Sorry, but this Customer is not eligible for more than 1 Arrangement in this
    Product Group. Sorry, but this Customer is not eligible for more than 1 Arrangement
    in this Product Group.|T24003465"
    - "Sorry, Reserved for future use Sorry, Reserved for future use|T24003472"
    - "SORT CODE MANDATORY SORT CODE MANDATORY|T24003473"
    - "SORT CODE NOT ALLOWED SORT CODE NOT ALLOWED|T24003474"
    - "Source Field name is mandatory input Source Field name is mandatory input|T24003475"
    - "STACHEM NOT USED STACHEM NOT USED|T24003486"
    - "STAGE CANNOT BE NULL STAGE CANNOT BE NULL|T24003487"
    - "START AND END DATES DO NOT MATCH START AND END DATES DO NOT MATCH|T24003488"
    - "Start date cannot be entred for matured contract Start date cannot be entred
    for matured contract|T24003489"
    - "Start date cannot be less than arrangement start date Start date cannot be less
    than arrangement start date|T24003490"
    - "START DATE IS GREATER THAN END DATE START DATE IS GREATER THAN END DATE|T24003491"
    - "Start date is greater than End date. Please rectify. Start date is greater than
    End date. Please rectify.|T24003493"
    - "STMT.ENTRY FILE, ID = & & STMT.ENTRY FILE, ID = & &|T24003521"
    - "Used prop class and field is not defined in activity Used prop class and field
    is not defined in activity|T24003710"
    - "User Id not found in EB.EXTERNAL.USER Application User Id not found in EB.EXTERNAL.USER
    Application|T24003711"
    - "UNABLE TO FIND INPUT FILE FOR INPUT FIELD & UNABLE TO FIND INPUT FILE FOR INPUT
    FIELD &|T24003687"
    - "UNABLE TO FIND INPUT FILE UNABLE TO FIND INPUT FILE|T24003686"
    - "UNABLE TO FIND INPUT RECORD UNABLE TO FIND INPUT RECORD|T24003688"
    - "Unable to Generate Sequence Number Unable to Generate Sequence Number|T24003689"
    - "UNABLE TO OPEN SPF file UNABLE TO OPEN SPF file|T24003690"
    - "UNABLE TO READ ACCOUNT FILE UNABLE TO READ ACCOUNT FILE|T24003691"
    - "UNABLE TO READ SYSTEM RECORD ON SPF UNABLE TO READ SYSTEM RECORD ON SPF|T24003692"
    - "UNAUTH FT EXIST, CANNOT CHANGE SETTLEMENT ACC UNAUTH FT EXIST, CANNOT CHANGE
    SETTLEMENT ACC|T24003693"
    - "Unauth record exist Unauth record exist|T24003694"
    - "Unauthorised entries exist for this Closing Account. Unauthorised entries exist
    for this Closing Account.|T24003695"
    - "UNAUTHORISED RECORD EXISTS UNAUTHORISED RECORD EXISTS|T24003696"
    - "UNC Balance is exceeds & of CUR balance of & UNC Balance is exceeds & of CUR
    balance of &|T24003697"
    - "UNC Balance is greater than Outstanding Balance UNC Balance is greater than
    Outstanding Balance|T24003698"
    - "UNDERLYING DEAL - UNAUTHORISED RECORD EXISTS UNDERLYING DEAL - UNAUTHORISED
    RECORD EXISTS|T24003699"
    - "UNDERLYING DEAL CHANGED UNDERLYING DEAL CHANGED|T24003700"
    - "Unexpected error. Please contact bank (E-168005) Unexpected error. Please contact
    bank (E-168005)|T24003702"
    - "Unexpected error. Please contact bank Unexpected error. Please contact bank|T24003701"
    - "UNKNOWN DATE UNKNOWN DATE|T24003703"
    - "UPDATE RTN MANDATORY IF UPDATE.MANDATE IS YES UPDATE RTN MANDATORY IF UPDATE.MANDATE
    IS YES|T24003704"
    - "Upfront Profit Not Allowed For Deposits Upfront Profit Not Allowed For Deposits|T24003705"
    - "Upload Reference and Seller ID not allowed. Upload Reference and Seller ID not
    allowed.|T24003706"
    - "Upload reference is mandatory for batching Upload reference is mandatory for
    batching|T24003707"
    - "Upload reference present rebatch not allowed Upload reference present rebatch
    not allowed|T24003708"
    - "USD Or EUR Currency Only Accept USD Or EUR Currency Only Accept|T24003709"
    - "User not allowed to make changes to this record. User not allowed to make changes
    to this record.|T24003712"
    - "USER NUMBER SHOULD BE 8 CHARACTERS LONG USER NUMBER SHOULD BE 8 CHARACTERS LONG|T24003713"
    - "V$FUNCTION NOT ALLOWED FOR THIS APPLICATION V$FUNCTION NOT ALLOWED FOR THIS
    APPLICATION|T24003714"
    - "VALID BASE DATE.MISSING VALID BASE DATE.MISSING|T24003715"
    - "Valid Interest property to be inputted Valid Interest property to be inputted|T24003716"
    - "Valid only for CHARGE property class Valid only for CHARGE property class|T24003717"
    - "VALID ONLY FOR EXISTING LIMITS VALID ONLY FOR EXISTING LIMITS|T24003718"
    - "VALID ONLY FOR PARTIAL VALID ONLY FOR PARTIAL|T24003719"
    - "VALID ONLY FOR PERIODIC.INTEREST VALID ONLY FOR PERIODIC.INTEREST|T24003720"
    - "Valid only for takeover activity Valid only for takeover activity|T24003721"
    - "Valid only if CHANGE.ACTIVITY equal to CHANGE.PRODUCT Valid only if CHANGE.ACTIVITY
    equal to CHANGE.PRODUCT|T24003722"
    - "VALIDATE AGAIN TO COMPLETE TRANSACTION VALIDATE AGAIN TO COMPLETE TRANSACTION|T24003723"
    - "Value allowed for Funds Transfer Netting only Value allowed for Funds Transfer
    Netting only|T24003724"
    - "VALUE ALLOWED ONLY FOR FT NETTING VALUE ALLOWED ONLY FOR FT NETTING|T24003725"
    - "Value Date cannot be less than the Expiry Date Value Date cannot be less than
    the Expiry Date|T24003726"
    - "VALUE DATE INVALID FOR OPERATION REBATCH VALUE DATE INVALID FOR OPERATION REBATCH|T24003727"
    - "VALUE DATE IS MANDATORY VALUE DATE IS MANDATORY|T24003728"
    - "Value does not match specfied pattern (&) Value does not match specfied pattern
    (&)|T24003729"
    - "value in CONSOLIDATION.ENTRY differs from Disbursement value in CONSOLIDATION.ENTRY
    differs from Disbursement|T24003730"
    - "Value is not allowed (&) Value is not allowed (&)|T24003731"
    - "Value must be & Value must be &|T24003732"
    - "Value must be outside defined range (&) Value must be outside defined range
    (&)|T24003733"
    - "Value not accepted if ORIG.TRANS.REF does not exist Value not accepted if ORIG.TRANS.REF
    does not exist|T24003734"
    - "Value not accepted if ORIG.TRANS.REF Exists Value not accepted if ORIG.TRANS.REF
    Exists|T24003735"
    - "Value not allowed list (&) Value not allowed list (&)|T24003736"
    - "VALUE NOT ENTERED VALUE NOT ENTERED|T24003737"
    - "Value or Exposure Date allowed only for DATE.CHANGE. Value or Exposure Date
    allowed only for DATE.CHANGE.|T24003738"
    - "Value outside negotiation range (&) Value outside negotiation range (&)|T24003739"
    - "Value PAYMENT Value PAYMENT|T24003740"
    - "VALUE SHOULD BE LIMIT VALUE SHOULD BE LIMIT|T24003741"
    - "Values matches banned pattern (&) Values matches banned pattern (&)|T24003742"
    - "VARIABLE ONLY FOR ACTUAL CALC TYPE VARIABLE ONLY FOR ACTUAL CALC TYPE|T24003743"
    - "VARIATION NOT DEFINED IN PRODUCT VARIATION NOT DEFINED IN PRODUCT|T24003744"
    - "Variations Failed and No default variation for product Variations Failed and
    No default variation for product|T24003745"
    - "Version is lesser than the latest available version Version is lesser than the
    latest available version|T24003746"
    - "VERSION MANDATORY VERSION MANDATORY|T24003747"
    - "Version Not Found Version Not Found|T24003748"
    - "Version should belongs to same Application Version should belongs to same Application|T24003749"
    - "VERSION: &|T24003750"
    - "Virtual Balance are not allowed. Virtual Balance are not allowed.|T24003751"
    - "Waiving is Allowed only for Charge property Waiving is Allowed only for Charge
    property|T24003752"
    - "We dont do Locations yet We dont do Locations yet|T24003753"
    - "WHEN SETTLE.DUES=YES, SETTLE ACTIVITY IS MANDATORY WHEN SETTLE.DUES=YES, SETTLE
    ACTIVITY IS MANDATORY|T24003754"
    - "Why program aborted Why program aborted|T24003755"
    - "Withdraw amount greater than available UNC amount & Withdraw amount greater
    than available UNC amount &|T24003757"
    - "WITHDRAW AMOUNT MUST GT INITIAL DEPOSIT WITHDRAW AMOUNT MUST GT INITIAL DEPOSIT|T24003758"
    - "Withdraw amount should not be greater than or equal to available balance Withdraw
    amount should not be greater than or equal to available balance|T24003759"
    - "Withdraw date should not go beyond maturity date Withdraw date should not go
    beyond maturity date|T24003760"
    - "WITHDRAW PERIOD MUST LE MATURITY DATE WITHDRAW PERIOD MUST LE MATURITY DATE|T24003761"
    - "WITHDRAWAL AMOUNT IS MANDATORY WITHDRAWAL AMOUNT IS MANDATORY|T24003762"
    - "WITHDRAWAL DATE IS MANDATORY WITHDRAWAL DATE IS MANDATORY|T24003763"
    - "Sum of GL Allocation Percentages Must be 100 Sum of GL Allocation Percentages
    Must be 100%|T24003541"
    - "Sum of liability customer percentage must be 100 Sum of liability customer percentage
    must be 100|T24003542"
    - "Sum of Limit Allocation Percentages Must be 100 Sum of Limit Allocation Percentages
    Must be 100%|T24003543"
    - "SYSTEM STATUS RECORD NOT SET UP SYSTEM STATUS RECORD NOT SET UP|T24003577"
    - "SYSTEM updated unauth record exists SYSTEM updated unauth record exists|T24003578"
    - "SYSTEM.STATUS RECORD MISSING FROM F.DE.PARM SYSTEM.STATUS RECORD MISSING FROM
    F.DE.PARM|T24003579"
    - "TAPE ENTRIES FOR THIS BATCH MISSING TAPE ENTRIES FOR THIS BATCH MISSING|T24003580"
    - "TAX AMT GREATER THAN CALCULATED TAX AMT TAX AMT GREATER THAN CALCULATED TAX
    AMT|T24003581"
    - "TAX CODE / TAX CONDITION MANDATORY TAX CODE / TAX CONDITION MANDATORY|T24003582"
    - "Tax inclusive allowed only for constant payment type Tax inclusive allowed only
    for constant payment type|T24003583"
    - "Tax inclusive not allowed for fixed type of interest Tax inclusive not allowed
    for fixed type of interest|T24003584"
    - "TAX OPERAND NOT PLUS TAX OPERAND NOT PLUS|T24003585"
    - "TELEX ADDRESS MUST BE NUMERIC TELEX ADDRESS MUST BE NUMERIC|T24003586"
    - "TELEX HARDWARE MUST BE DEFINED TELEX HARDWARE MUST BE DEFINED|T24003587"
    - "TELEX IS NOT AN OUTWARD NON-GENERIC CARRIER TELEX IS NOT AN OUTWARD NON-GENERIC
    CARRIER|T24003588"
    - "TELEX LINE NUMBER MUST BE PRESENT TELEX LINE NUMBER MUST BE PRESENT|T24003589"
    - "TELEX NUMBER MUST BE NUMERIC TELEX NUMBER MUST BE NUMERIC|T24003590"
    - "TELLER TRANSACTION NOT AUTHORISED FOR ONLINE CLOSURE TELLER TRANSACTION NOT
    AUTHORISED FOR ONLINE CLOSURE|T24003591"
    - "Term Amount Mandatory Term Amount Mandatory|T24003592"
    - "WITHDRAWAL PERIOD MUST BE ENTERED WITHDRAWAL PERIOD MUST BE ENTERED|T24003765"
    - "Write error for & on & Write error for & on &|T24003766"
    - "WRITE FAILED WRITE FAILED|T24003767"
    - "Write Off balance is not allowed for fixed type of interest Write Off balance
    is not allowed for fixed type of interest|T24003768"
    - "Write off not set Write off not set|T24003769"
    - "WRITE.OFF.BILL must be YES WRITE.OFF.BILL must be YES|T24003770"
    - "WRONG COMPANY FOR BATCH WRONG COMPANY FOR BATCH|T24003771"
    - "WRONG IBLC.CODES (DR/CR CCY NOT EQ) WRONG IBLC.CODES (DR/CR CCY NOT EQ)|T24003772"
    - "Wrong Input (Account = ID) Wrong Input (Account = ID)|T24003773"
    - "Y MANDATORY WHEN CHEQUE.IND Y Y MANDATORY WHEN CHEQUE.IND Y|T24003774"
    - "Trade date can be input only during New/Rollover activities Trade date can be
    input only during New/Rollover activities|T24003652"
    - "Trade date should not be greater than current system date Trade date should
    not be greater than current system date|T24003653"
    - "Trade date should not be lesser than product available date|T24003655"
    - "TRAILER DEFINITION DUPLICATED TRAILER DEFINITION DUPLICATED|T24003657"
    - "TRAILER MUST BE AFTER END.HEADER TRAILER MUST BE AFTER END.HEADER|T24003658"
    - "TRAILER MUST BE IN COL 1 TRAILER MUST BE IN COL 1|T24003659"
    - "TRAILER MUST OCCUPY OWN LINE TRAILER MUST OCCUPY OWN LINE|T24003660"
    - "TRAN REF MISSING FOR RECEIPTS TRAN REF MISSING FOR RECEIPTS|T24003661"
    - "TRANS CODE MISSING TRANS CODE MISSING|T24003662"
    - "TRANS.CODE.CHARGE = Y MISSING TRANS.CODE.CHARGE = Y MISSING|T24003663"
    - "Transaction cannot be processed. Contact bank(E-135315) Transaction cannot be
    processed. Contact bank(E-135315)|T24003664"
    - "Transaction cannot be processed. Please contact bank Transaction cannot be processed.
    Please contact bank|T24003665"
    - "Transaction code not set &, &, &, & Transaction code not set &, &, &, &|T24003666"
    - "Transaction Customer mandatory input missing Transaction Customer mandatory
    input missing|T24003667"
    - "TRANSACTION REFERENCE IS MANDATORY TRANSACTION REFERENCE IS MANDATORY|T24003668"
    - "Transfer date too forward (E-135479) Transfer date too forward (E-135479)|T24003670"
    - "Transfer date too forward Transfer date too forward|T24003669"
    - "Type Cannot Be Changed Type Cannot Be Changed|T24003681"
    - "Type input can only be EXTERNAL Type input can only be EXTERNAL|T24003682"
    - "TYPE MUST BE ALL FOR THIS ID TYPE MUST BE ALL FOR THIS ID|T24003683"
    - "TYPE MUST BE NUMERIC TYPE MUST BE NUMERIC|T24003684"
    - "Unable to determine profit rate with current setup Unable to determine profit
    rate with current setup|T24003685"
    - "WITHDRAWAL DATE MUST BE GT ARRANGEMENT START DATE WITHDRAWAL DATE MUST BE GT
    ARRANGEMENT START DATE|T24003764"
    - "Year Input is less than the latest Rulebooks year Year Input is less than
    the latest Rulebooks year|T24003775"
    - "You Can Create Only for dates upto System date & You Can Create Only for dates
    upto System date &|T24003776"
    - "You cannot use this account. Please contact bank You cannot use this account.
    Please contact bank|T24003777"
    - "You cant amend an & record using an & version. You cant amend an & record
    using an & version.|T24003778"
    - "You have exceeded your Overdraft Limit. You have exceeded your Overdraft Limit.|T24003779"
    - "YOU HAVENT KEYED IN A FCY-RECORD YOU HAVENT KEYED IN A FCY-RECORD|T24003781"
    - "You must use an & version to amend this record. You must use an & version to
    amend this record.|T24003782"
    - "Your Account Balance is less than Blocked Amount. Your Account Balance is less
    than Blocked Amount.|T24003783"
    - "Zero Ageing not allowed for multiple ageing status Zero Ageing not allowed for
    multiple ageing status|T24003784"
    - "ZERO AUTHORISATION NOT ALLOWED FOR ONLINE ACC CLOSURE ZERO AUTHORISATION NOT
    ALLOWED FOR ONLINE ACC CLOSURE|T24003785"
    - "ZERO NOT ALLOWED ZERO NOT ALLOWED|T24003786"
    - "ZERO ONLY VALID FOR TOTAL FIELDS ZERO ONLY VALID FOR TOTAL FIELDS|T24003787"
    - "Zero percentage not allowed to input Zero percentage not allowed to input|T24003788"
    - "ZERO SHOULD NOT BE PREFIXED WITH ID ZERO SHOULD NOT BE PREFIXED WITH ID|T24003789"
    - "-1 T24 Back End Error|T24 Back End Error"
    - "100 CustomerId is required|T24SDA101"
    - "132 CustomerId doesnt exists.|T24SDA103"
    - "9999 Unknown error, Please check request|T24SDA102"
    - "CLOSED THE ACCOUNT IS CLOSED|T2450001"
    - "CNA1 COPIED NOT AUTHORIZED|T2450002"
    - "CNA2 COPIED NOT AUTHORIZED REQUIRES SECOND AUTHORIZED|T2450003"
    - "FHLD FAST HOLD|T2450005"
    - "EXP EXPIRED|T2450004"
    - "INA2 INPUT REQUIRES SECOND AUTHORIZED|T2450010"
    - "DERIVED BUY RATE CANNOT BE -ve Buy Rate must be a positive value|T2401001"
    - "MUST BE LEFT BLANK Must be left blank|T2401002"
    - "SELL LESS THAN BUY SellRate must be greater than BuyRate|T2401003"
    - "Previous Cheque is in INAU status Previous Cheque is in INAU status|T2450042"
    - "HLD HOLD|T2450006"
    - "HNAU HISTORY NOT AUTHORIZED|T2450007"
    - "IAUT INPUT AUTHORIZED|T2450008"
    - "IHLD INPUT ON HOLD|T2450009"
    - "IPAU INPUT PARTIALLY AUTHORIZED|T2450013"
    - "MAT MATURED|T2450015"
    - "LIQ LIQUIDATED|T2450014"
    - "RNAO RNAO|T2450022"
    - "RNAU REVERSED NOT AUTHORIZED|T2450017"
    - "RSAO REVERSED SUPPRESS ACCEPT OVERRIDES|T2450018"
    - "SAO SUPPRESS ACCEPT OVERRIDES|T2450019"
    - "SUS SUSPENDED|T2450021"
    - "SNP START NEW PROCESS|T2450020"
    - "DEBIT ACCT CCY NOT EQ DEBIT CCY DEBIT ACCT CCY NOT EQ DEBIT CCY|T2450040"
    - "Posting Restriction for Retail Customer Posting Restriction for Retail Customer|T24007237"
    - "LIMITEXPIRES BEFORE TXN MATURITY Limit expired before completing the transaction|T24004743"
    - "NO SIGN ON NAME SUPPLIED DURING SIGN ON PROCESS NO SIGN ON NAME SUPPLIED DURING
    SIGN ON PROCESS|T24003799"
    - "Ben Customer or Address details should be entered Ben Customer or Address details
    should be entered|T24004292"
    - "CUSTOMER - MANDATORY INPUT CUSTOMER - MANDATORY INPUT|T2450043"
    - "HOLD - OVERRIDE A Hold override account|T2450044"
    - "CANNOT BE MORE THAN ONE WEEK OLD Fixing Date cannot be older than one week|T24004741"
    - "WITHDRAWL MAKES A/C BAL LESS THAN MIN BAL This transfer makes A/C balance less
    than MIN balance|T24004742"
    - "Posting Restriction 14 on Debit Account Customer Posting Restriction 14 on Debit Account Customer|T24002998"
    - "ALL FIELDS CANNOT BE NULL ALL FIELDS CANNOT BE NULL|T2450045"
    - "ACCT.BAL.LT.LOCKED ACCT.BAL.LT.LOCKED|T2450046"
    - "ACCT.BAL.LT.LOCKED.LIMIT ACCT.BAL.LT.LOCKED.LIMIT|T2450047"
    - "Invalid IBAN(CBOJ)|T24501000"
    - "FT-VAL.AMOUNT.SHOULD.NOT.BE.ZERO Amount value should not be zero|T24004744"
    - "Posting Resriction for Corporate Customer Posting Resriction for Corporate Customer|T2450038"
    - "Posting Resriction for Retail Customer Posting Resriction for Retail Customer|T2450039"
    - "LIMIT EXPIRED*OMNI LIMIT EXPIRED*OMNI|T2450034"
    - "ACCOUNT RECORD MISSING  ACCOUNT RECORD MISSING|T2450035"
    - "TOO FEW MNEMONIC CHAR TOO FEW MNEMONIC CHAR|T2450036"
    - "An error occurred, please try again An error occurred, please try again|T2450698"
    - "MISSING CUSTOMER RECORD MISSING CUSTOMER RECORD|T240232"
    - "NO ZERO-AUTH. VERSION: RIGHT RECORD MISSING|T240233"
    - "INVALID ENTRY INVALID ENTRY|T240234"
    - "MISSING  RECORD - MISSING  RECORD|T240235"
    - "MISSING MNEMONIC.CUSTOMER - RECORD MISSING MNEMONIC CUSTOMER - RECORD|T247845"
    - "Maximum Value Exceeded (9.2) Maximum Value Exceeded (9.2)|T2495487"
    - "MISSING AA.PRODUCT - RECORD MISSING AA.PRODUCT - RECORD|T2495488"
    - "NOINPUT FIELD NO INPUT FIELD|T2495489"
    - "Duplicate FT found and not allowed to book another FT Duplicate FT found and not allowed to book another FT|T24002"
    - "Below minimum value (6.45) Below minimum value (6.45)|T240032"
    - "Invalid Period Invalid Period|T24003841"
    - "National Number already exists National Number already exists|T24003842"
    - "Currency not allowed with BIC other than JO or PS Currency not allowed with
    BIC other than JO or PS|T2410222"
    - "JOD Currency not allowed with BIC other than JO or PS JOD Currency not allowed
    with BIC other than JO or PS|T2440211"
    - "TRANSACTION.TYPE:1:1=Arr Start Date (**)
    after value date (**) for A/c ** (*)|T2445000"
    - "POSSIBLE DUPLICATE CONTRACT POSSIBLE DUPLICATE CONTRACT|T24444"
    - "NULL Not Update Mobile Number on T24|T2490"
    - "LOCAL CREDIT AMOUNT MUST BE POSITIVE Local Credit Amount Must Be Positive|T2499879"
    - "TRF AMOUNT MUST BE INPUT IN #6 OR #14 TRF AMOUNT MUST BE INPUT IN #6 OR #14|T2499889"
    - "END DATE LT START DATE END DATE LT START DATE|T2499869"
    - "Withdraw amount greater than available UNC amount 0 Withdraw amount greater
    than available UNC amount 0|T2499769"
    - "DEFAULT ERROR T24 Technical Error|T24 Technical Error"










