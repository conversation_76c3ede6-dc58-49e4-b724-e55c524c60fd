# Server Configuration
server:
  port: 8080

# CBOJ Custom Properties
# OAuth2 Security Configuration
cboj:
  security:
    oauth2:
      enabled: ${oauth2.enabled}

spring:

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: https://sso-stg-cboj.capitalbank.jo:8443/auth/realms/cboj-mai


swagger:
  server-url: http://localhost:8081/


http-lib:
  tls:
    keystore:
      path: ""
      password: ""

