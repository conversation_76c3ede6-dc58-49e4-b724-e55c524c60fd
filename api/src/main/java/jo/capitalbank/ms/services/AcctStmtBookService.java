package jo.capitalbank.ms.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jo.capitalbank.ms.config.AcctStmtBackendProperties;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.common.services.HttpClientService;
import jo.capitalbank.ms.mapper.AccountStatementMapperFcc;
import jo.capitalbank.ms.models.responses.fccAccountStatement.AcctStmtBackendResponse;
import jo.capitalbank.ms.utils.AcctStmtBookRequestsHelper;
import jo.capitalbank.ms.utils.AcctStmtBookResponseHelper;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class AcctStmtBookService {

    @Autowired
    private  HttpClientService httpClientService;
    @Autowired
    private  AcctStmtBackendProperties backendProperties;
    @Autowired
    private AcctStmtBookRequestsHelper requestsHelper;

    @Autowired
    private AcctStmtBookResponseHelper acctStmtBookResponseHelper;

    private String backendUrl;
    private List<MediaType> acceptHeader;

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AccountStatementMapperFcc accountStatementMapperFcc;


    @PostConstruct
    private void initValues() {
        backendUrl = backendProperties.getProtocol()+"://"+backendProperties.getHost() + ":" + backendProperties.getPort() + backendProperties.getPath();
        acceptHeader = Arrays.asList(MediaType.APPLICATION_XML, MediaType.TEXT_XML);
    }

    public ResponseEntity<?> getAccountStatementBook(String id, String fromDate, String toDate) {
        log.debug("Requesting account statement book for id={}, fromDate={}, toDate={}", id, fromDate, toDate);

        final String soapXmlRequestBody = requestsHelper.generateAcctStmtBookXml(id, fromDate, toDate);
        final HttpHeaders headers = createHeaders();

        ResponseEntity<?> backendResponse = httpClientService.sendRequest(
                backendUrl, HttpMethod.POST, headers, new HashMap<>(), soapXmlRequestBody, String.class
        );


        var stringRes= ExctractXmlString(backendResponse);



        var response=acctStmtBookResponseHelper.parse(stringRes);

        var accountStatementResponseDto = accountStatementMapperFcc.map(response);

        // TODO: Parse response here with JAXB or custom parser
        CommonResponse commonResponse = CommonResponse.builder()
                .response(accountStatementResponseDto) // raw XML for now
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .build();

        return ResponseEntity.ok(commonResponse);
    }

    private static String ExctractXmlString(ResponseEntity<?> sironResponse) {
        ResponseEntity<String> sironResponseEntity = (ResponseEntity<String>) sironResponse.getBody();
        String sironXmlResponseString = sironResponseEntity.getBody().toString();
        return sironXmlResponseString;
    }


    private HttpHeaders createHeaders() {
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_XML);
        headers.setAccept(acceptHeader);
        headers.add("SOAPAction", backendProperties.getSoapAction());
        return headers;
    }
}
