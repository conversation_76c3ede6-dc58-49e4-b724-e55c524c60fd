package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.AccountCardCreationResponse;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.mapper.UpdateNicknameMapper;
import jo.capitalbank.ms.models.requests.AccountCardRequest;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;

@Service
@Slf4j
public class AccountCardCreationService {


    @Autowired
    T24TransactionService t24TransactionService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    T24ExceptionHandlerService t24ExceptionHandlerService;


    @Value("${t24Interfaces.accountCardCreation.operation}")
    private String operation;

    @Value("${t24Interfaces.accountCardCreation.version}")
    private String version;


    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";

    private final static String ARRANGEMENT = "NEW";
    private final static String ACTIVITY = "ACCOUNTS-NEW-ARRANGEMENT";
    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    public ResponseEntity<?> createCardAccount(AccountCardRequest request, String customerId, String channelId, String correlationId) throws JMSException {
        var transactionOptions = new TransactionRequest.TransactionOptions();
        transactionOptions.setFunction(FUNCTION);
        transactionOptions.setGtsControl(GTS_CONTROL);
        transactionOptions.setVersion(version);
        transactionOptions.setProcessOrValidate("");
        transactionOptions.setGtsControl("");
        transactionOptions.setNoOfAuth("");


        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field("ARRANGEMENT", ARRANGEMENT));
        t24Fields.add(new T24Field("ACTIVITY", ACTIVITY));
        t24Fields.add(new T24Field("CUSTOMER", customerId));
        if (request.getProductType() != null)
            t24Fields.add(new T24Field("PRODUCT", request.getProductType()));
        if (request.getCurrency() != null)
            t24Fields.add(new T24Field("CURRENCY", request.getCurrency()));
        if (request.getAccountType() != null)
            t24Fields.add(new T24Field("ACCOUNT.TYPE", request.getAccountType()));
        if (request.getPurposeFacility() != null)
            t24Fields.add(new T24Field("PURPOSE.FACILITY", request.getPurposeFacility()));
        if (request.getLimitRefernece() != null)
            t24Fields.add(new T24Field("LIMIT.REFERENCE:1:1", request.getLimitRefernece()));
        if (request.getLimitSerial() != null)
            t24Fields.add(new T24Field("LIMIT.SERIAL", request.getLimitSerial()));
        t24Fields.add(new T24Field("L.CHANNEL.ID", channelId));
        t24Fields.add(new T24Field("L.CHANNEL.REF", correlationId));


        var transactionRequest = TransactionRequest.builder()
                .operation(operation)
                .transactionId("")
                .options(transactionOptions)
                .userInformation(new UserInformation("", "", request.getAccountCompanyCode() != null ? request.getAccountCompanyCode() : ""))
                .t24Fields(t24Fields)
                .build();

        var t24Response = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);

        return accountCardCreationResponse(t24Response);


    }

    private ResponseEntity<?> accountCardCreationResponse(T24GenericResponse t24GenericResponse) {
        t24ExceptionHandlerService.checkT24Exception(t24GenericResponse);
        var accountCardCreationResponse= AccountCardCreationResponse.builder()
                .accountNumber(t24GenericResponse.getHeaders().stream().filter(header -> header.getName().equals("trxRef")).findFirst().get().getValue())
                .build();

        var commonResponse= CommonResponse.builder()
                .response(accountCardCreationResponse)
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }


}
