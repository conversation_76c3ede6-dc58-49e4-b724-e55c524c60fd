package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.AccountPostingRestrictionResponse;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.PostingRestrictionMapper;
import jo.capitalbank.ms.models.responses.AccountPostingRestricionT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Stream;

@Service
@Slf4j
public class AccountsPostingRestrictionService {


    @Autowired
    T24InquiryService t24InquiryService;

    @Autowired
    ObjectMapper objectMapper;

    @Value("${t24Interfaces.postingRestriction.version}")
    String postingRestrictionVersion;

    @Value("${t24Interfaces.accountDetailsBpmchl.version}")
    String postingRestrictionVersionBpmchl;


    @Autowired
    PostingRestrictionMapper postingRestrictionMapper;

    public ResponseEntity<?> getAccountPostingRestrictions(String accountNumber)  {

        var enquiryRequest = new EnquiryRequest();

        enquiryRequest.setEnquiry(postingRestrictionVersion);
        enquiryRequest.setOptions("");


        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setEnquiryId("@ID:EQ=" + accountNumber);


            var t24Response = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);


        log.info("T24 Response After Mapping: {}", t24Response);

        return prepareAccounPostingRestrictionResponse(t24Response.getBody());


    }

    private ResponseEntity<?> prepareAccounPostingRestrictionResponse(List<T24GenericResponse.Record> t24Fields) {


        var listofFields = new ArrayList<HashMap<String, String>>(t24Fields.size());
        log.info("T24 Response After Mapping: {}", t24Fields.size());
        t24Fields.forEach(field -> {
            var mapResponse = new HashMap<String, String>();
            field.getFields().forEach(f -> {

                mapResponse.put(f.getT24field().getName().split(":")[0], f.getT24field().getValue());
            });
            listofFields.add(mapResponse);

        });

        return getAccountPostingRestrictionMapper(listofFields);


    }

    private ResponseEntity<?> getAccountPostingRestrictionMapper(List<HashMap<String, String>> t24FieldsList) {

        var accountPostingRestrictionT24 = t24FieldsList.stream()
                .filter(map -> map != null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountPostingRestricionT24Fields.PostingRestriction.class))
                .filter(posting -> posting != null && hasNonNullField(posting))
                .toList();


        var mapperResponse = postingRestrictionMapper.mapToAccounts(accountPostingRestrictionT24);

        var status = new CommonResponse.Status(true, "200", "", "", "", "");
        var commonResponse = CommonResponse.builder().status(status).response(mapperResponse).build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }

    private boolean hasNonNullField(AccountPostingRestricionT24Fields.PostingRestriction posting) {
        return posting.getCode() != null ||
                posting.getDescription() != null ||
                posting.getType() != null ||
                posting.getTypeDescription() != null;
    }


}
