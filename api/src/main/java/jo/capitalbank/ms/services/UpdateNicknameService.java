package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24BusinessException;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.mapper.UpdateNicknameMapper;
import jo.capitalbank.ms.models.requests.NicknameUpdateRequest;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.models.responses.UpdateAccountNicknameT24ResponseFileds;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;

@Service
@Slf4j
public class UpdateNicknameService {


    @Autowired
    T24TransactionService t24TransactionService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    T24ExceptionHandlerService t24ExceptionHandlerService;

    @Autowired
    UpdateNicknameMapper updateNicknameMapper;


    @Value("${t24Interfaces.updateNickname.operation}")
    private String operation;

    @Value("${t24Interfaces.updateNickname.version}")
    private String version;


    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";

    private final static String NICKNAME_FIELD = "ACCT.NICK.NAME";
    private final static String SUCCESS_MESSAGE="The Operation has been Successfully Completed";


    public ResponseEntity<?> updateAccountNickname(String accountId, NicknameUpdateRequest nicknameUpdateRequest) throws JMSException {

        var transactionOptions = new TransactionRequest.TransactionOptions();
        transactionOptions.setFunction(FUNCTION);
        transactionOptions.setGtsControl(GTS_CONTROL);
        transactionOptions.setVersion(version);
        transactionOptions.setProcessOrValidate("");
        transactionOptions.setGtsControl("");
        transactionOptions.setNoOfAuth("");


        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field(NICKNAME_FIELD, nicknameUpdateRequest.getAccountNickName()));

        var transactionRequest = TransactionRequest.builder()
                .operation(operation)
                .transactionId(accountId)
                .options(transactionOptions)
                .userInformation(new UserInformation("", "", ""))
                .t24Fields(t24Fields)
                .build();

        var transactionResponse = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest ).getBody(), T24GenericResponse.class);


        return processT24Response(transactionResponse);


    }

    private ResponseEntity<?> processT24Response(T24GenericResponse t24GenericResponse) {

        //to check if T24 returned any excpetion
        t24ExceptionHandlerService.checkT24Exception(t24GenericResponse);

        var listofFields = new ArrayList<HashMap<String,String>>();
        t24GenericResponse.getBody().forEach(field -> {
            var mapResponse = new HashMap<String, String>();
            field.getFields().forEach(f -> {

                mapResponse.put(f.getT24field().getName().split(":")[0], f.getT24field().getValue());
            });
            listofFields.add(mapResponse);

        });

        var updateNickNameFieldsResponse = objectMapper.convertValue(listofFields.getFirst(), UpdateAccountNicknameT24ResponseFileds.class);
        var updateDtoResponse=updateNicknameMapper.mapToUpdateNicknameResponse(updateNickNameFieldsResponse);

        var commonResponse = CommonResponse.builder().response(updateDtoResponse)
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(updateDtoResponse)
                .build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);

    }


}
