package jo.capitalbank.ms.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountDormantMapper;
import jo.capitalbank.ms.models.responses.AccountDormantT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountDormantService {
    @Autowired
    private T24InquiryService t24InquiryService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AccountDormantMapper accountDormantMapper;
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;
    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    @Value("${t24Interfaces.dormentAccount.version}")
    private String dormentAccountVersion;


    private final static String ACCOUNT_STATUS="ACCOUNT.STATUS:EQ";
    private final static String STATUS_DATE="STATUS.DATE:EQ";
    private final static String CUSTOMER_NUMBER="CUSTOMER.NO:EQ";
    private final static String ACCOUNT_BRANCH="ACC.BRANCH:EQ";


    public ResponseEntity<?> getDormantAccount(String customerId,String statusDate,String accountBranch ) {
        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(ACCOUNT_STATUS,"DORMANT"));
        t24Fields.add(new T24Field(STATUS_DATE,statusDate));
        t24Fields.add(new T24Field(CUSTOMER_NUMBER,customerId));
        t24Fields.add(new T24Field(ACCOUNT_BRANCH,accountBranch));

        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(dormentAccountVersion);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response= objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        return accountDormantResponse(t24Response.getBody());

    }


    private ResponseEntity<?> accountDormantResponse(List<T24GenericResponse.Record> t24Fields) {


        var t24ResponseFields= t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var doramntAccountList = t24ResponseFields.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map-> objectMapper.convertValue(map, AccountDormantT24Fields.class))
                .filter(Objects::nonNull)
                .toList();

        var dtoResponse = accountDormantMapper.mappAccountDormantResponse(doramntAccountList.getFirst());

        var commonResponse= CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(dtoResponse)
                .build();
        return ResponseEntity.ok(commonResponse);

    }



}
