package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24BusinessException;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountTransactionV2Mapper;
import jo.capitalbank.ms.mapper.AccountTransactionsMapper;
import jo.capitalbank.ms.models.responses.AccountByLimitsT24Fields;
import jo.capitalbank.ms.models.responses.AccountTransactionsT24Fields;
import jo.capitalbank.ms.models.responses.AccountTransactionsV2T24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class AccountTransactionsService {


    @Autowired
    private T24InquiryService t24InquiryService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AccountTransactionsMapper accountTransactionsMapper;
    @Autowired
    private AccountTransactionV2Mapper accountTransactionV2Mapper;

    @Value("${t24Interfaces.accountTransactionsList.version}")
    private String version;
    @Value("${t24Interfaces.accountTransactionsList.version2}")
    private String version2;
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    public ResponseEntity<?> getAccountTransactions(String accountNumber, String pageNumber, String recordNumber, String fromDate, String toDate, String channelId) {


        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setEnquiry(version);
        enquiryRequest.setOptions("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setEnquiryId("");
        var numberOfTransactions = Integer.parseInt(pageNumber) * Integer.parseInt(recordNumber);
        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field("ESB.ACCT.NO" + ":EQ", accountNumber));
        t24Fields.add(new T24Field("ESB.FROM.DT" + ":EQ", fromDate));
        t24Fields.add(new T24Field("ESB.TO.DT" + ":EQ", toDate));
        t24Fields.add(new T24Field("ESB.NO.OF.TRANS" + ":EQ", String.valueOf(numberOfTransactions)));

        enquiryRequest.setT24Fields(t24Fields);

        var t24Response = t24InquiryService.processEnquiry(enquiryRequest);
        var t24GenericResponse = objectMapper.convertValue(t24Response.getBody(), T24GenericResponse.class);

        return prepareTransactionsResponse(t24GenericResponse.getBody());


    }

    public ResponseEntity<?> getAccountTransactions2(String accountNumber, int pageNumber,
                                                     int recordNumber,
                                                     String fromDate, String toDate,
                                                     String fromAmount,
                                                     String toAmount,
                                                     String transactionType,
                                                     String sortByDescending,
                                                     String channelId, String correlationId) {

        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setEnquiry(version2);
        enquiryRequest.setOptions("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setEnquiryId("");
        var numberOfTransactions = pageNumber * recordNumber;
        var t24Fields = new ArrayList<T24Field>();
        var sortByDesc = (sortByDescending != null && sortByDescending.equals("true")) ? "Y" : "";
        t24Fields.add(new T24Field("ACCTNO" + ":EQ", accountNumber));

        if (fromDate != null && !fromDate.isEmpty())
            t24Fields.add(new T24Field("FROMDATE" + ":GE", fromDate));

        if (toDate != null && !toDate.isEmpty())
            t24Fields.add(new T24Field("TODATE" + ":LE", toDate));

        if (fromAmount != null && !fromAmount.isEmpty())
            t24Fields.add(new T24Field("FROMAMOUNT" + ":GE", fromAmount));
        if (toAmount != null && !toAmount.isEmpty())
            t24Fields.add(new T24Field("TOAMOUNT" + ":LE", toAmount));
        if (transactionType != null && !transactionType.isEmpty())
            t24Fields.add(new T24Field("TXN.IND" + ":EQ", transactionType));

        t24Fields.add(new T24Field("SORTDSND" + ":EQ", sortByDesc));
        t24Fields.add(new T24Field("L.CHANNEL.ID" + ":EQ", channelId));
        t24Fields.add(new T24Field("L.CHANNEL.REF" + ":EQ", correlationId));
        t24Fields.add(new T24Field("NOOFTXNS" + ":EQ", String.valueOf(numberOfTransactions)));

        enquiryRequest.setT24Fields(t24Fields);

        var t24Response = t24InquiryService.processEnquiry(enquiryRequest);
        var t24GenericResponse = objectMapper.convertValue(t24Response.getBody(), T24GenericResponse.class);

        return prepareTransactionsResponse2(t24GenericResponse.getBody());


    }

    public ResponseEntity<?> getAccountSpecificTransaction(String accountNumber, int pageNumber,
                                                     int recordNumber,
                                                     String fromDate, String toDate,
                                                     String fromAmount,
                                                     String toAmount,
                                                     String transactionType,
                                                     String sortByDescending,
                                                     String transactionId,
                                                     String channelId, String correlationId) {

        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setEnquiry(version2);
        enquiryRequest.setOptions("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setEnquiryId("");
        var numberOfTransactions = pageNumber * recordNumber;
        var t24Fields = new ArrayList<T24Field>();
        var sortByDesc = (sortByDescending != null && sortByDescending.equals("true")) ? "Y" : "";
        t24Fields.add(new T24Field("ACCTNO" + ":EQ", accountNumber));

        if (fromDate != null && !fromDate.isEmpty())
            t24Fields.add(new T24Field("FROMDATE" + ":GE", fromDate));

        if (toDate != null && !toDate.isEmpty())
            t24Fields.add(new T24Field("TODATE" + ":LE", toDate));

        if (fromAmount != null && !fromAmount.isEmpty())
            t24Fields.add(new T24Field("FROMAMOUNT" + ":GE", fromAmount));
        if (toAmount != null && !toAmount.isEmpty())
            t24Fields.add(new T24Field("TOAMOUNT" + ":LE", toAmount));
        if (transactionType != null && !transactionType.isEmpty())
            t24Fields.add(new T24Field("TXN.IND" + ":EQ", transactionType));

        t24Fields.add(new T24Field("SORTDSND" + ":EQ", sortByDesc));
        t24Fields.add(new T24Field("L.CHANNEL.ID" + ":EQ", channelId));
        t24Fields.add(new T24Field("L.CHANNEL.REF" + ":EQ", correlationId));
        t24Fields.add(new T24Field("NOOFTXNS" + ":EQ", String.valueOf(numberOfTransactions)));
        t24Fields.add(new T24Field("TXN.IND" + ":EQ", transactionId));

        enquiryRequest.setT24Fields(t24Fields);

        var t24Response = t24InquiryService.processEnquiry(enquiryRequest);
        var t24GenericResponse = objectMapper.convertValue(t24Response.getBody(), T24GenericResponse.class);

        return prepareTransactionsResponse2(t24GenericResponse.getBody());


    }


    private ResponseEntity<?> prepareTransactionsResponse(List<T24GenericResponse.Record> t24Fields) {


        var t24ResponseFields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var accountsList = t24ResponseFields.stream()
                .filter(map -> map != null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountTransactionsT24Fields.class))
                .filter(Objects::nonNull)
                .toList();


        var accountTransactionsDto = accountTransactionsMapper.toResponse(accountsList);


        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(accountTransactionsDto)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }

    private ResponseEntity<?> prepareTransactionsResponse2(List<T24GenericResponse.Record> t24Fields) {


        var t24ResponseFields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var accountsList = t24ResponseFields.stream()
                .filter(map -> map != null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountTransactionsV2T24Fields.class))
                .filter(Objects::nonNull)
                .toList();


        var accountTransactionsDto = accountTransactionV2Mapper.toResponse(accountsList);


        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(accountTransactionsDto)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }



}
