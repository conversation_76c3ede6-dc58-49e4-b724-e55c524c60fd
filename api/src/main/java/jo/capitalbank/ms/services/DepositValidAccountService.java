package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.dto.DepositValidAccountResponse;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.BillerAccountMapper;
import jo.capitalbank.ms.mapper.DepositValidAccountMapper;
import jo.capitalbank.ms.models.responses.DepositValidAccountT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class DepositValidAccountService {


    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    @Autowired
    T24InquiryService inquiryService;

    @Autowired
    ObjectMapper objectMapper;

    @Value("${t24Interfaces.accountDepositValid.version}")
    private String version;
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;
    private final static String CUSTOMER_ID="CUSTOMER.ID:EQ";

    @Autowired
    private DepositValidAccountMapper depositValidAccountMapper;


    public ResponseEntity<?> getDepositValidAccount(String customerId){
        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(CUSTOMER_ID,customerId));
        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(version);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response = objectMapper.convertValue(inquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        return depostValidAccountResponse(t24Response.getBody());
    }

    private ResponseEntity<?> depostValidAccountResponse(List<T24GenericResponse.Record> t24Fields) {
        var t24ResponseFields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var listOfAccounts = t24ResponseFields.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, DepositValidAccountT24Fields.class))
                .filter(Objects::nonNull)
                .toList();


        var accountValidDepositDto= depositValidAccountMapper.toDepositValidAccountResponse(listOfAccounts);
        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(accountValidDepositDto)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }



}
