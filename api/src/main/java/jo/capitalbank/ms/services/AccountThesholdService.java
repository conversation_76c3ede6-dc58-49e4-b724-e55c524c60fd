package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.dto.AccountThresholdResponse;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountDormantMapper;
import jo.capitalbank.ms.mapper.AccountThresholdMapper;
import jo.capitalbank.ms.models.responses.AccountThresholdT24Response;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountThesholdService {

    @Autowired
    private T24InquiryService t24InquiryService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AccountDormantMapper accountDormantMapper;
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;
    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    @Autowired
    private AccountThresholdMapper accountThresholdMapper;

    private final static String SS_PROD_ID = "SS.PROD.ID:EQ";
    private final static String SS_CURR_ID = "SS.CURR.ID:EQ";


    @Value("${t24Interfaces.threshold.version}")
    private String version;



    public ResponseEntity<?> getAccountTheshold(String id,String currency) {

        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(SS_PROD_ID,id));
        t24Fields.add(new T24Field(SS_CURR_ID,currency));
        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(version);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        return getThresholdAmountResponse(t24Response.getBody());
    }


    private ResponseEntity<?> getThresholdAmountResponse(List<T24GenericResponse.Record> t24Fields) {

        var t24ResponseFields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var t24ResponseList = t24ResponseFields.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map-> objectMapper.convertValue(map, AccountThresholdT24Response.class))
                .filter(Objects::nonNull)
                .toList();

        var accountThesholdResponse = accountThresholdMapper.toAccountThresholdResponse(t24ResponseList);

        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null, null))
                .response(accountThesholdResponse)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);


    }


}
