package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.dto.AccountArrangementsResponse;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountArranegemtnsMapper;
import jo.capitalbank.ms.mapper.AccountByLimitMapper;
import jo.capitalbank.ms.models.responses.AccountArrangementsT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountLinkedArrangements {

    @Autowired
    private T24InquiryService t24InquiryService;

    @Autowired
    private ObjectMapper objectMapper;

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";
    @Autowired
    private AccountArranegemtnsMapper accountArrangementsMapper;

    @Value("${t24Interfaces.accountArrangements.version}")
    private String version;

    private final static String ACCOUNT_EQ="ACCOUNT:EQ";
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;


    public ResponseEntity<?> getAccountArrangements(String accountId) {

        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field(ACCOUNT_EQ, accountId));

        var enquiryRequest = EnquiryRequest.builder()
                .enquiryId("")
                .enquiry(version)
                .userInformation(new UserInformation("", "", ""))
                .operation("")
                .t24Fields(t24Fields)
                .build();


        var t24Response = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        //return new ResponseEntity<>(t24Response, HttpStatus.OK);

        return accountLinkedArrangementsResponse(t24Response.getBody());


    }


    private ResponseEntity<?> accountLinkedArrangementsResponse(List<T24GenericResponse.Record> t24Fields) {

        var t24ResponseFiels = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var accountArragementsList = t24ResponseFiels.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountArrangementsT24Fields.class))
                .filter(Objects::nonNull)
                .toList();

        var accountArrangementsResponse = accountArrangementsMapper.accountArrangementsMapper(accountArragementsList);

        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(accountArrangementsResponse)
                .build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }

}
