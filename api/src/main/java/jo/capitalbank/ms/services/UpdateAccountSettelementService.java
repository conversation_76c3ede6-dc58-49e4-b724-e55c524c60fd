package jo.capitalbank.ms.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.UpdateAccountArrangementResponse;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.mapper.AccountInternalMapper;
import jo.capitalbank.ms.mapper.UpdateAccountSettlementMapper;
import jo.capitalbank.ms.models.requests.UpdateInternalAccountRequest;
import jo.capitalbank.ms.models.requests.UpdateSettelmentAccountRequest;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class UpdateAccountSettelementService {

    @Autowired
    T24TransactionService t24TransactionService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    T24ExceptionHandlerService t24ExceptionHandlerService;

    @Autowired
    UpdateAccountSettlementMapper updateAccountSettlementMapper;

    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";


    @Value("${t24Interfaces.settlementAccount.operation}")
    private String operation;
    @Value("${t24Interfaces.settlementAccount.version}")
    private String version;


    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    public ResponseEntity<?> updateSettelemntAccount(UpdateSettelmentAccountRequest request, String channelId) throws JMSException {

        var transactionOptions = new TransactionRequest.TransactionOptions();
        transactionOptions.setFunction(FUNCTION);
        transactionOptions.setGtsControl(GTS_CONTROL);
        transactionOptions.setVersion(version);
        transactionOptions.setNoOfAuth("");
        transactionOptions.setProcessOrValidate("");

        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field("ARRANGEMENT", request.getAccountNumber()));
        t24Fields.add(new T24Field("SETTLEMENT.ACCOUNT", request.getSettlementAccount()));
        t24Fields.add(new T24Field("NEW.SETTLEMENT.ACCOUNT", request.getNewSettlementAccount()));

        var transactionRequest = TransactionRequest.builder()
                .options(transactionOptions)
                .operation(operation)
                .transactionId("")
                .userInformation(new UserInformation("", "", ""))
                .t24Fields(t24Fields)
                .build();


        var t24Response = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);


        return updateAccountResponse(t24Response);


    }

    private ResponseEntity<?> updateAccountResponse(T24GenericResponse t24GenericResponse) throws JMSException {

        t24ExceptionHandlerService.checkT24Exception(t24GenericResponse);


        var trxRef = t24GenericResponse.getHeaders().stream().filter(header -> header.getName().equals("trxRef")).findFirst().get().getValue();
        var updateAccountSettelemtnResponse = updateAccountSettlementMapper.updateInternalAccountResponse(trxRef);


        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(updateAccountSettelemtnResponse)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);

    }

}
