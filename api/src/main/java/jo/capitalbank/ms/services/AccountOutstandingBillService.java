package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountOutstandingBillsMapper;
import jo.capitalbank.ms.mapper.BillerAccountMapper;
import jo.capitalbank.ms.mapper.OutstandingBillsKofaxMapper;
import jo.capitalbank.ms.models.responses.AccountOutstandingBillsT24Response;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountOutstandingBillService {

    /**
     *
     * This service is not completed , as no channel is using it so we kept it on HOLD
     *
     */
    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    @Autowired
    T24InquiryService inquiryService;

    @Autowired
    ObjectMapper objectMapper;


    @Value("${t24Interfaces.outstandingBills.version}")
    private String outstandingBillsVersion;

    @Value("${t24Interfaces.outstandingBillsOverDrawn.version}")
    private String outstandingBillsOverDrawnVersion;

    @Value("${t24Interfaces.outstandingBillDetailsKofax.version}")
    private String outstandingBillDetailsKofaxVersion;


    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;

    private static final String RESERVED = "RESERVED.1:EQ";

    private static final String BRANCH_CODE = "BRANCH.CODE:EQ";

    private static final String ARRANGEMENT_ID = "ARRANGEMENT.ID:EQ";

    @Autowired
    private AccountOutstandingBillsMapper accountOutstandingBillsMapper;

    @Autowired
    private OutstandingBillsKofaxMapper outstandingBillsKofaxMapper;


    public ResponseEntity<?> getAccountOutstandingBills(String accountId) {

        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(RESERVED, accountId));
        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(outstandingBillsVersion);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response = objectMapper.convertValue(inquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);


        return accountOutstandingBillResponse(t24Response.getBody());
    }


    private ResponseEntity<?> accountOutstandingBillResponse(List<T24GenericResponse.Record> t24Fields){
        var t2Fields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var outstandingbillsList = t2Fields.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map-> objectMapper.convertValue(map, AccountOutstandingBillsT24Response.class))
                .filter(Objects::nonNull)
                .toList();

        var outstandingbillsResponse = accountOutstandingBillsMapper.mapAccounts(outstandingbillsList);

        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(outstandingbillsResponse)
                .build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }


    //over-drawn
    public ResponseEntity<?> getAccountOutstandingBillsOverDrawn(String accountId) {

        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(BRANCH_CODE, accountId));
        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(outstandingBillsOverDrawnVersion);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response = objectMapper.convertValue(inquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        return accountOutstandingBillResponse(t24Response.getBody());
    }


    //Kofax
    public ResponseEntity<?> getAccountOutstandingBillsKofax(String accountId) {

        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(ARRANGEMENT_ID, accountId));
        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(outstandingBillDetailsKofaxVersion);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response = objectMapper.convertValue(inquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);



        return accountOutstandingBillResponseKofax(t24Response.getBody());
    }


    private ResponseEntity<?> accountOutstandingBillResponseKofax(List<T24GenericResponse.Record> t24Fields){
        var t2Fields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var outstandingbillsList = t2Fields.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map-> objectMapper.convertValue(map, AccountOutstandingBillsT24Response.class))
                .filter(Objects::nonNull)
                .toList();


        var kofaxOutStandingResponse = outstandingBillsKofaxMapper.mapOutstandingBills(outstandingbillsList);
        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(kofaxOutStandingResponse)
                .build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }

}
