package jo.capitalbank.ms.services;

import jo.capitalbank.ms.library.common.services.HttpClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

@Slf4j
@Service
public class HttpClientServiceImpl extends HttpClientService {
    @Override
    protected ResponseEntity<?> handleConnectionException(String url, ResourceAccessException ex) {
        log.error("Timeout or Error connecting to {}", url, ex);
        return new ResponseEntity<>(HttpStatus.SERVICE_UNAVAILABLE);
    }
}
