package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.UpdateAccountArrangementResponse;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.mapper.AccountInternalMapper;
import jo.capitalbank.ms.models.requests.UpdateAccountArrangementRequest;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.IntStream;

@Service
@Slf4j
public class UpdateAccountArrangementService {

    @Autowired
    T24TransactionService t24TransactionService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    T24ExceptionHandlerService t24ExceptionHandlerService;

    @Autowired
    AccountInternalMapper accountInternalMapper;

    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    @Value("${t24Interfaces.updateAccountArrangement.version}")
    private String version;

    @Value("${t24Interfaces.updateAccountArrangement.operation}")
    private String operation;


    private final static String ARRANGEMENT = "ARRANGEMENT";
    private final static String ACTIVITY = "ACTIVITY";
    private final static String PROPERTY1 = "PROPERTY:1:1";
    private final static String EFFCTIVE_DATE = "EFFCTIVE.DATE";
    private final static String CHANNELID = "L.CHANNEL.ID";
    private final static String CHANNEL_REF = "L.CHANNEL.REF";

    public ResponseEntity<?> updateAccountArrangements(UpdateAccountArrangementRequest request, String accountId, String channelId, String correlationId) throws JMSException {


        var transactionOptions = new TransactionRequest.TransactionOptions();
        transactionOptions.setFunction(FUNCTION);
        transactionOptions.setGtsControl(GTS_CONTROL);
        transactionOptions.setVersion(version);
        transactionOptions.setProcessOrValidate("");
        transactionOptions.setGtsControl("");
        transactionOptions.setNoOfAuth("");


        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field(ARRANGEMENT, accountId));
        t24Fields.add(new T24Field(ACTIVITY, request.getActivityName()));
        t24Fields.add(new T24Field(PROPERTY1, request.getProperty()));
        if (request.getEffectiveDate() != null)
            t24Fields.add(new T24Field(EFFCTIVE_DATE, request.getEffectiveDate()));
        t24Fields.add(new T24Field(CHANNELID, channelId));
        t24Fields.add(new T24Field(CHANNEL_REF, correlationId));
        transform(request.getArrangementFields()).forEach(arrangementField -> {
            t24Fields.add(new T24Field(arrangementField.get("name"), arrangementField.get("value")));
        });
        var transactionRequest = TransactionRequest.builder().operation(operation).
                transactionId("")
                .options(transactionOptions)
                .userInformation(new UserInformation("", "", ""))
                .t24Fields(t24Fields).build();


        var t24TransactionResponse = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);


        return handleUpdateAccountArrangementResponse(t24TransactionResponse);
    }


    private List<Map<String, String>> transform(List<UpdateAccountArrangementRequest.ArrangementField> arrangementFields) {
        List<Map<String, String>> result = new ArrayList<>();

        IntStream.range(0, arrangementFields.size()).forEach(index -> {
            UpdateAccountArrangementRequest.ArrangementField item = arrangementFields.get(index);
            int oneBasedIndex = index + 1;

            Map<String, String> nameField = new HashMap<>();
            nameField.put("name", String.format("FIELD.NAME:1:%d", oneBasedIndex));
            nameField.put("value", item.getFieldName());

            Map<String, String> valueField = new HashMap<>();
            valueField.put("name", String.format("FIELD.VALUE:1:%d", oneBasedIndex));
            valueField.put("value", item.getFieldValue());

            result.add(nameField);
            result.add(valueField);
        });

        return result;
    }

    private ResponseEntity<?> handleUpdateAccountArrangementResponse(T24GenericResponse response) throws JMSException {

        t24ExceptionHandlerService.checkT24Exception(response);
        var UpdateAcountArrangementResponse= UpdateAccountArrangementResponse.builder()
                .transactionReferenceNo(response.getHeaders().stream().filter(header -> header.getName().equals("trxRef")).findFirst().get().getValue())
                .build();

        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true,"200",SUCCESS_MESSAGE,null,null,null))
                .response(UpdateAcountArrangementResponse)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }
}
