package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountIntrestMapper;
import jo.capitalbank.ms.mapper.BillerAccountMapper;
import jo.capitalbank.ms.models.responses.AccountIntrestT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountIntersetService {


    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    @Autowired
    T24InquiryService inquiryService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    BillerAccountMapper billerAccountMapper;

    @Value("${t24Interfaces.accountInterest.version}")
    private String version;
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;
    @Autowired
    private AccountIntrestMapper accountIntrestMapper;


    private final static String ACCOUNT_NO = "ACCOUNT.NO:EQ";
    private final static String PRODUCT_NAME = "PRODUCT.NAME:EQ";
    private final static String CURRENCY_EQ = "CURRENCY:EQ";


    public ResponseEntity<?> getAccountInterest(String accountNumber, String productName, String currency) {
        List<T24Field> t24Fields = new ArrayList<>();
        t24Fields.add(new T24Field(ACCOUNT_NO, accountNumber));
        t24Fields.add(new T24Field(PRODUCT_NAME, productName));
        t24Fields.add(new T24Field(CURRENCY_EQ, currency));
        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setOptions("");
        enquiryRequest.setEnquiry(version);
        enquiryRequest.setEnquiryId("");
        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setT24Fields(t24Fields);


        var t24Response = objectMapper.convertValue(inquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);


        return getAccountInterestResponse(t24Response.getBody());

    }

    private ResponseEntity<?> getAccountInterestResponse(List<T24GenericResponse.Record> t24Fields) {

        var t24ResponseFileds = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var accountIntresetResponse = t24ResponseFileds.stream()
                .filter(map -> map != null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountIntrestT24Fields.class))
                .filter(Objects::nonNull)
                .toList();

        var accountIntrestResponse = accountIntrestMapper.accountIntrestResponse(accountIntresetResponse.getFirst());
        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(accountIntrestResponse)
                .build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }


}
