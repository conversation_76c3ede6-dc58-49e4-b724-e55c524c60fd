package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24BusinessException;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountByLimitMapper;
import jo.capitalbank.ms.models.responses.AccountByLimitsT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountByLimitService {


    @Autowired
    private T24InquiryService t24InquiryService;

    @Autowired
    private ObjectMapper objectMapper;

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    @Value("${t24Interfaces.accountByLimit.version}")
    private String version;

    private final static String LIABNO = "LIAB.NO:EQ";
    private final static String REFNO = "REF.NO:EQ";
    private final static String SERNO = "SER.NO:EQ";
    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;
    @Autowired
    private AccountByLimitMapper accountByLimitMapper;


    public ResponseEntity<?> getAccountsByLimit(String customerId, String limitReference, String limitSerialNumber) {

        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field(LIABNO, customerId));
        if(limitReference != null && !limitReference.isEmpty()) {}
            t24Fields.add(new T24Field(REFNO, limitReference));

        if(limitSerialNumber != null && !limitSerialNumber.isEmpty()) {}
            t24Fields.add(new T24Field(SERNO, limitSerialNumber));
        var enquiryRequest = EnquiryRequest.builder()
                .enquiryId("")
                .enquiry(version)
                .userInformation(new UserInformation("", "", ""))
                .operation("")
                .t24Fields(t24Fields)
                .build();


        var t24GenericResponse = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        return getAccountByLimitResponseDto(t24GenericResponse.getBody());

    }

    private ResponseEntity<?> getAccountByLimitResponseDto(List<T24GenericResponse.Record> t24Fields) {

        if (t24Fields.isEmpty()) {
            throw new T24BusinessException("No Records Found", ErrorCodes.ACCT_T24003790);
        }


        var t24ResponseFields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var accountsList=t24ResponseFields.stream()
                .filter(map-> map!=null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountByLimitsT24Fields.class))
                .filter(Objects::nonNull)
                .toList();


        var accountByLimitDto= accountByLimitMapper.mapToAccountsList(accountsList);

        var commonResponse = CommonResponse.builder()
                .response(accountByLimitDto)
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);

    }
}
