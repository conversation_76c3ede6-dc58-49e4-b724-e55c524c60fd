package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class AccountEstatementService {

    @Autowired
    private T24TransactionService t24TransactionService;

    @Autowired
    ObjectMapper objectMapper;

    @Value("${t24Interfaces.accountEstatementList.version}")
    private String version;
    @Value("${t24Interfaces.accountEstatementList.operation}")
    private String operation;

    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    private final static String ACCOUNTID = "ACCOUNT.ID";
    private final static String FROMDATE = "FROM.DATE";
    private final static String TODATE = "TO.DATE";
    private final static String LANGUAGE = "LANGUAGE";
    @Autowired
    private T24ExceptionHandlerService t24ExceptionHandlerService;

    public ResponseEntity<?> getAccountEstatement(String accountId, String fromDate, String toDate, String language) throws JMSException {

        var t24Fields = new ArrayList<T24Field>();
        t24Fields.add(new T24Field(ACCOUNTID, accountId));
        t24Fields.add(new T24Field(FROMDATE, fromDate));
        t24Fields.add(new T24Field(TODATE, toDate));
        t24Fields.add(new T24Field(LANGUAGE, language));

        var transactionOptions = new TransactionRequest.TransactionOptions();
        transactionOptions.setFunction(FUNCTION);
        transactionOptions.setGtsControl(GTS_CONTROL);
        transactionOptions.setVersion(version);
        transactionOptions.setProcessOrValidate("");
        transactionOptions.setGtsControl("");
        transactionOptions.setNoOfAuth("");

        var transactionRequest = TransactionRequest.builder()
                .transactionId("")
                .operation(operation)
                .options(transactionOptions)
                .userInformation(new UserInformation("", "", ""))
                .t24Fields(t24Fields)
                .build();


        var t24GenericResponse = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);

        return getAccountEstatementResponse(t24GenericResponse);


    }

    private ResponseEntity<?> getAccountEstatementResponse(T24GenericResponse t24GenericResponse) {

        t24ExceptionHandlerService.checkT24Exception(t24GenericResponse);
        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .build();

        return ResponseEntity.ok(commonResponse);
    }

}
