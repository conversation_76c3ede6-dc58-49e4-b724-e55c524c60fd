package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.InternalAccountCreationResponse;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.mapper.AccountInternalMapper;
import jo.capitalbank.ms.models.requests.InternalAccountCreationRequest;
import jo.capitalbank.ms.models.responses.InternalAccountCreationT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class AccountInternalCreationService {


    @Autowired
    T24TransactionService t24TransactionService;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    T24ExceptionHandlerService t24ExceptionHandlerService;

    @Autowired
    AccountInternalMapper accountInternalMapper;

    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";


    @Value("${t24Interfaces.accountCreationRequestGeneric.version}")
    private String genericVersion;
    @Value("${t24Interfaces.accountCreationRequestGeneric.operation}")
    private String genericOperation;

    @Value("${t24Interfaces.accountCreationOnboarding.version}")
    private String onboardingVersion;
    @Value("${t24Interfaces.accountCreationOnboarding.operation}")
    private String onboardingOperation;


    // T24 Ofs Request Fields (GENERIC VERSION)
    private static final String CUSTOMER_FIELD = "CUSTOMER";
    private static final String ACCOUNT_TITLE_FIELD = "ACCOUNT.TITLE.1";
    private static final String SHORT_TITLE_FIELD = "SHORT.TITLE";
    private static final String CATEGORY_FIELD = "CATEGORY";
    private static final String CURRENCY_FIELD = "CURRENCY";
    private static final String LIMITFIELD = "LIMIT.REF";


    // T24 Ofs Request Fields (Onboarding VERSION)
    private static final String ARRANGEMENT_FIELD = "ARRANGEMENT";
    private static final String ACTIVITY_FIELD = "ACTIVITY";
    private static final String CUSTOMER_FIELD_ONB = "CUSTOMER:1:1";
    private static final String PRODUCT_FIELD = "PRODUCT:1:1";
    private static final String CURRENCY_FIELD_ONB = "CURRENCY:1:1";
    private static final String PROPERTY_FIELD_ONB = "PROPERTY:1:1";
    private static final String FIELD_NAME_1_1 = "FIELD.NAME:1:1";
    private static final String FIELD_VALUE_1_1 = "FIELD.VALUE:1:1";
    private static final String FIELD_NAME_1_2 = "FIELD.NAME:1:2";
    private static final String FIELD_VALUE_1_2 = "FIELD.VALUE:1:2";
    private static final String FIELD_NAME_1_3 = "FIELD.NAME:1:3";
    private static final String FIELD_VALUE_1_3 = "FIELD.VALUE:1:3";


    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";


    public ResponseEntity<?> createInternalAccount(InternalAccountCreationRequest internalAccountCreationRequest, String channelId) throws JMSException {


        if (channelId.equals("MOBCHL")) {

            var transactionOptions = new TransactionRequest.TransactionOptions();
            transactionOptions.setFunction(FUNCTION);
            transactionOptions.setGtsControl(GTS_CONTROL);
            transactionOptions.setVersion(onboardingVersion);
            transactionOptions.setProcessOrValidate("");
            transactionOptions.setGtsControl("");
            transactionOptions.setNoOfAuth("");
            List<T24Field> t24Fields = new ArrayList<>();
            t24Fields.add(new T24Field(ARRANGEMENT_FIELD, "NEW"));
            t24Fields.add(new T24Field(ACTIVITY_FIELD, "ACCOUNTS-NEW-ARRANGEMENT"));
            t24Fields.add(new T24Field(CUSTOMER_FIELD_ONB, internalAccountCreationRequest.getCustomerId() != null ? internalAccountCreationRequest.getCustomerId() : ""));
            t24Fields.add(new T24Field(PRODUCT_FIELD, internalAccountCreationRequest.getProduct() != null ? internalAccountCreationRequest.getProduct() : ""));
            t24Fields.add(new T24Field(CURRENCY_FIELD_ONB, internalAccountCreationRequest.getCurrency() != null ? internalAccountCreationRequest.getCurrency() : ""));
            t24Fields.add(new T24Field(PROPERTY_FIELD_ONB, "BALANCE"));

            t24Fields.add(new T24Field(FIELD_NAME_1_1, "ACCT.NICK.NAME:1:1"));
            t24Fields.add(new T24Field(FIELD_VALUE_1_1, "Capital Select Int"));
            t24Fields.add(new T24Field(FIELD_NAME_1_2, "PURP.OF.OPENING:1:1"));
            t24Fields.add(new T24Field(FIELD_VALUE_1_2, "PURP.OF.OPENING:1:1"));
            t24Fields.add(new T24Field(FIELD_NAME_1_3, "OMNI.FLAG:1:1"));
            t24Fields.add(new T24Field(FIELD_VALUE_1_3, "Y"));


            var transactionRequest = TransactionRequest.builder().operation(onboardingOperation).
                    transactionId("")
                    .options(transactionOptions)
                    .userInformation(new UserInformation("", "", ""))
                    .t24Fields(t24Fields).build();

            var t24Response = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);
            return mapAccountCreationResponse(t24Response);


        } else {

            var transactionOptions = new TransactionRequest.TransactionOptions();
            transactionOptions.setFunction(FUNCTION);
            transactionOptions.setGtsControl(GTS_CONTROL);
            transactionOptions.setVersion(genericVersion);
            transactionOptions.setProcessOrValidate("");
            transactionOptions.setGtsControl("");
            transactionOptions.setNoOfAuth("");
            List<T24Field> t24Fields = new ArrayList<>();
            t24Fields.add(new T24Field(CUSTOMER_FIELD, internalAccountCreationRequest.getCustomerId() != null ? internalAccountCreationRequest.getCustomerId() : ""));
            t24Fields.add(new T24Field(ACCOUNT_TITLE_FIELD, internalAccountCreationRequest.getAccountHolderName().getEn() != null ? internalAccountCreationRequest.getAccountHolderName().getEn() : ""));
            t24Fields.add(new T24Field(SHORT_TITLE_FIELD, internalAccountCreationRequest.getShortName().getEn() != null ? internalAccountCreationRequest.getShortName().getEn() : ""));
            t24Fields.add(new T24Field(CATEGORY_FIELD, internalAccountCreationRequest.getProduct() != null ? internalAccountCreationRequest.getProduct() : ""));
            t24Fields.add(new T24Field(LIMITFIELD, internalAccountCreationRequest.getLimitRef() != null ? internalAccountCreationRequest.getLimitRef() : ""));
            t24Fields.add(new T24Field(CURRENCY_FIELD, internalAccountCreationRequest.getCurrency() != null ? internalAccountCreationRequest.getCurrency() : ""));


            var transactionRequest = TransactionRequest.builder().operation(genericOperation).
                    transactionId("")
                    .options(transactionOptions)
                    .userInformation(new UserInformation("", "", ""))
                    .t24Fields(t24Fields).build();

            var t24Response = objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);

            return mapAccountCreationResponse(t24Response);
        }


    }

    private ResponseEntity<?> mapAccountCreationResponse(T24GenericResponse t24GenericResponse) {

        t24ExceptionHandlerService.checkT24Exception(t24GenericResponse);

        var listofFields = new ArrayList<HashMap<String, String>>();
        t24GenericResponse.getBody().forEach(field -> {
            var mapResponse = new HashMap<String, String>();
            field.getFields().forEach(f -> {

                mapResponse.put(f.getT24field().getName().split(":")[0], f.getT24field().getValue());
            });
            listofFields.add(mapResponse);

        });

        var accountCreationResponse = objectMapper.convertValue(listofFields.getFirst(), InternalAccountCreationT24Fields.class);
        var dtoResponse = accountInternalMapper.toInternalAccountCreationResponse(accountCreationResponse);
        var accountId = t24GenericResponse.getHeaders().stream().filter(header -> header.getName().equals("trxRef")).findFirst().get().getValue();
        dtoResponse.setAccountId(accountId);

        var commonResponse = CommonResponse.builder().status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(dtoResponse).build();
        return new ResponseEntity<>(commonResponse, HttpStatus.OK);
    }


}
