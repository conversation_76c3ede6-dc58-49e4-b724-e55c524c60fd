package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.AccountPostingRestrictionResponse;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.coyote.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@Service
@Slf4j
public class AccountsInquiryService {


    @Autowired
    T24InquiryService t24InquiryService;

    @Autowired
    ObjectMapper objectMapper;


    public ResponseEntity<?> getAccountPostingRestrictions(String accountNumber) throws JMSException {

        var enquiryRequest = new EnquiryRequest();
        enquiryRequest.setEnquiry("CBOJ.ACC.POST.RES.MW");
        enquiryRequest.setOptions("");


        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        enquiryRequest.setEnquiryId("@ID:EQ=" + accountNumber);

        var t24Response = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        log.info("T24 Response After Mapping: {}", t24Response);

        return prepareAccounPostingRestrictionResponse(t24Response.getBody());


    }

    private ResponseEntity<?> prepareAccounPostingRestrictionResponse(List<T24GenericResponse.Record> t24Fields) {


        var listofFields = new ArrayList<HashMap<String, String>>(t24Fields.size());
        log.info("T24 Response After Mapping: {}", t24Fields.size());
        t24Fields.forEach(field -> {
            var mapResponse = new HashMap<String, String>();
            field.getFields().forEach(f -> {

                mapResponse.put(f.getT24field().getName().split(":")[0], f.getT24field().getValue());
            });
            listofFields.add(mapResponse);

        });

        return accountPostingRestResponse(listofFields);


    }

    private ResponseEntity<?> accountPostingRestResponse(ArrayList<HashMap<String, String>> t24FieldsList) {

        var listOfPostingRestrictions = new ArrayList<AccountPostingRestrictionResponse.PostingRestriction>();
/*        t24FieldsList.stream().forEach(field -> {
            var postingRestriction = new AccountPostingRestrictionResponse.PostingRestriction();
           log.info("T24 Response After Mapping: {}", field);
           postingRestriction.setCode(field.get("POSTING.RESTRICT"));
           postingRestriction.setDescription(field.get("POSTING.RESTRICT.DES"));
           postingRestriction.setType(field.get("POSTING.RESTRICT.TYPE"));
           postingRestriction.setNarrative(field.get("PR.NARRATIVE"));
           postingRestriction.setTypeDescription(field.get("POSTING.RESTRICT.DES"));
           listOfPostingRestrictions.add(postingRestriction);

        });*/
        t24FieldsList.forEach(field -> {
            var code = (String) field.get("POSTING.RESTRICT");
            var description = (String) field.get("POSTING.RESTRICT.DES");
            var type = (String) field.get("POSTING.RESTRICT.TYPE");
            var narrative = (String) field.get("PR.NARRATIVE");

            // Skip if all fields are null or blank
            if (Stream.of(code, description, type, narrative).allMatch(val -> val == null || val.isBlank())) {
                return; // Skip this iteration
            }

            var postingRestriction = new AccountPostingRestrictionResponse.PostingRestriction();
            postingRestriction.setCode(code);
            postingRestriction.setDescription(description);
            postingRestriction.setType(type);
            postingRestriction.setNarrative(narrative);
            postingRestriction.setTypeDescription(description); // duplicate of description?

            listOfPostingRestrictions.add(postingRestriction);
        });



        var accountPostingRestriction= new AccountPostingRestrictionResponse.Accounts(listOfPostingRestrictions);
        var accountPostingRestrictionsResponse = AccountPostingRestrictionResponse.builder()
                .accounts(accountPostingRestriction).build();

        var status = new CommonResponse.Status(true,"200","success","","","");
        var commonResponse = CommonResponse.builder().status(status).response(accountPostingRestrictionsResponse).build();

        return new ResponseEntity<>(commonResponse,HttpStatus.OK);
    }


}
