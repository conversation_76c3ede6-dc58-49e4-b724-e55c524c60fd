package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.SafeDepositClosureResponse;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24ExceptionHandlerService;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.library.services.T24TransactionService;
import jo.capitalbank.ms.mapper.AccountSafeDepositMapper;
import jo.capitalbank.ms.models.requests.SafeDepositClosureRequest;
import jo.capitalbank.ms.models.responses.AccountSafeDepositT24Fields;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AccountSafeDepositsService {


    @Autowired
    private T24InquiryService t24InquiryService;


    @Autowired
    private ObjectMapper objectMapper;

    private final static String SUCCESS_MESSAGE = "The Operation has been Successfully Completed";

    @Value("${t24Interfaces.safeDepositBox.version}")
    private String version;

    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;
    @Autowired
    private AccountSafeDepositMapper accountSafeDepositMapper;


    @Autowired
    private T24TransactionService t24TransactionService;

    @Value("${t24Interfaces.safeDepositBoxAccountClosure.operation}")
    private String operation;

    @Value("${t24Interfaces.safeDepositBoxAccountClosure.version}")
    private String transactionVersion;
    private final static String DEFAULT_COMPANY_ID = "*********";


    private final static String FUNCTION = "I";
    private final static String GTS_CONTROL = "0";
    @Autowired
    private T24ExceptionHandlerService t24ExceptionHandlerService;

    public ResponseEntity<?> safeDepositRequest( SafeDepositClosureRequest request) throws JMSException {

        var transactionOptions = new TransactionRequest.TransactionOptions();
        transactionOptions.setFunction(FUNCTION);
        transactionOptions.setGtsControl(GTS_CONTROL);
        transactionOptions.setVersion(transactionVersion);
        transactionOptions.setProcessOrValidate("");
        transactionOptions.setGtsControl("");
        transactionOptions.setNoOfAuth("");


        var t24Fields = new ArrayList<T24Field>();

        if (request.getClosureAccount() != null)
            t24Fields.add(new T24Field("CLOSE.ONLINE", request.getClosureAccount()));

        if (request.getSettlementAccount() != null)
            t24Fields.add(new T24Field("SETTLEMENT.ACCT", request.getSettlementAccount()));

        var transactionRequest = TransactionRequest.builder()
                .operation(operation)
                .transactionId(request.getAccountNumber())
                .options(transactionOptions)
                .userInformation(new UserInformation("", "", (request.getAccountCompanyCode() == null) ? DEFAULT_COMPANY_ID : request.getAccountCompanyCode()))
                .t24Fields(t24Fields)
                .build();


        var t24Response= objectMapper.convertValue(t24TransactionService.processTransaction(transactionRequest).getBody(), T24GenericResponse.class);

        return handleSafeDepositClosureResponse(t24Response);


    }

    public ResponseEntity<?> getAccountSafeDeposits(String id) {
        var t24Fields = new ArrayList<T24Field>();
        if (id.startsWith("2"))
            t24Fields.add(new T24Field("CUSTOMER:EQ", id));
        else
            t24Fields.add(new T24Field("ACCOUNT:EQ", id));

        var enquiryRequest = EnquiryRequest.builder()
                .enquiryId("")
                .enquiry(version)
                .userInformation(new UserInformation("", "", ""))
                .operation("")
                .t24Fields(t24Fields)
                .build();


        var t24Response = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);

        return handleSafeDepositClosureResponse(t24Response.getBody());

    }


    private ResponseEntity<?> handleSafeDepositClosureResponse(List<T24GenericResponse.Record> t24Fields) {
        var t24FieldsResponse = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        var safeDepositBoxList = t24FieldsResponse.stream()
                .filter(map -> map != null && !map.isEmpty())
                .map(map -> objectMapper.convertValue(map, AccountSafeDepositT24Fields.class))
                .filter(Objects::nonNull)
                .toList();

        var safeDepositResponse = accountSafeDepositMapper.toAccountSafeDepositResponse(safeDepositBoxList);
        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(safeDepositResponse)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);


    }


    private ResponseEntity<?> handleSafeDepositClosureResponse(T24GenericResponse response) {
        t24ExceptionHandlerService.checkT24Exception(response);

        var safeDepositClosureResponse= SafeDepositClosureResponse.builder()
                .transactionReferenceNo(response.getHeaders().stream().filter(header -> header.getName().equals("trxRef")).findFirst().get().getValue())
                .build();

        var commonResponse = CommonResponse.builder()
                .status(new CommonResponse.Status(true, "200", SUCCESS_MESSAGE, null, null, null))
                .response(safeDepositClosureResponse)
                .build();

        return new ResponseEntity<>(commonResponse, HttpStatus.OK);



    }
}
