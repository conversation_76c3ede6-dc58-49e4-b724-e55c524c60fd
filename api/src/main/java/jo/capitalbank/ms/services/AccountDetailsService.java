package jo.capitalbank.ms.services;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.mq.headers.internal.store.StoreDataOutput;
import jakarta.jms.JMSException;
import jo.capitalbank.ms.dto.AccountDetailsResponse;
import jo.capitalbank.ms.dto.AccountDetailsResponseKofax;
import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24BusinessException;
import jo.capitalbank.ms.library.common.dto.CommonResponse;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import jo.capitalbank.ms.library.models.requests.T24Field;
import jo.capitalbank.ms.library.models.requests.UserInformation;
import jo.capitalbank.ms.library.services.T24InquiryService;
import jo.capitalbank.ms.mapper.AccountInfoMapper;
import jo.capitalbank.ms.models.responses.AccountDetailsT24ResponseFields;
import jo.capitalbank.ms.models.responses.AccountDetailsT24ResponseFiledsBpm;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import jo.capitalbank.ms.utils.T24FieldsExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class AccountDetailsService {


    @Autowired
    private T24InquiryService t24InquiryService;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${t24Interfaces.accountDetails.version}")
    private String accountDetailsVersion;
    @Value("${t24Interfaces.accountDetailsBpmchlOther.version}")
    private String accountDetailsVersionbpmchlother;
    @Value("${t24Interfaces.accountDetailsBpmchl.version}")
    private String accountDetailsVersionbpmchl;

    @Autowired
    AccountInfoMapper accountInfoMapper;


    private final static String ACCOUNT_ENQ_ID = "ACCT.NO:EQ=";
    private final static String ENQ_ID = "@ID:EQ=";
    private final static String CHANNEL_ID_ENQ = "L.CHANNEL.ID:EQ";
    private final static String SERVICE_TYPE_ENQ = "L.SERVICE.TYPE:EQ";
    private final static String KOFAXCHL = "KOFAX";
    private final static String BPMCHL = "BPMCHL";
    private final static String BPMCH_OTHER = "other";

    @Autowired
    private T24FieldsExtractor t24FieldsExtractor;

    public CommonResponse getAccountDetails(String accountId, String serviceType, String channelId, String subChannelId) {

        var enquiryRequest = new EnquiryRequest();

        enquiryRequest.setOptions("");

        enquiryRequest.setUserInformation(new UserInformation("", "", ""));
        var t24FieldsReq = new ArrayList<T24Field>();

        if (serviceType != null && serviceType.equalsIgnoreCase(KOFAXCHL)) {
            enquiryRequest.setEnquiry(accountDetailsVersionbpmchl);
            enquiryRequest.setEnquiryId(ACCOUNT_ENQ_ID + accountId);
        } else if (channelId.equals(BPMCHL) && subChannelId != null && subChannelId.equals(BPMCH_OTHER)) {
            enquiryRequest.setEnquiry(accountDetailsVersionbpmchlother);
            enquiryRequest.setEnquiryId(ACCOUNT_ENQ_ID + accountId);
        } else {
            enquiryRequest.setEnquiry(accountDetailsVersion);
            enquiryRequest.setEnquiryId(ENQ_ID + accountId);
            t24FieldsReq.add(new T24Field(CHANNEL_ID_ENQ, channelId));
            if (serviceType != null && !serviceType.isEmpty())
                t24FieldsReq.add(new T24Field(SERVICE_TYPE_ENQ, serviceType));
        }

        enquiryRequest.setT24Fields(t24FieldsReq);

        var t24Response = objectMapper.convertValue(t24InquiryService.processEnquiry(enquiryRequest).getBody(), T24GenericResponse.class);
        return prepareAccountDetailsResponse(t24Response.getBody(), channelId, subChannelId, serviceType);
    }

    private CommonResponse prepareAccountDetailsResponse(List<T24GenericResponse.Record> t24Fields, String channelId, String subChannelId, String serviceType) {
        CommonResponse commonResponse = new CommonResponse();
        var t24ResponseFields = t24FieldsExtractor.t24FieldsExtractor(t24Fields);

        if (subChannelId != null && channelId.equalsIgnoreCase(BPMCHL) && subChannelId.equalsIgnoreCase(BPMCH_OTHER)) {
            AccountDetailsResponse accountDetailsResponse = prepareAccountDetailsResponseMapperBpmOther(t24ResponseFields);
            //todo: [Hussein] make default method to setup the success response. like CommonResponse.successResponse(accountDetailsResponse);
            commonResponse.setSuccessResponse(accountDetailsResponse);

        } else if (serviceType != null && serviceType.equalsIgnoreCase(KOFAXCHL)) {
            ArrayList<AccountDetailsResponseKofax> list = prepareAccountDetailsResponseMapperBpm(t24ResponseFields);
            commonResponse.setSuccessResponse(list);
            return commonResponse;
        }
        AccountDetailsResponse accountDetailsResponse = prepareAccountDetailsResponseMapperMain(t24ResponseFields);
        commonResponse.setSuccessResponse(accountDetailsResponse);
        return commonResponse;
    }

    private AccountDetailsResponse prepareAccountDetailsResponseMapperMain(List<HashMap<String, String>> t24FieldsList) {

        //using object mapper to map the t24 fields into a class as the Mapstruct has limitation when
        //it comes to (.) in field names
        var result = objectMapper.convertValue(t24FieldsList.getFirst(), AccountDetailsT24ResponseFields.class);

        //using map struct to map the account details fields
        var accountMapperResponse = accountInfoMapper.mapAccountT24ToAccountDto(result);
        var customerMapperResponse = accountInfoMapper.mapCustomerT24ToCustomerDto(result);

        var accountDetailsResponse = new AccountDetailsResponse();
        accountDetailsResponse.setCustomers(customerMapperResponse);
        accountDetailsResponse.setAccounts(accountMapperResponse);

        return accountDetailsResponse;
    }

    private ArrayList<AccountDetailsResponseKofax> prepareAccountDetailsResponseMapperBpm(List<HashMap<String, String>> t24FieldsList) {

        var result = objectMapper.convertValue(t24FieldsList.getFirst(), AccountDetailsT24ResponseFields.class);

        var accountMapperResponse = accountInfoMapper.mapKofaxToAccountDto(result);

        var list = new ArrayList<AccountDetailsResponseKofax>();
        list.add(accountMapperResponse);
        return list;
    }

    private AccountDetailsResponse prepareAccountDetailsResponseMapperBpmOther(List<HashMap<String, String>> t24FieldsList) {

        //using object mapper to map the t24 fields into a class as the Mapstruct has limitation when
        //it comes to (.) in field names
        var result = objectMapper.convertValue(t24FieldsList.getFirst(), AccountDetailsT24ResponseFiledsBpm.class);

        log.info("BPM Other Account prepareAccountDetailsResponseMapperBpm");
        //using map struct to map the account details fields
        var customerMapperResponse = accountInfoMapper.mapCustomerBpmToAccountDto(result);
        var accountMapperResponse = accountInfoMapper.mapAccountBpmToAccountDto(result);
        var accountDetailsResponse = new AccountDetailsResponse();
        accountDetailsResponse.setAccounts(accountMapperResponse);
        accountDetailsResponse.setCustomers(customerMapperResponse);

        return accountDetailsResponse;
    }

}
