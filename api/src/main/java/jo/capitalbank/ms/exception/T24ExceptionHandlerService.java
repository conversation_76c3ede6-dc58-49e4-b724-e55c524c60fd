package jo.capitalbank.ms.exception;

import jo.capitalbank.ms.config.ErrorCodesMapper;
import jo.capitalbank.ms.library.error.exception.SubException;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
public class T24ExceptionHandlerService {


    private String statusError[] = {"-1", "-2", "-3"};

    @Autowired
    private ErrorCodesMapper errorCodesMapper;

    public void checkT24Exception(T24GenericResponse t24GenericResponse) {

        AtomicReference<String> errorDescription= new AtomicReference<>("default");
        t24GenericResponse.getHeaders().stream().filter(header -> header.getName().equals("status")).findFirst().ifPresent(header -> {
            if (Arrays.stream(statusError).anyMatch(header.getValue()::equalsIgnoreCase)) {
                List<SubException> subErrors = new ArrayList<>();
                t24GenericResponse.getBody().forEach(body -> {

                    body.getFields().forEach(field -> {

                        errorDescription.set(field.getT24field().getValue());
                        var subException = new SubException(field.getT24field().getName(), field.getT24field().getValue(), "");
                        subErrors.add(subException);
                    });

                });


                if (!t24GenericResponse.getBody().isEmpty())
                    throw new T24BusinessException(t24GenericResponse.getBody().getFirst().getFields().getLast().getT24field().getValue(), getErrorCodeBasedOnError(errorDescription.get()).toString(), subErrors);
                else
                    throw new T24BusinessException("", getErrorCodeBasedOnError(errorDescription.get()), subErrors);

            }
        });

    }

    private String getErrorCodeBasedOnError(String errorDescription) {
        try{
            log.info("errorDescription: {}", errorDescription);

            var errorCode = errorCodesMapper.getErrors().stream().filter(error -> error.contains(errorDescription)).findFirst();

            if (!errorCode.get().isEmpty())
                return errorCode.get().split("\\|")[1];
            else
                return "T2499997";

        }catch (Exception e){
            return "T2499997";
        }

    }


}
