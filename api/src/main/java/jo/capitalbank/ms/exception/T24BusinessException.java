package jo.capitalbank.ms.exception;

import jo.capitalbank.ms.library.error.code.ErrorCodeEnum;
import jo.capitalbank.ms.library.error.exception.OBBusinessException;
import jo.capitalbank.ms.library.error.exception.SubException;

import java.util.List;
import java.util.Map;

public class T24BusinessException extends OBBusinessException {
    public T24BusinessException(String message,Enum<?> errorCode) {
        super(message, errorCode);
    }

    public T24BusinessException(String message,String errorCode,List<SubException> subErrors) {
        super(message, errorCode,subErrors,null);
    }

    protected T24BusinessException(String message, Enum<?> errorCode, List<SubException> subErrors, Map<String, String> additionalInformation) {
        super(message, errorCode, subErrors, additionalInformation);
    }
}
