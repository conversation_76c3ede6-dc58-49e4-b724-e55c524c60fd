package jo.capitalbank.ms.models.requests;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bouncycastle.cms.PasswordRecipientId;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountCardRequest {

    private String productType;
    private String currency;
    private String accountType;
    private String purposeFacility;
    private String limitRefernece;
    private String limitSerial;
    private String accountCompanyCode;
}
