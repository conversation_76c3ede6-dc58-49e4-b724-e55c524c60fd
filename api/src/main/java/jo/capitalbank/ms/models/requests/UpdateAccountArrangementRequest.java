package jo.capitalbank.ms.models.requests;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateAccountArrangementRequest {


    private String activityName;

    private String property;

    private String effectiveDate;

    private List<ArrangementField> arrangementFields;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ArrangementField {

        private String fieldName;

        private String fieldValue;
    }
}


