package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountDetailsT24ResponseFields {
    @JsonProperty("TARGET")
    private String target;

    @JsonProperty("AVAIL.BAL.AMT")
    private String availableBalanceAmount;

    @JsonProperty("LEDGER.BAL")
    private String ledgerBalance;

    @JsonProperty("CODE")
    private String code;

    @JsonProperty("EN.CUST.NAME")
    private String customerNameEn;

    @JsonProperty("AR.CUST.NAME")
    private String customerNameAr;


    @JsonProperty("MOBILE.NO")
    private String mobileNumber;


    @JsonProperty("CREDIT.INTEREST.RATE")
    private String creditInterestRate;

    @JsonProperty("ACCOUNT.TYPE")
    private String accountType;

    @JsonProperty("AVAILABLE.BAL")
    private String availableBalance;

    @JsonProperty("AccruedCRInterest")
    private String accruedCRInterest;

    @JsonProperty("STATEMENT.FREQUENCY")
    private String statementFrequency;

    @JsonProperty("BRANCH.EN")
    private String branchEnglish;

    @JsonProperty("STATUS")
    private String status;

    @JsonProperty("CUSTOMER.POSTING.RESTRICT.TYPE.DESC")
    private String customerPostingRestrictTypeDesc;

    @JsonProperty("CATEGORY")
    private String category;

    @JsonProperty("PRODUCT")
    private String product;

    @JsonProperty("CATEGORY.DESCRIPTION")
    private String categoryDescription;

    @JsonProperty("CUSTOMER.ID")
    private String customerId;

    @JsonProperty("CUST.ID")
    private String customId;

    @JsonProperty("BRANCH.AR")
    private String branchArabic;


    @JsonProperty("IBAN.NUMBER")
    private String iban;

    @JsonProperty("CURRENCY")
    private String currency;

    @JsonProperty("NAT.ID.NO")
    private String nationalNumber;

    @JsonProperty("NATIONALITY")
    private String nationality;

    @JsonProperty("RESIDENCE")
    private String residence;

    @JsonProperty("GENDER")
    private String gender;

    @JsonProperty("DOB")
    private String dob;

    @JsonProperty("POSTING.RESTRICT")
    private String postingRestrict;

    @JsonProperty("CUSTOMER.STATUS")
    private String customerSatus;

    @JsonProperty("FAX")
    private String fax;

    @JsonProperty("EMAIL")
    private String email;


    @JsonProperty("SEGMENT")
    private String segment;

    @JsonProperty("RISK.RATING")
    private String riskRating;


    @JsonProperty("BRANCH")
    private String branch;

    @JsonProperty("AccruedDRInterest")
    private String accruedDRInterest;

    @JsonProperty("CUSTOMER.POSTING.RESTRICT.TYPE")
    private String customerPostingRestrictType;

    @JsonProperty("DEBIT.INTEREST.RATE")
    private String debitInterestRate;

    @JsonProperty("CUSTOMER.POSTING.RESTRICT.CODE")
    private String customerPostingRestrictCode;

    @JsonProperty("CLEARED.BALANCE")
    private String clearedBalance;

    @JsonProperty("Y.ACCOUNT.ID")
    private String accountId;

    @JsonProperty("DAO")
    private String accountOfficer;

    @JsonProperty("IS.SETTLEMENT")
    private String isSettlement;


    @JsonProperty("CUSTOMER.NAME")
    private String customerName;

    @JsonProperty("CUSTOMER.NAME.ENGLISH")
    private String customerNameEnglish;

    @JsonProperty("CUSTOMER.NAME.ARABIC")
    private String customerNameArabic;

    @JsonProperty("OnlineActualBal")
    private String onlineActualBalance;

    @JsonProperty("LOCKED.AMOUNT")
    private String lockedAmount;

    @JsonProperty("WORKING.BALANCE")
    private String workingBalance;

    @JsonProperty("AVAIL.LMT.AMT")
    private String availableLimitAmount;

    @JsonProperty("OPENING.DATE")
    private String openingDate;

    @JsonProperty("ACCOUNT.TITLE")
    private String accountTitle;

    @JsonProperty("ACCOUNT.TITLE.AR")
    private String accountTitleArabic;

    @JsonProperty("INT.RATE")
    private String accountInterestRate;

    @JsonProperty("IBAN")
    private String ibanNumber;
    @JsonProperty("CO.CODE")
    private String companyCode;
    @JsonProperty("INACTIV.MARKER")
    private String isDormant;
    @JsonProperty("MNEMONIC")
    private String accountNickName;

    @JsonProperty("PRODUCT.ID")
    private String productId;



    @JsonProperty("LIMIT.EXPIRY.DATE")
    private String limitExpiryDate;

    @JsonProperty("AVAIL.BAL.LIMIT")
    private String availableBalanceLimit;

    @JsonProperty("LOCK.AMT")
    private String  lockAmount;

    @JsonProperty("OVR.DRN.FLAG")
    private String overdrawnFlag;

    @JsonProperty("EX.LIM.FLAG")
    private String exceedLimitFlag;

    @JsonProperty("ONLINE.LIMIT")
    private String onlineLimit;

    @JsonProperty("TARGET.DESC")
    private String targetDescription;

    @JsonProperty("COMPANY.NAME")
    private String companyName;

    @JsonProperty("FAX.INDEMNITY")
    private String faxIndemnity;



    @JsonProperty("CU.POST.RES")
    private String postingRestrictCode;

    @JsonProperty("CU.POST.RES.TYP")
    private String postingRestrictTyp;

    @JsonProperty("CCU.POST.RES.TYP")
    private String postingRestrictTypeDesc;

    @JsonProperty("CU.POST.RES.DESC")
    private String postingRestrictDesc;


    @JsonProperty("AC.POST.RES.TYPE")
    private String postResType;

    @JsonProperty("AC.POST.RES.DESC")
    private String postResDesc;








}
