package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountSafeDepositT24Fields {

    @JsonProperty("TRANSACTION.REF")
    private String transactionReferance;

    @JsonProperty("AccountId")
    private String accountNumber;

    @JsonProperty("ProductId")
    private String productType;

    @JsonProperty("BoxNumber")
    private String boxNumber;

    @JsonProperty("BoxType")
    private String boxType;

    @JsonProperty("PrincipalAmount")
    private String principalAmount;

    @JsonProperty("OutstandingAmount")
    private String outstandingAmount;
}
