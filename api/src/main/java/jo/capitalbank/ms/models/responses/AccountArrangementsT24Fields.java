package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountArrangementsT24Fields {

    @JsonProperty("AccountNo")
    private String accountNumber;

    @JsonProperty("LinkedArrangement")
    private String linkedArrangementId;

    @JsonProperty("LinkType")
    private String linkType;

    @JsonProperty("LinkDate")
    private String linkDate;

    @JsonProperty("ProductLine")
    private String productLine;

    @JsonProperty("ProductId")
    private String productId;

    @JsonProperty("BalanceAmount")
    private String balanceAmount;
}
