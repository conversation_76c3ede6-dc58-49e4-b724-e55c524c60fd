package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountPostingRestricionT24Fields {

    private PostingRestriction[] postingRestrictions;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PostingRestriction {
        @JsonProperty("POSTING.RESTRICT.DES")
        private String typeDescription;
        @JsonProperty("POSTING.RESTRICT.TYPE")
        private String type;
        @JsonProperty("POSTING.RESTRICT")
        private String code;
        @JsonProperty("PR.NARRATIVE")
        private String description;

    }

}