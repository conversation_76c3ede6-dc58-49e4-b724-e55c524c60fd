package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountTransactionsT24Fields {

    @JsonProperty("ACCOUNT.NUMBER")
    private String accountId;
    @JsonProperty("BOOKING.DATE")
    private String bookingDate;
    @JsonProperty("CUSTOMER.NO")
    private String customerId;
    @JsonProperty("CURRENCY")
    private String currency;
    @JsonProperty("VALUE.DATE")
    private String valueDate;
    @JsonProperty("TRANS.REF")
    private String transactionReferenceNumber;
    @JsonProperty("TRANSACTION.CODE")
    private String transactionCode;
    @JsonProperty("CRF.TYPE")
    private String crfType;
    @JsonProperty("AMT.LCY")
    private String amountLcy;
    @JsonProperty("AMT.FCY")
    private String amountFcy;
    @JsonProperty("BEGIN.BALANCE")
    private String beginBalance;
    @JsonProperty("RUNNING.BAL")
    private String runningBalance;
    @JsonProperty("TRAN.COUNT")
    private String transactionCount;


    @JsonProperty("TRANSACTION.CODE.DESC.EN")
    private String transactionCodeDescriptionAr;

    @JsonProperty("TRANSACTION.CODE.DESC.AR")
    private String transactionCodeDescriptionEn;
}
