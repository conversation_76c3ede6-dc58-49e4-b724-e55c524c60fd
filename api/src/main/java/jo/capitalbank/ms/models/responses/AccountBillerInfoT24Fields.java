package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountBillerInfoT24Fields {


    @JsonProperty("ACCT.ID")
    private String accountId;

    @JsonProperty("ISSUE.DATE")
    private String issueDate;

    @JsonProperty("BILLER.STATUS")
    private String billerStatus;

    @JsonProperty("SERVICE.TYPE")
    private String serviceType;

    @JsonProperty("MADF.ACCT")
    private String madfoatComAccount;

    @JsonProperty("BILLER.ID")
    private String billId;

    @JsonProperty("DEFAULT.FLAG")
    private String defaultFlag;

    @JsonProperty("EN.NAME")
    private String enName;


}
