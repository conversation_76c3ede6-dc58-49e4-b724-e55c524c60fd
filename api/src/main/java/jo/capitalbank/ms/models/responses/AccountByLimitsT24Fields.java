package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountByLimitsT24Fields {



        @JsonProperty("CO.MNE")
        private String company;

        @JsonProperty("ACC.NUMBER")
        private String accountId;

        @JsonProperty("F.CCY")
        private String currency;

        @JsonProperty("DTE")
        private String limitMaturityDate;

        @JsonProperty("AMT")
        private String limitAmount;

        @JsonProperty("FULL.AMT")
        private String internalAmount;


}
