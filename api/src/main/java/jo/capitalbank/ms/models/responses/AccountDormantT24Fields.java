package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountDormantT24Fields {


    @JsonProperty("ACC.BRANCH")
    private String accountBranch;

    @JsonProperty("CUSTOMER.NO")
    private String customerNo;

    @JsonProperty("CUS.NAME.1.2")
    private String customerName;

    @JsonProperty("ACCOUNT.NO")
    private String accountNumber;

    @JsonProperty("Y.FINAL.DESC")
    private String finalDescription;

    @JsonProperty("Y.FINAL.OFFICER")
    private String finalOfficer;

    @JsonProperty("CURRENCY")
    private String currency;

    @JsonProperty("ACC.BAL")
    private String accountBalance;

    @JsonProperty("LAST.TXN.DATE")
    private String lastTxnDate;

    @JsonProperty("ACCOUNT.STATUS")
    private String accountStatus;

    @JsonProperty("STATUS.DATE")
    private String statusDate;

    @JsonProperty("PRODUCT.ID")
    private String productId;

    @JsonProperty("CUSTOMER.POST.REST")
    private String customerPostRest;

    @JsonProperty("DORMANT.FLAG")
    private String dormantFlag;

    @JsonProperty("OVERDRAWN.FLAG")
    private String overDrawnFlag;

    @JsonProperty("LEDGER.BALANCE")
    private String ledgerBalance;

    @JsonProperty("AVAILABLE.BALANCE")
    private String availableBalance;

    @JsonProperty("LOCKED.AMOUNT")
    private String lockedAmount;

    @JsonProperty("CUSTOMER.DAO")
    private String customerDepartmentAccountOfficer;

    @JsonProperty("CUSTOMER.DAO.NAME")
    private String customerDAOName;

    @JsonProperty("ACCOUNT.DAO.NAME")
    private String accountDAOName;

    @JsonProperty("TARGET")
    private String target;

    @JsonProperty("MOBILE.NO")
    private String mobileNo;

    @JsonProperty("EMAIL")
    private String email;
}
