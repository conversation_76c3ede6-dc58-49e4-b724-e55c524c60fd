package jo.capitalbank.ms.models.requests;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UpdateSettelmentAccountRequest{
    @NotBlank(message = "Account number is mandatory")
    private String accountNumber;
    @NotBlank(message = "Settlement account is mandatory")
    private String settlementAccount;
    @NotBlank(message = "New settlement account is mandatory")
    @NotNull
    private String newSettlementAccount;

}
