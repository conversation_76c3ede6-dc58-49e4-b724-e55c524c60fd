package jo.capitalbank.ms.models.responses;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class T24GenericResponse {
    private List<Header> headers;
    private List<Record> body;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Header {
        private String name;
        private String value;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Record {
        private String recordIndex;
        private List<T24FieldWrapper> fields;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class T24FieldWrapper {
        private T24Field t24field;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class T24Field {
        private String name;
        private String value;
    }
}
