package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountIntrestT24Fields {


    @JsonProperty("ProductName")
    private String productName; // from finalItem.ProductName

    @JsonProperty("Currency")
    private String currency; // from finalItem.Currency

    @JsonProperty("arrangementId")
    private String arrangementId; // from finalItem.arrangementId

    @JsonProperty("fixedRate")
    private String fixedRate; // from finalItem.fixedRate

    @JsonProperty("biKey")
    private String biKey; // from finalItem.biKey

    @JsonProperty("piKey")
    private String piKey; // from finalItem.piKey

    @JsonProperty("periodicPeriod")
    private String periodicPeriod; // from finalItem.periodicPeriod

    @JsonProperty("MarginType")
    private String marginType; // from finalItem.MarginType

    @JsonProperty("marginOperator")
    private String MarginOperator; // from finalItem.MarginOperator

    @JsonProperty("MarginRate")
    private String marginRate; // from finalItem.MarginRate

    @JsonProperty("tierAmount")
    private String tierAmount; // from finalItem.tierAmount

    @JsonProperty("MinRate")
    private String minRate; // from finalItem.MinRate

    @JsonProperty("MaxRate")
    private String maxRate; // from finalItem.MaxRate

    @JsonProperty("effectiveRate")
    private String effectiveRate; // from finalItem.effectiveRate


}
