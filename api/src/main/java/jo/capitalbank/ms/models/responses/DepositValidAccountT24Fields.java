package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DepositValidAccountT24Fields {



    @JsonProperty("ACCOUNT.NO")
    private String accountNumber;

    @JsonProperty("CATEGORY")
    private String category;

    @JsonProperty("CATEGORY.DESC")
    private String categoryDescription;

}
