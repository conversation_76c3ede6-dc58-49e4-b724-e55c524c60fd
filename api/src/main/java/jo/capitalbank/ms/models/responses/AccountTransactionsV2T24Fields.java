package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountTransactionsV2T24Fields {

    @JsonProperty("STMT.ID")
    private String statementId;

    @JsonProperty("BOOKING.DATE")
    private String bookingDate;

    @JsonProperty("VALUE.DATE")
    private String valueDate;

    @JsonProperty("TRANSACTION.REF")
    private String transactionReferenceNumber;

    @JsonProperty("TXN.CODE")
    private String transactionCode;

    @JsonProperty("CURRENCY")
    private String currency;

    @JsonProperty("TXN.AMOUNT")
    private String transactionAmount;

    @JsonProperty("TXN.MARKER")
    private String transactionIndicator;

    @JsonProperty("CHEQUE.NO")
    private String chequeNumber;

    @JsonProperty("NARRATIVE")
    private String statementNarrative;

    @JsonProperty("NARR.PARAM.OUTPUT")
    private String extendedNarrative;

    @JsonProperty("SWIFT.NARRATIVE")
    private String swiftNarrative;

    @JsonProperty("RUNNING.BALANCE")
    private String runningBalance;

    @JsonProperty("DATE.TIME")
    private String transactionDateTime;

    @JsonProperty("PAY.BRANCH.CODE")
    private String payBranchCode;

    @JsonProperty("PAY.ACC.NO")
    private String payAccountId;

    @JsonProperty("SESSION.DATE")
    private String sessionDate;

    @JsonProperty("TXN.NARR.AR")
    private String transactionCodeDescriptionAr;

    @JsonProperty("TXN.NARR")
    private String transactionCodeDescriptionEn;



}
