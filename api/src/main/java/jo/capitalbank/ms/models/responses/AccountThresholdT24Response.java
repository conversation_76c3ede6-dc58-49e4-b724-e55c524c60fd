package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountThresholdT24Response {



    @JsonProperty("PRODUCT.ID")
    private String prductId;

    @JsonProperty("CURRENCY")
    private String currency;

    @JsonProperty("MINIMUM.AMT")
    private String minimumAmount;

    @JsonProperty("MAXIMUM.AMT")
    private String maximumAmount;

    @JsonProperty("MINIMUM.TERM")
    private String minimumTerm;

    @JsonProperty("MAXIMUM.TERM")
    private String maximumTerm;

    @JsonProperty("INTEREST.PAY.FREQ")
    private String interestPaymentFrequency;







}
