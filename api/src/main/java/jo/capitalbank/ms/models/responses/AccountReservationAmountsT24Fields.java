package jo.capitalbank.ms.models.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountReservationAmountsT24Fields {


    @JsonProperty("TRANSACTION.REF")
    private String transactionReferance;

    @JsonProperty("ACCOUNT.NO")
    private String accountNumber;

    @JsonProperty("ACC.NAME")
    private String accountName;

    @JsonProperty("DESCRIPTION")
    private String description;

    @JsonProperty("FROM.DATE")
    private String fromDate;


    @JsonProperty("TO.DATE")
    private String toDate;

    @JsonProperty("LOCKED.AMOUNT")
    private String lockedAmount;

    @JsonProperty("AT.UNIQUE.ID")
    private String atmUniqueId;

    @JsonProperty("ATM.CARD.NO")
    private String atmCardNumber;

    @JsonProperty("ATM.MSG.IMD")
    private String atmMessageId;

    @JsonProperty("ATM.MERCHANT")
    private String atmMerchant;


}
