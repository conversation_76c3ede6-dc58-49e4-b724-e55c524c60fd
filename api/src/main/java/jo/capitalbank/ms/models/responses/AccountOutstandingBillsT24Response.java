package jo.capitalbank.ms.models.responses;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountOutstandingBillsT24Response {


    @JsonProperty("CUSTOMER.NO")
    private String customerId;

    @JsonProperty("CUS.NAME.1.2")
    private String customerName;

    @JsonProperty("CUSTOMER.POST.REST")
    private String postingRestriction;

    @JsonProperty("ACCOUNT.POST.REST")
    private String postingRestrictionAccount;

    @JsonProperty("ACC.TYPE")
    private String accountType;

    @JsonProperty("ACCOUNT.NO")
    private String accountId;

    @JsonProperty("availableLimitAmount")
    private String availableLimitAmount;

    @JsonProperty("CATEGORY")
    private String category;

    @JsonProperty("categoryDescription")
    private String categoryDescription;

    @JsonProperty("CURRENCY")
    private String currency;

    @JsonProperty("PRODUCT.ID")
    private String productId;

    @JsonProperty("Y.FINAL.OFFICER")
    private String finalOfficer;

    @JsonProperty("ACC.BAL")
    private String accountBalance;

    @JsonProperty("RESERVED.1")
    private String branch;

    @JsonProperty("COMPANY.CODE")
    private String companyCode;

    @JsonProperty("DORMANT.FLAG")
    private String dormantFlag;

    @JsonProperty("OVERDRAWN.FLAG")
    private String overdrawnFlag;

    @JsonProperty("OVERDRAWN.DATE")
    private String overdrawnDate;

    @JsonProperty("LEDGER.BALANCE")
    private String ledgerBalance;

    @JsonProperty("AVAILABLE.BALANCE")
    private String availableBalance;

    @JsonProperty("LOCKED.AMOUNT")
    private String lockedAmount;

    @JsonProperty("CUSTOMER.DAO")
    private String accountOfficer;

    @JsonProperty("CUSTOMER.DAO.NAME")
    private String customerOfficerName;

    @JsonProperty("ACCOUNT.DAO.NAME")
    private String accountOfficerName;

    @JsonProperty("NATIONAL.NO")
    private String nationalNumber;

    @JsonProperty("TARGET")
    private String target;

    @JsonProperty("MOBILE.NO")
    private String mobileNumber;

    @JsonProperty("EMAIL")
    private String email;

    @JsonProperty("CUSTOMER.NOTES")
    private String customerNotes;

    @JsonProperty("ACCOUNT.NOTES")
    private String accountNotes;

    @JsonProperty("ARR.ID")
    private String arrangementId;

    @JsonProperty("ACC.NUM")
    private String accountNumber;

    @JsonProperty("BILL.ID")
    private String billId;

    @JsonProperty("PROPERTY.NAME")
    private String propertyName;

    @JsonProperty("OS.AMT")
    private String outstandingAmount;

    @JsonProperty("BILL.DATE")
    private String billDate;


}
