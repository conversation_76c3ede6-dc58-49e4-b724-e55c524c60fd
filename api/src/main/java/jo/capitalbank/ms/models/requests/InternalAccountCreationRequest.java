package jo.capitalbank.ms.models.requests;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InternalAccountCreationRequest {


    private String action;
    private String limitRef;
    private MultilingualField shortName;
    private MultilingualField accountHolderName;
    private String customerId;
    private String currency;
    private String product;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MultilingualField {
        @JsonProperty("en")
        private String en;
    }
}
