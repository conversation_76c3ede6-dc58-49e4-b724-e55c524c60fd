package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.AccountReservationAmountsResponse;
import jo.capitalbank.ms.models.responses.AccountReservationAmountsT24Fields;
import jo.capitalbank.ms.services.AccountReservationAmountService;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountReservationAmountsMapper {




    List<AccountReservationAmountsResponse.AccountInfo> toList(List<AccountReservationAmountsT24Fields> source);



    default  AccountReservationAmountsResponse accounntReservationAmountsResponse(List<AccountReservationAmountsT24Fields> source) {
        var listOfReservations = toList(source);

        return AccountReservationAmountsResponse.builder()
                .accountsInfo(listOfReservations)
                .build();
    }

}
