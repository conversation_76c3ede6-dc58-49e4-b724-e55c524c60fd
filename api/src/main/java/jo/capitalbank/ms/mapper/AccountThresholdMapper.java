package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.AccountThresholdResponse;
import jo.capitalbank.ms.models.responses.AccountThresholdT24Response;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountThresholdMapper {



    List<AccountThresholdResponse.ProductInfo> toList(List<AccountThresholdT24Response> source);

    default  AccountThresholdResponse toAccountThresholdResponse(List<AccountThresholdT24Response> source) {
        var productInfo = toList(source);
        return AccountThresholdResponse.builder()
                .productInfo(productInfo)
                .build();
    }
}
