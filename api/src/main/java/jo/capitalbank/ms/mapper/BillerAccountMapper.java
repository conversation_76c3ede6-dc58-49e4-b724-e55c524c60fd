package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.BillerAccountResponse;
import jo.capitalbank.ms.models.responses.AccountBillerInfoT24Fields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface BillerAccountMapper {


    List<BillerAccountResponse.Account> toList(List<AccountBillerInfoT24Fields> source);


    default BillerAccountResponse toBillerAccountResponse(List<AccountBillerInfoT24Fields> source) {
        var accountsList = toList(source);
        var billerAccountResponse = BillerAccountResponse.builder()
                .billerAccounts(accountsList)
                .build();

        return billerAccountResponse;
    }
}
