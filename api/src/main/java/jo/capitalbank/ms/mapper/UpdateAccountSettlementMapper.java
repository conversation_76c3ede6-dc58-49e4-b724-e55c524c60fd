package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.UpdateSettlementAccountResponse;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface UpdateAccountSettlementMapper {


    default UpdateSettlementAccountResponse updateInternalAccountResponse(String trxId) {
        return UpdateSettlementAccountResponse.builder()
                .accounts(new UpdateSettlementAccountResponse.Account(trxId))
                .build();
    }
}
