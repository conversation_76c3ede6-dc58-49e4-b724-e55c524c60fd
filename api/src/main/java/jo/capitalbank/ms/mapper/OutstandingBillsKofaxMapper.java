package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.AccountOutstandingBilsKofaxResponse;
import jo.capitalbank.ms.models.responses.AccountOutstandingBillsT24Response;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OutstandingBillsKofaxMapper {


    List<AccountOutstandingBilsKofaxResponse.BillDetails> mapBills(List<AccountOutstandingBillsT24Response> source);

    default AccountOutstandingBilsKofaxResponse mapOutstandingBills(List<AccountOutstandingBillsT24Response> source) {
        return AccountOutstandingBilsKofaxResponse.builder()
                .bills(mapBills(source))
                .build();
    }


}
