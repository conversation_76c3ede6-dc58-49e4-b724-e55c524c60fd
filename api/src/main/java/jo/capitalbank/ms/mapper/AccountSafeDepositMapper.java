package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.AccountSafeDepositResponse;
import jo.capitalbank.ms.models.responses.AccountSafeDepositT24Fields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountSafeDepositMapper {


    List<AccountSafeDepositResponse.SafeDepositInfo> toListSafeDepositInfo(List<AccountSafeDepositT24Fields> source);

    default AccountSafeDepositResponse toAccountSafeDepositResponse(List<AccountSafeDepositT24Fields> source) {
        var safeDepositsList = toListSafeDepositInfo(source);
        return AccountSafeDepositResponse.builder()
                .safeDepositsInfo(safeDepositsList)
                .build();
    }
}
