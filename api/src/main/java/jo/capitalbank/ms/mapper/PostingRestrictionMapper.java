package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.AccountPostingRestrictionResponse;
import jo.capitalbank.ms.models.responses.AccountPostingRestricionT24Fields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface PostingRestrictionMapper {


    List<AccountPostingRestrictionResponse.PostingRestriction> map(List<AccountPostingRestricionT24Fields.PostingRestriction> list);


    // this customer mapping to handle the list
    default AccountPostingRestrictionResponse.Accounts mapToAccounts(List<AccountPostingRestricionT24Fields.PostingRestriction> list) {
        List<AccountPostingRestrictionResponse.PostingRestriction> mappedList = map(list);
        AccountPostingRestrictionResponse.Accounts accounts = new AccountPostingRestrictionResponse.Accounts();
        accounts.setPostingRestriction(mappedList);
        return accounts;
    }
}
