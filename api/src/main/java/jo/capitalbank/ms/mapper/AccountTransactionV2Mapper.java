package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.AccountTransactionsResponse;
import jo.capitalbank.ms.dto.AccountTransactionsV2Response;
import jo.capitalbank.ms.models.responses.AccountTransactionsT24Fields;
import jo.capitalbank.ms.models.responses.AccountTransactionsV2T24Fields;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;


@Mapper(componentModel = "spring")
public interface AccountTransactionV2Mapper {

    @Mappings({
            @Mapping(source = "transactionCodeDescriptionAr", target = "transactionCodeDescriptionAr"),
            @Mapping(source = "transactionCodeDescriptionEn", target = "transactionCodeDescriptionEn")
    })
    AccountTransactionsV2Response.Transaction toTransaction(AccountTransactionsV2T24Fields t24);

    List<AccountTransactionsV2Response.Transaction> toTransactionList(List<AccountTransactionsV2T24Fields> t24List);


    default AccountTransactionsV2Response toResponse(List<AccountTransactionsV2T24Fields> t24List) {
        List<AccountTransactionsV2Response.Transaction> transactionList = toTransactionList(t24List);
        AccountTransactionsV2Response.Accounts accounts = AccountTransactionsV2Response.Accounts.builder()
                .transactionList(transactionList)
                .build();
        return AccountTransactionsV2Response.builder()
                .accounts(accounts)
                .build();
    }
}
