package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.AccountByLimitResponse;
import jo.capitalbank.ms.models.responses.AccountByLimitsT24Fields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountByLimitMapper {
    List<AccountByLimitResponse.Account> accountByLimitMapper(List<AccountByLimitsT24Fields> list);

    default AccountByLimitResponse mapToAccountsList(List<AccountByLimitsT24Fields> list){
        return AccountByLimitResponse.builder().accounts(accountByLimitMapper(list)).build();
    }
}
