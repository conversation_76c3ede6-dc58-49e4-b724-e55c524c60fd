package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.DepositValidAccountResponse;
import jo.capitalbank.ms.models.responses.DepositValidAccountT24Fields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface DepositValidAccountMapper {




    List<DepositValidAccountResponse.AccountInfo> toList(List<DepositValidAccountT24Fields> source);

    default DepositValidAccountResponse toDepositValidAccountResponse(List<DepositValidAccountT24Fields> source) {
        var listOfAccounts = toList(source);
        return DepositValidAccountResponse.builder()
                .accountsInfo(listOfAccounts)
                .build();
    }

}
