package jo.capitalbank.ms.mapper;

import com.fasterxml.jackson.annotation.JsonInclude;
import jo.capitalbank.ms.dto.AccountDetailsResponse;
import jo.capitalbank.ms.dto.AccountDetailsResponseKofax;
import jo.capitalbank.ms.models.responses.AccountDetailsT24ResponseFields;
import jo.capitalbank.ms.models.responses.AccountDetailsT24ResponseFiledsBpm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountInfoMapper {




    @Mapping(source = "accountTitle",target = "accountHolderName.en")
    @Mapping(source = "accountTitleArabic",target = "accountHolderName.ar")
    @Mapping(target = "postingRestriction",expression = "java(mapAccountPostingRestriction(source))")
    @Mapping(target = "productId",source = "product" )
    AccountDetailsResponse.Accounts mapAccountT24ToAccountDto(AccountDetailsT24ResponseFields source);

    @Mapping(target = "customerName",expression = "java(map(source.getCustomerNameEnglish(),source.getCustomerNameArabic()))")
    @Mapping(
            target = "postingRestriction",
            expression = "java(mapPostingRestrictions(source.getCustomerPostingRestrictCode(), source.getCustomerPostingRestrictType(), source.getCustomerPostingRestrictTypeDesc()))"
    )
    AccountDetailsResponse.Customers mapCustomerT24ToCustomerDto(AccountDetailsT24ResponseFields source);

    @Mapping(source = "customId",target ="customerId")
    @Mapping(source = "code",target = "branchCode")
    @Mapping(source = "dob",target = "dateOfBirth")
    @Mapping(source = "availableBalanceAmount",target = "availableAmountBalance")
    AccountDetailsResponseKofax mapKofaxToAccountDto(AccountDetailsT24ResponseFields source);

    @Mapping(source = "stmtFreqBpm",target = "statementFrequency")
    @Mapping(source = "categoryDesc",target = "categoryDescription")
    @Mapping(target = "postingRestriction",expression = "java(mapAccountPostingRestrictionBpm(source))")
    AccountDetailsResponse.Accounts mapAccountBpmToAccountDto(AccountDetailsT24ResponseFiledsBpm source);

    @Mapping(source = "customerIdBpm",target = "customerId")
    @Mapping(target = "customerName",expression = "java(map(source.getCustomerNameEn(),source.getCustomerNameAr()))")
    @Mapping(
            target = "postingRestriction",
            expression = "java(mapPostingRestrictions(source.getPostingRestrictCode(), source.getPostingRestrictTyp(), source.getPostingRestrictDesc()))"
    )
    AccountDetailsResponse.Customers mapCustomerBpmToAccountDto(AccountDetailsT24ResponseFiledsBpm source);



    default AccountDetailsResponse.Name map(String value,String value2) {
        if (value == null) {
            return null;
        }
        return AccountDetailsResponse.Name.builder()
                .en(value)
                .ar(value2)
                .build();
    }


    default List<AccountDetailsResponse.PostingRestriction> mapPostingRestrictions(
            String codes,
            String types,
            String typeDescriptions) {

        if (codes == null || codes.isEmpty()) {
            return Collections.emptyList();
        }

        String[] codeArr = codes.split("\\*");
        String[] typeArr = types != null ? types.split("\\*") : new String[0];
        String[] descArr = typeDescriptions != null ? typeDescriptions.split("\\*") : new String[0];

        List<AccountDetailsResponse.PostingRestriction> list = new ArrayList<>();
        for (int i = 0; i < codeArr.length; i++) {
            String code = codeArr[i];
            String type = i < typeArr.length ? typeArr[i] : (typeArr.length > 0 ? typeArr[0] : null);
            String desc = i < descArr.length ? descArr[i] : (descArr.length > 0 ? descArr[0] : null);

            list.add(AccountDetailsResponse.PostingRestriction.builder()
                    .code(code)
                    .type(type)
                    .typeDescription(desc)
                    .description("")   // matches your DataWeave default ""
                    .build());
        }
        return list;
    }

    default List<AccountDetailsResponse.PostingRestrictionAccount> mapAccountPostingRestrictionBpm(AccountDetailsT24ResponseFiledsBpm source) {
        if (source == null) {
            return null;
        }
        var postingRestrictionList = new ArrayList<AccountDetailsResponse.PostingRestrictionAccount>();

        var accountPosting =new AccountDetailsResponse.PostingRestrictionAccount();
        if(source.getPostingRestrict() != null) {
            accountPosting.setCode(source.getPostingRestrictCode());
        }
        if(source.getPostResType() != null) {
            accountPosting.setType(source.getPostResType());
        }
        if (source.getPostResDesc() != null) {
            accountPosting.setDescription(source.getPostResDesc());
        }

        postingRestrictionList.add(accountPosting);
        return postingRestrictionList;


    }

    default List<AccountDetailsResponse.PostingRestrictionAccount> mapAccountPostingRestriction(AccountDetailsT24ResponseFields source) {
        if (source == null) {
            return null;
        }
        var postingRestrictionList = new ArrayList<AccountDetailsResponse.PostingRestrictionAccount>();
        var accountPosting =new AccountDetailsResponse.PostingRestrictionAccount();
        if(source.getPostingRestrict() != null) {
            accountPosting.setCode(source.getPostingRestrictCode());
        }
        if(source.getPostResType() != null) {
            accountPosting.setType(source.getPostResType());
        }
        if (source.getPostResDesc() != null) {
            accountPosting.setDescription(source.getPostResDesc());
        }

        postingRestrictionList.add(accountPosting);
        return postingRestrictionList;


    }

}
