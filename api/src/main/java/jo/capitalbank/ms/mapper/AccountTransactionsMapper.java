package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.AccountByLimitResponse;
import jo.capitalbank.ms.dto.AccountTransactionsResponse;
import jo.capitalbank.ms.models.responses.AccountByLimitsT24Fields;
import jo.capitalbank.ms.models.responses.AccountTransactionsT24Fields;
import jo.capitalbank.ms.models.responses.AccountTransactionsV2T24Fields;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountTransactionsMapper {

    @Mappings({
            @Mapping(source = "transactionCodeDescriptionAr", target = "transactionCodeDescriptionAr"),
            @Mapping(source = "transactionCodeDescriptionEn", target = "transactionCodeDescriptionEn")
    })
    AccountTransactionsResponse.Transaction toTransaction(AccountTransactionsT24Fields t24);

    List<AccountTransactionsResponse.Transaction> toTransactionList(List<AccountTransactionsT24Fields> t24List);

    default AccountTransactionsResponse toResponse(List<AccountTransactionsT24Fields> t24List) {
        List<AccountTransactionsResponse.Transaction> transactionList = toTransactionList(t24List);
        AccountTransactionsResponse.Accounts accounts = AccountTransactionsResponse.Accounts.builder()
                .transactionList(transactionList)
                .build();
        return AccountTransactionsResponse.builder()
                .accounts(accounts)
                .build();
    }

}
