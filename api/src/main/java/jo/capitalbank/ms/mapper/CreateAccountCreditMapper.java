package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.CreateCreditAccountResponse;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface CreateAccountCreditMapper {
    
    
    default CreateCreditAccountResponse createCreditAccountResponse(String trxRef) {
        return CreateCreditAccountResponse.builder()
                .accountId(trxRef)
                .build();
    }
}
