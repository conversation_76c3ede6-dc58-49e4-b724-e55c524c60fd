package jo.capitalbank.ms.mapper;


import jo.capitalbank.ms.dto.AccountDormantResponse;
import jo.capitalbank.ms.models.responses.AccountDormantT24Fields;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface AccountDormantMapper {


    AccountDormantResponse.Account toAccount(AccountDormantT24Fields accountDormantT24Fields);

    default AccountDormantResponse mappAccountDormantResponse(AccountDormantT24Fields accountDormantT24Fields) {
        var accountDormantDetails = toAccount(accountDormantT24Fields);
        return AccountDormantResponse.builder()
                .dormantAccountDetails(accountDormantDetails)
                .build();
    }
}
