package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.AccountOutstandingBillsResponse;
import jo.capitalbank.ms.models.responses.AccountOutstandingBillsT24Response;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountOutstandingBillsMapper {

    List<AccountOutstandingBillsResponse.AccountDetails> mapBillsList(List<AccountOutstandingBillsT24Response> source);

    @Mapping(target = "customers", expression = "java(mapCustomer(source))")
    @Mapping(target = "postingRestriction", expression = "java(map(source.getPostingRestrictionAccount()))")
    AccountOutstandingBillsResponse.AccountDetails mapSingle(AccountOutstandingBillsT24Response source);

    default AccountOutstandingBillsResponse.Customers mapCustomer(AccountOutstandingBillsT24Response source) {
        return AccountOutstandingBillsResponse.Customers.builder()
                .customerId(source.getCustomerId())
                .customerName(
                        AccountOutstandingBillsResponse.Name.builder()
                                .en(source.getCustomerName())
                                .build()
                )
                .postingRestriction(
                        AccountOutstandingBillsResponse.PostingRestriction.builder()
                                .code(source.getPostingRestrictionAccount())
                                .build()
                )
                .build();
    }

    default AccountOutstandingBillsResponse.PostingRestriction map(String value) {
        if (value == null) return null;
        return AccountOutstandingBillsResponse.PostingRestriction.builder()
                .code(value)
                .build();
    }

    default AccountOutstandingBillsResponse mapAccounts(List<AccountOutstandingBillsT24Response> source) {
        return AccountOutstandingBillsResponse
                .builder()
                .accounts(mapBillsList(source))
                .build();
    }
}
