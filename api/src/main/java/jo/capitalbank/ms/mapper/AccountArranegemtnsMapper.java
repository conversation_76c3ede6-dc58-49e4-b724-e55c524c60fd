package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.AccountArrangementsResponse;
import jo.capitalbank.ms.models.responses.AccountArrangementsT24Fields;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AccountArranegemtnsMapper {

    List<AccountArrangementsResponse.Arrangements> toList(List<AccountArrangementsT24Fields> t24Fields);

    default AccountArrangementsResponse accountArrangementsMapper(List<AccountArrangementsT24Fields> t24Fields) {
        var list = toList(t24Fields);
        var accountArrangementsResponse = AccountArrangementsResponse.builder()
                .linkedArrangements(list)
                .build();

        return accountArrangementsResponse;
    }
}
