package jo.capitalbank.ms.mapper;

import jo.capitalbank.ms.dto.AccountStatementFccResponse;
import jo.capitalbank.ms.dto.AcctStmtBookResponse;
import org.mapstruct.Mapper;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface AccountStatementMapperFcc {

    default AccountStatementFccResponse map(AcctStmtBookResponse response) {
        if (response == null || response.getAccountStatement() == null) {
            return null;
        }

        // Map account info
        AccountStatementFccResponse.AccountInfo accountInfo = AccountStatementFccResponse.AccountInfo.builder()
                .accountCurrency(response.getAccountStatement().getAcctCurrency())
                .openingBalanceDate(response.getAccountStatement().getOpenBalDate())
                .openingBalanceAmount(response.getAccountStatement().getOpenBalAmt())
                .clearedBalanceDate(response.getAccountStatement().getClBalDate())
                .clearedBalance(response.getAccountStatement().getClBalAmt())
                .noOfDebits(response.getAccountStatement().getNoOfDebits())
                .totalDebitAmount(response.getAccountStatement().getTotDbAmt())
                .noOfCredit(response.getAccountStatement().getNoOfCredits())
                .totalCreditAmount(response.getAccountStatement().getTotalCr())
                .openBalanceMarker(response.getAccountStatement().getOpenBalMarker())
                .closingBalanceMarker(response.getAccountStatement().getClBalMarker())
                .build();

        // Map transactions
        List<AccountStatementFccResponse.Transaction> transactions =
                response.getAccountStatement().getTransactions().getFirst().getTimestamp() == "" && response.getAccountStatement().getTransactions().size() ==1
                        ? Collections.emptyList()
                        : response.getAccountStatement().getTransactions().stream()
                        .map(tx -> AccountStatementFccResponse.Transaction.builder()
                                .transactionType(tx.getTxnType())
                                .valueDate(tx.getValueDate())
                                .postDate(tx.getPostDate())
                                .transactionAmount(tx.getTransAmt())
                                .transactionDescription(splitDescription(tx.getTransDesc()))
                                .transactionReference(tx.getTransRef())
                                .timestamp(tx.getTimestamp())
                                .marker(tx.getMarker())
                                .transactionCode(tx.getTransactionCode())
                                .build()
                        )
                        .collect(Collectors.toList());

        // Build final DTO
        if(transactions.isEmpty()){
            return AccountStatementFccResponse.builder()
                    .accountInfo(accountInfo)
                    .build();
        }
        return AccountStatementFccResponse.builder()
                .accountInfo(accountInfo)
                .transactionList(transactions)
                .build();
    }

    private static List<String> splitDescription(String desc) {
        if (desc == null || desc.isBlank()) {
            return Collections.emptyList();
        }
        // split on "|" like in your XML examples
        return Arrays.stream(desc.split("\\|"))
                .map(String::trim)
                .collect(Collectors.toList());
    }
}
