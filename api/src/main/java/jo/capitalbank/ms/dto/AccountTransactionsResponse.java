package jo.capitalbank.ms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountTransactionsResponse {

    private Accounts accounts;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Accounts {
        private List<Transaction> transactionList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Transaction {

        private String accountId;
        private String bookingDate;
        private String customerId;
        private String currency;
        private String valueDate;
        private String transactionReferenceNumber;
        private String transactionCode;
        private String crfType;
        private String amountLcy;
        private String amountFcy;
        private String beginBalance;
        private String runningBalance;
        private String transactionCount;

        @JsonProperty("transactionCodeDescription.ar")
        private String transactionCodeDescriptionAr;

        @JsonProperty("transactionCodeDescription.en")
        private String transactionCodeDescriptionEn;
    }
}
