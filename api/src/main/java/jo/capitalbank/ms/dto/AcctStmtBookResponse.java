package jo.capitalbank.ms.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AcctStmtBookResponse {

    @JacksonXmlProperty(localName = "Status")
    private Status status;

    @JacksonXmlProperty(localName = "NOFILECBOJSTMTBOOKSType")
    private AccountStatement accountStatement;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Status {
        @JacksonXmlProperty(localName = "successIndicator")
        private String successIndicator;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AccountStatement {
        @JacksonXmlProperty(localName = "ACCTCURRENCY")
        private String acctCurrency;

        @JacksonXmlProperty(localName = "OPENBALMARKER")
        private String openBalMarker;

        @JacksonXmlProperty(localName = "OPENBALDATE")
        private String openBalDate;

        @JacksonXmlProperty(localName = "OPENBALAMT")
        private String openBalAmt;

        @JacksonXmlProperty(localName = "CLBALMARKER")
        private String clBalMarker;

        @JacksonXmlProperty(localName = "CLBALDATE")
        private String clBalDate;

        @JacksonXmlProperty(localName = "CLBALAMT")
        private String clBalAmt;

        @JacksonXmlProperty(localName = "NOOFDEBITS")
        private String noOfDebits;

        @JacksonXmlProperty(localName = "TOTDBAMT")
        private String totDbAmt;

        @JacksonXmlProperty(localName = "NOOFCREDITS")
        private String noOfCredits;

        @JacksonXmlProperty(localName = "TOTALCR")
        private String totalCr;

        @JacksonXmlElementWrapper(localName = "gNOFILECBOJSTMTBOOKSDetailType")
        @JacksonXmlProperty(localName = "mNOFILECBOJSTMTBOOKSDetailType")
        private List<Transaction> transactions;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Transaction {
        @JacksonXmlProperty(localName = "TXNTYPE")
        private String txnType;

        @JacksonXmlProperty(localName = "VALUEDATE")
        private String valueDate;

        @JacksonXmlProperty(localName = "POSTDATE")
        private String postDate;

        @JacksonXmlProperty(localName = "MARKER")
        private String marker;

        @JacksonXmlProperty(localName = "TRANSAMT")
        private String transAmt;

        @JacksonXmlProperty(localName = "TRANSDESC")
        private String transDesc;

        @JacksonXmlProperty(localName = "TRANSREF")
        private String transRef;

        @JacksonXmlProperty(localName = "TIMESTAMP")
        private String timestamp;

        @JacksonXmlProperty(localName = "PAYBANKCODE")
        private String payBankCode;

        @JacksonXmlProperty(localName = "PAYBRCODE")
        private String payBrCode;

        @JacksonXmlProperty(localName = "CHEQUENO")
        private String chequeNo;

        @JacksonXmlProperty(localName = "PAYACCOUNT")
        private String payAccount;

        @JacksonXmlProperty(localName = "SESSIONDATE")
        private String sessionDate;

        @JacksonXmlProperty(localName = "transactionCode")
        private String transactionCode;
    }
}

