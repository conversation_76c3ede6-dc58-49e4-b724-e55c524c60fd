package jo.capitalbank.ms.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountDetailsResponse {

    private Customers customers;
    private Accounts accounts;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Customers {
        private String customerId;
        private Name customerName;
        private List<PostingRestriction> postingRestriction;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Accounts {
        private String accountInterestRate;
        private String accountNickName;
        private Name accountHolderName;
        private String accountType;
        private String creditInterestRate;
        private String accruedCRInterest;
        private String accruedDRInterest;
        private String accountId;
        private String availableLimitAmount;
        private String availableBalance;
        private String branch;
        private String category;
        private String categoryDescription;
        private String clearedBalance;
        private String currency;
        private String accountOfficer;
        private String debitInterestRate;
        private String iban;
        private String lockedAmount;
        private String onlineActualBalance;
        private String openingDate;
        private String productId;
        private String statementFrequency;
        private String status;
        private String target;
        private String workingBalance;
        private String branchEnglish;
        private String branchArabic;
        private String companyCode;
        private String isDormant;
        private String limitExpiryDate;
        private String availableBalanceLimit;
        private String lockAmount;
        private String overdrawnFlag;
        private  String exceedLimitFlag;
        private String onlineLimit;
        private String targetDescription;
        private String companyName;
        private String faxIndemnity;
        private String accountTitle;
        private List<PostingRestrictionAccount> postingRestriction;




    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Name {
        private String en;
        private String ar;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PostingRestriction{

        private String code;
        private String type;
        private String typeDescription;
        private String description;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.ALWAYS)
    public static class PostingRestrictionAccount {
        private String code;
        private String type;
        private String description;
    }


}
