package jo.capitalbank.ms.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountPostingRestrictionResponse {


    private Accounts accounts;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Accounts {
        private List<PostingRestriction> postingRestriction;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PostingRestriction {
        private String typeDescription;
        private String type;
        private String code;
        private String description;
        private String narrative;
    }

}
