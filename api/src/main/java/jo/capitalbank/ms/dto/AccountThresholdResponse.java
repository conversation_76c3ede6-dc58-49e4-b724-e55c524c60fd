package jo.capitalbank.ms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jo.capitalbank.ms.models.responses.AccountThresholdT24Response;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountThresholdResponse {


    private List<ProductInfo> productInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ProductInfo {
        private String prductId;

        private String currency;

        private String minimumAmount;

        private String maximumAmount;

        private String minimumTerm;

        private String maximumTerm;

        private String interestPaymentFrequency;
    }

}
