package jo.capitalbank.ms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountIntrestResponse {


    private String productName; // from finalItem.ProductName

    private String currency; // from finalItem.Currency

    private String arrangementId; // from finalItem.arrangementId

    private String fixedRate; // from finalItem.fixedRate

    private String biKey; // from finalItem.biKey

    private String piKey; // from finalItem.piKey

    private String periodicPeriod; // from finalItem.periodicPeriod

    private String marginType; // from finalItem.MarginType

    private String MarginOperator; // from finalItem.MarginOperator

    private String marginRate; // from finalItem.MarginRate

    private String tierAmount; // from finalItem.tierAmount

    private String minRate; // from finalItem.MinRate

    private String maxRate; // from finalItem.MaxRate

    private String effectiveRate; // fr

}
