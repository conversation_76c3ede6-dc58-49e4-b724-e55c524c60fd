package jo.capitalbank.ms.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountSafeDepositResponse {


    private List<SafeDepositInfo> safeDepositsInfo;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SafeDepositInfo {
        private String accountNumber;
        private String productType;
        private String boxNumber;

        private String boxType;

        private String principalAmount;
        private String outstandingAmount;


    }
}
