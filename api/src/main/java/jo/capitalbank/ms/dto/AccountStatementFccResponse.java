package jo.capitalbank.ms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountStatementFccResponse {

    @JsonProperty("accountInfo")
    private AccountInfo accountInfo;

    @JsonProperty("transactionList")
    private List<Transaction> transactionList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AccountInfo {
        @JsonProperty("accountCurrency")
        private String accountCurrency;

        @JsonProperty("openingBalanceDate")
        private String openingBalanceDate;

        @JsonProperty("openingBalanceAmount")
        private String openingBalanceAmount;

        @JsonProperty("clearedBalanceDate")
        private String clearedBalanceDate;

        @JsonProperty("clearedBalance")
        private String clearedBalance;

        @JsonProperty("noOfDebits")
        private String noOfDebits;

        @JsonProperty("totalDebitAmount")
        private String totalDebitAmount;

        @JsonProperty("noOfCredit")
        private String noOfCredit;

        @JsonProperty("totalCreditAmount")
        private String totalCreditAmount;

        @JsonProperty("openBalanceMarker")
        private String openBalanceMarker;

        @JsonProperty("closingBalanceMarker")
        private String closingBalanceMarker;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Transaction {
        @JsonProperty("transactionType")
        private String transactionType;

        @JsonProperty("valueDate")
        private String valueDate;

        @JsonProperty("postDate")
        private String postDate;

        @JsonProperty("transactionAmount")
        private String transactionAmount;

        @JsonProperty("transactionDescription")
        private List<String> transactionDescription;

        @JsonProperty("transactionReference")
        private String transactionReference;

        @JsonProperty("timestamp ")
        private String timestamp; // ⚠️ note the space in the key

        @JsonProperty("marker")
        private String marker;
        @JsonProperty("transactionCode")
        private String transactionCode;
    }
}
