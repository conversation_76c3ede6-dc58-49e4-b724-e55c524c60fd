package jo.capitalbank.ms.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BillerAccountResponse {


    private List<Account> billerAccounts;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Account{
        private String accountId;
        private String issueDate;
        private String billerStatus;
        private String serviceType;
        private String madfoatComAccount;
        private String billerId;
        private String defaultFlag;
        private String englishName;
    }
}
