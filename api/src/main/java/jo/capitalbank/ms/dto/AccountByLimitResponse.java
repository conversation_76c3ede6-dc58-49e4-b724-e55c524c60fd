package jo.capitalbank.ms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountByLimitResponse {

    private List<Account> accounts;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Account {
        private String accountId;
        private String currency;
        private String limitMaturityDate;
        private String limitAmount;
        private String internalAmount;
        private String company;
    }
}
