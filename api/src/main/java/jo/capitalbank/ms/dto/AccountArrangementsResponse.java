package jo.capitalbank.ms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountArrangementsResponse {


    private List<Arrangements> linkedArrangements;



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Arrangements{
        private String accountNumber;

        private String linkedArrangementId;

        private String linkType;

        private String linkDate;

        private String productLine;

        private String productId;

        private String balanceAmount;
    }
}
