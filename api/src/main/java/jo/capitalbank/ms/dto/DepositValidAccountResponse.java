package jo.capitalbank.ms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DepositValidAccountResponse {



    private List<AccountInfo> accountsInfo;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AccountInfo{
        private String accountNumber;
        private String category;
        private String categoryDescription;
    }
}
