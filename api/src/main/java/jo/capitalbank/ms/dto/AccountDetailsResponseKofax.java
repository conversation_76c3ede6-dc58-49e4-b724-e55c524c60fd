package jo.capitalbank.ms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountDetailsResponseKofax {

    private String customerName;
    private String customerId;
    private String target;
    private String branchCode;
    private String mobileNumber;
    private String currency;
    private String nationalNumber;
    private String nationality;
    private String residence;
    private String gender;
    private String customerSatus;
    private String fax;
    private String email;
    private String dateOfBirth;
    private String segment;
    private String riskRating;
    private String availableAmountBalance;
    private String ledgerBalance;

}
