package jo.capitalbank.ms.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountOutstandingBillsResponse {

    private List<AccountDetails> accounts;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AccountDetails {
        private Customers customers;
        private PostingRestriction postingRestriction;
        private String accountType;
        private String accountId;
        private String availableLimitAmount;
        private String availableBalance;
        private String category;
        private String categoryDescription;
        private String currency;
        private String productId;
        private String finalOfficer;
        private String accountBalance;
        private String branch;
        private String dormantFlag;
        private String overdrawnFlag;
        private String overdrawnDate;
        private String ledgerBalance;
        private String lockedAmount;
        private String accountOfficer;
        private String customerOfficerName;
        private String accountOfficerName;
        private String nationalNumber;
        private String target;
        private String mobileNumber;
        private String email;
        private String customerNotes;
        private String accountNotes;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Customers {
        private String customerId;
        private Name customerName;
        private PostingRestriction postingRestriction;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Name {
        private String en;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PostingRestriction {
        private String code;
    }
}
