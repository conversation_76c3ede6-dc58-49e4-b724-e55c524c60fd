package jo.capitalbank.ms.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountDormantResponse {


    private Account dormantAccountDetails;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Account {

        private String accountBranch;

        private String customerNo;

        private String customerName;

        private String accountNumber;

        private String finalDescription;

        private String finalOfficer;

        private String currency;

        private String accountBalance;

        private String lastTxnDate;

        private String accountStatus;

        private String statusDate;

        private String productId;

        private String customerPostRest;

        private String dormantFlag;

        private String overDrawnFlag;

        private String ledgerBalance;

        private String availableBalance;

        private String lockedAmount;

        private String customerDepartmentAccountOfficer;

        private String customerDAOName;

        private String accountDAOName;

        private String target;

        private String mobileNo;


        private String email;

    }
}
