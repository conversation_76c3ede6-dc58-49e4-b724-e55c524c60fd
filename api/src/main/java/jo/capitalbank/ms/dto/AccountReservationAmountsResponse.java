package jo.capitalbank.ms.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountReservationAmountsResponse {



    private List<AccountInfo> accountsInfo;



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AccountInfo{
        private String transactionReferance;
        private String accountNumber;
        private String description;
        private String fromDate;
        private String toDate;
        private String lockedAmount;
    }



}
