package jo.capitalbank.ms.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccountOutstandingBilsKofaxResponse {


    private List<BillDetails> bills;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class BillDetails{
        private String arrangementId;
        private String accountNumber;
        private String billId;
        private String propertyName;
        private String outstandingAmount;
        private String billDate;

    }
}
