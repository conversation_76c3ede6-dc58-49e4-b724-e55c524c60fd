package jo.capitalbank.ms.utils;


import jo.capitalbank.ms.exception.ErrorCodes;
import jo.capitalbank.ms.exception.T24BusinessException;
import jo.capitalbank.ms.models.responses.T24GenericResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class T24FieldsExtractor {



    public ArrayList<HashMap<String, String>> t24FieldsExtractor(List<T24GenericResponse.Record> t24Fields) {
        var listofFields = new ArrayList<HashMap<String, String>>(t24Fields.size());
        if (t24Fields.isEmpty()) {

            throw new T24BusinessException("No Records Found", "T24003790",null);


        }
        t24Fields.forEach(field -> {
            var mapResponse = new HashMap<String, String>();
            field.getFields().forEach(f -> {

                mapResponse.put(f.getT24field().getName().split(":")[0], f.getT24field().getValue());
            });
            listofFields.add(mapResponse);

        });

        return listofFields;
    }
}
