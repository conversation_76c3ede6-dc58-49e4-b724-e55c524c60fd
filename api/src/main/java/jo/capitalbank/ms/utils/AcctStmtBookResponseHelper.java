package jo.capitalbank.ms.utils;

import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import jo.capitalbank.ms.dto.AcctStmtBookResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringReader;
import java.io.StringWriter;

@Slf4j
@Component
public class AcctStmtBookResponseHelper {

    private final XmlMapper xmlMapper = new XmlMapper();

    public AcctStmtBookResponse parse(String xmlResponse) {
        try {
            // Parse SOAP Envelope into DOM
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.parse(new InputSource(new StringReader(xmlResponse)));

            // Extract only the <GetAcctStmtBookResponse> node
            NodeList list = doc.getElementsByTagNameNS("http://temenos.com/CBOJSTMTV3", "GetAcctStmtBookResponse");
            if (list.getLength() == 0) {
                throw new RuntimeException("No GetAcctStmtBookResponse found in SOAP XML");
            }

            String innerXml = nodeToString(list.item(0));

            // Unwrap namespaces and map directly into DTO
            String cleanedXml = innerXml
                    .replace("ns3:", "")
                    .replace("ns2:", "")
                    .replaceAll("(<\\/?)(\\w+:)", "$1"); // generic namespace stripper

            log.debug("Cleaned XML to parse: {}", cleanedXml);

            // Deserialize into DTO
            AcctStmtBookResponse response = xmlMapper.readValue(cleanedXml, AcctStmtBookResponse.class);



            return response;

        } catch (Exception e) {
            throw new RuntimeException("Failed to parse SOAP XML", e);
        }
    }

    private String nodeToString(Node node) throws Exception {
        Transformer transformer = TransformerFactory.newInstance().newTransformer();
        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(node), new StreamResult(writer));
        return writer.toString();
    }
}
