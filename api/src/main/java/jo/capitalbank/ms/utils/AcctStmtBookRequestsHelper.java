package jo.capitalbank.ms.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AcctStmtBookRequestsHelper {

    public String generateAcctStmtBookXml(String id, String fromDate, String toDate) {
        return """
                <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:cboj="http://temenos.com/CBOJSTMTV3">
                   <soapenv:Header/>
                   <soapenv:Body>
                      <cboj:GetAcctStmtBook>
                         <WebRequestCommon>
                            <company></company>
                            <password>CAPITAL@0722</password>
                            <userName>TS_FCC_STMT</userName>
                         </WebRequestCommon>
                         <NOFILECBOJSTMTBOOKSType>
                            <enquiryInputCollection>
                               <columnName>@ID</columnName>
                               <criteriaValue>%s</criteriaValue>
                               <operand>EQ</operand>
                            </enquiryInputCollection>
                            <enquiryInputCollection>
                               <columnName>FROM.DATE</columnName>
                               <criteriaValue>%s</criteriaValue>
                               <operand>GE</operand>
                            </enquiryInputCollection>
                            <enquiryInputCollection>
                               <columnName>TO.DATE</columnName>
                               <criteriaValue>%s</criteriaValue>
                               <operand>LE</operand>
                            </enquiryInputCollection>
                         </NOFILECBOJSTMTBOOKSType>
                      </cboj:GetAcctStmtBook>
                   </soapenv:Body>
                </soapenv:Envelope>
                """.formatted(id, fromDate, toDate);
    }
}
